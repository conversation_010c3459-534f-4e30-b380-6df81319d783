[workspace]
resolver = "2"
members = [
    "apps/desktop/src-tauri",
    # "packages/services/rust-service",  # 可选的 Rust 微服务 (暂时注释掉)
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["imeepos <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/imeepos/mixvideo"

[workspace.dependencies]
# Tauri 相关依赖
tauri = { version = "2.0", features = ["api-all"] }
tauri-build = { version = "2.0", features = [] }

# gRPC 相关依赖
tonic = "0.10"
prost = "0.12"
tokio = { version = "1.0", features = ["full"] }
tokio-stream = "0.1"

# 序列化和配置
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# 日志和错误处理
tracing = "0.1"
tracing-subscriber = "0.3"
anyhow = "1.0"
thiserror = "1.0"

# 异步和并发
futures = "0.3"
async-trait = "0.1"

# 数据库 (可选)
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }

[workspace.dependencies.tonic-build]
version = "0.10"