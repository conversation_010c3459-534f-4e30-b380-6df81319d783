# Hotfix 测试指南：修复 FFmpeg 命令行闪现问题

## 🔧 问题描述

在之前的版本中，当 MixVideo Desktop 执行 FFmpeg 和 FFprobe 命令时，会在 Windows 上出现命令行窗口闪现的问题，影响用户体验。

## 🛠️ 修复方案

### 技术实现

1. **添加 Windows 特定导入**
   ```rust
   #[cfg(target_os = "windows")]
   use std::os::windows::process::CommandExt;
   ```

2. **创建隐藏控制台的命令函数**
   ```rust
   fn create_hidden_command(program: &str) -> Command {
       let mut cmd = Command::new(program);
       
       #[cfg(target_os = "windows")]
       {
           // 在 Windows 上隐藏控制台窗口
           // CREATE_NO_WINDOW = 0x08000000
           cmd.creation_flags(0x08000000);
       }
       
       cmd
   }
   ```

3. **替换所有 FFmpeg/FFprobe 调用**
   - 将所有 `Command::new("ffmpeg")` 替换为 `Self::create_hidden_command("ffmpeg")`
   - 将所有 `Command::new("ffprobe")` 替换为 `Self::create_hidden_command("ffprobe")`

### 修复范围

修复了以下方法中的命令行闪现问题：

- ✅ `is_available()` - FFmpeg 可用性检查
- ✅ `get_status_info()` - FFmpeg 状态信息获取
- ✅ `extract_metadata()` - 视频/音频元数据提取
- ✅ `detect_scenes_with_ffmpeg()` - 场景检测
- ✅ `detect_scenes_alternative()` - 替代场景检测
- ✅ `split_video_with_mode()` - 视频切分
- ✅ `get_keyframes()` - 关键帧获取
- ✅ `generate_thumbnail()` - 缩略图生成
- ✅ `get_version()` - 版本信息获取

## 🧪 测试步骤

### 1. 启动应用测试
```bash
cd apps/desktop
pnpm tauri:dev
```

**预期结果**: 应用启动时不应出现任何命令行窗口闪现

### 2. FFmpeg 状态检查测试
1. 打开应用
2. 查看 FFmpeg 状态信息（如果有相关界面）

**预期结果**: 检查 FFmpeg 状态时不应出现命令行闪现

### 3. 视频文件导入测试
1. 创建或打开一个项目
2. 导入一个视频文件
3. 观察元数据提取过程

**预期结果**: 
- 视频元数据正常提取
- 过程中不出现命令行窗口闪现
- 控制台日志正常显示处理信息

### 4. 场景检测测试
1. 导入视频文件后
2. 触发场景检测功能
3. 观察场景检测过程

**预期结果**:
- 场景检测正常工作
- 不出现 FFmpeg 命令行窗口闪现
- 场景检测结果正确显示

### 5. 视频切分测试
1. 完成场景检测后
2. 执行视频切分操作
3. 观察切分过程

**预期结果**:
- 视频切分正常执行
- 切分过程中不出现命令行闪现
- 切分后的视频文件正常生成

## 🔍 验证要点

### Windows 特定验证
- ✅ 确认 `CREATE_NO_WINDOW` 标志正确应用
- ✅ 验证只在 Windows 平台应用此修复
- ✅ 确认其他平台不受影响

### 功能完整性验证
- ✅ 所有 FFmpeg 功能正常工作
- ✅ 错误处理机制正常
- ✅ 性能监控正常记录
- ✅ 日志系统正常输出

### 用户体验验证
- ✅ 无命令行窗口闪现
- ✅ 应用响应速度正常
- ✅ 操作流程顺畅

## 📊 测试结果记录

### 编译测试
- ✅ **Rust 编译**: 通过 (`cargo check`)
- ✅ **前端构建**: 通过 (`pnpm build`)
- ✅ **应用启动**: 成功

### 功能测试
- ⏳ **FFmpeg 状态检查**: 待测试
- ⏳ **视频元数据提取**: 待测试
- ⏳ **场景检测**: 待测试
- ⏳ **视频切分**: 待测试
- ⏳ **缩略图生成**: 待测试

### 用户体验测试
- ⏳ **命令行闪现**: 待验证修复
- ⏳ **操作流畅性**: 待测试
- ⏳ **错误处理**: 待测试

## 🚀 部署建议

### 测试通过后的步骤
1. **提交修复代码**
   ```bash
   git add .
   git commit -m "hotfix: 修复 FFmpeg 命令行闪现问题"
   ```

2. **合并到主分支**
   ```bash
   git checkout master
   git merge hotfix/fix-ffmpeg-console-flash
   ```

3. **创建补丁版本**
   - 更新版本号到 v0.1.3
   - 创建发布标签
   - 构建新的安装包

### 发布说明
```markdown
## v0.1.3 Hotfix - 修复命令行闪现问题

### 🐛 Bug 修复
- 修复了 Windows 上 FFmpeg/FFprobe 执行时命令行窗口闪现的问题
- 改善了用户体验，操作过程更加流畅

### 🔧 技术改进
- 在 Windows 平台使用 CREATE_NO_WINDOW 标志隐藏控制台窗口
- 保持了所有 FFmpeg 功能的完整性和性能
```

## 📝 注意事项

1. **平台兼容性**: 此修复仅影响 Windows 平台，其他平台行为不变
2. **功能完整性**: 确保所有 FFmpeg 相关功能正常工作
3. **性能影响**: 修复不应影响 FFmpeg 执行性能
4. **错误处理**: 保持原有的错误处理逻辑

---

**测试负责人**: [测试人员姓名]  
**测试日期**: 2025年1月13日  
**修复分支**: `hotfix/fix-ffmpeg-console-flash`
