# 多轮对话功能实现文档

## 概述

本文档描述了基于 `promptx/tauri-desktop-app-expert` 开发规范实现的多轮对话功能。该功能扩展了现有的 Gemini API 集成，支持会话历史管理和上下文保持的多轮对话。

## 功能特性

### 🎯 核心功能
- **多轮对话支持**: 支持历史消息传递给后端，构建完整的对话上下文
- **会话管理**: 利用 session_id 进行会话生命周期管理
- **历史消息存储**: 完整的会话历史持久化存储
- **上下文保持**: 在多轮对话中保持对话上下文
- **类型安全**: 完整的 TypeScript 类型定义

### 🏗️ 架构设计
遵循 Tauri 四层架构设计模式：

1. **表现层 (Presentation Layer)**
   - `conversation_commands.rs`: Tauri 命令处理
   - 前端 React 组件和页面

2. **业务逻辑层 (Business Layer)**
   - `conversation_service.rs`: 会话管理业务逻辑
   - 多轮对话处理逻辑

3. **数据访问层 (Data Layer)**
   - `conversation_repository.rs`: 会话数据访问
   - `conversation.rs`: 数据模型定义

4. **基础设施层 (Infrastructure Layer)**
   - 扩展的 `gemini_service.rs`: 支持多轮对话的 API 调用

## 技术实现

### 后端实现 (Rust)

#### 数据模型
```rust
// 会话消息
pub struct ConversationMessage {
    pub id: String,
    pub session_id: String,
    pub role: MessageRole,
    pub content: Vec<MessageContent>,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

// 会话会话
pub struct ConversationSession {
    pub id: String,
    pub title: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
    pub metadata: Option<serde_json::Value>,
}
```

#### API 结构修改
扩展了 Gemini API 的请求结构以支持多轮对话：

```rust
pub struct GenerateContentRequest {
    pub contents: Vec<ContentPart>,  // 支持多条历史消息
    pub generation_config: GenerationConfig,
}

pub struct ContentPart {
    pub role: String,  // "user", "assistant", "system"
    pub parts: Vec<Part>,
}
```

#### 核心服务方法
```rust
impl ConversationService {
    // 处理多轮对话
    pub async fn process_multi_turn_conversation(
        &self,
        request: MultiTurnConversationRequest,
    ) -> Result<MultiTurnConversationResponse>

    // 会话管理
    pub async fn create_session(&self, request: CreateConversationSessionRequest) -> Result<ConversationSession>
    pub async fn get_conversation_history(&self, query: ConversationHistoryQuery) -> Result<ConversationHistory>
}
```

### 前端实现 (TypeScript + React)

#### 类型定义
```typescript
// 多轮对话请求
export interface MultiTurnConversationRequest {
  session_id?: string;
  user_message: string;
  include_history?: boolean;
  max_history_messages?: number;
  system_prompt?: string;
  config?: Record<string, any>;
}

// 多轮对话响应
export interface MultiTurnConversationResponse {
  session_id: string;
  assistant_message: string;
  message_id: string;
  response_time_ms: number;
  model_used: string;
  metadata?: Record<string, any>;
}
```

#### 服务层
```typescript
export class ConversationService {
  static async processMultiTurnConversation(
    request: MultiTurnConversationRequest
  ): Promise<MultiTurnConversationResponse>

  static async createSession(request: CreateConversationSessionRequest): Promise<ConversationSession>
  static async getConversationHistory(query: ConversationHistoryQuery): Promise<ConversationHistory>
}
```

## 数据库设计

### 会话表 (conversation_sessions)
```sql
CREATE TABLE conversation_sessions (
    id TEXT PRIMARY KEY,
    title TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    metadata TEXT
);
```

### 消息表 (conversation_messages)
```sql
CREATE TABLE conversation_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL,
    content TEXT NOT NULL,
    timestamp TEXT NOT NULL,
    metadata TEXT,
    FOREIGN KEY (session_id) REFERENCES conversation_sessions (id) ON DELETE CASCADE
);
```

## 使用示例

### 基本多轮对话
```typescript
import { ConversationService, MultiTurnConversationHelper } from '../services/conversationService';

// 创建对话请求
const request = MultiTurnConversationHelper.createTextConversationRequest(
  "你好，请介绍一下自己",
  sessionId, // 可选，如果为空会创建新会话
  {
    includeHistory: true,
    maxHistoryMessages: 20,
    systemPrompt: "你是一个友好的AI助手"
  }
);

// 处理多轮对话
const response = await ConversationService.processMultiTurnConversation(request);
console.log(response.assistant_message);
```

### 会话管理
```typescript
// 创建新会话
const session = await ConversationService.createSession({
  title: "新的对话",
  metadata: { topic: "技术讨论" }
});

// 获取会话历史
const history = await ConversationService.getConversationHistory({
  session_id: session.id,
  limit: 50,
  include_system_messages: false
});
```

## 测试组件

项目包含了完整的测试组件：

- `MultiTurnChatTest.tsx`: 多轮对话测试界面
- `MultiTurnChatTestPage.tsx`: 测试页面
- `conversation_service_test.rs`: 后端单元测试

## 配置选项

### 多轮对话配置
```typescript
interface MultiTurnConversationOptions {
  sessionId?: string;           // 会话ID
  includeHistory?: boolean;     // 是否包含历史消息
  maxHistoryMessages?: number;  // 最大历史消息数
  systemPrompt?: string;        // 系统提示词
  temperature?: number;         // 生成温度
  maxTokens?: number;          // 最大输出令牌数
  timeout?: number;            // 请求超时时间
}
```

### 会话清理配置
```rust
pub struct ConversationCleanupConfig {
    pub max_inactive_days: u32,      // 最大非活跃天数
    pub max_messages_per_session: u32, // 每个会话最大消息数
    pub auto_cleanup_enabled: bool,   // 是否启用自动清理
}
```

## 性能优化

1. **数据库索引**: 为会话ID和时间戳创建索引以提高查询性能
2. **消息限制**: 支持限制历史消息数量以控制API请求大小
3. **连接池**: 使用数据库连接池提高并发性能
4. **异步处理**: 所有数据库操作都是异步的

## 安全考虑

1. **输入验证**: 严格验证用户输入和会话ID
2. **权限控制**: 遵循最小权限原则
3. **数据加密**: 敏感数据的安全存储
4. **会话隔离**: 确保会话之间的数据隔离

## 扩展性

该实现设计为可扩展的：

1. **插件化**: 支持自定义消息处理器
2. **多模型支持**: 可以轻松扩展支持其他AI模型
3. **存储后端**: 可以替换为其他数据库后端
4. **消息类型**: 支持文本、文件、内联数据等多种消息类型

## 开发规范遵循

- ✅ 遵循 `promptx/tauri-desktop-app-expert` 开发规范
- ✅ 四层架构设计模式
- ✅ 类型安全的 TypeScript 实现
- ✅ 完整的错误处理和用户反馈
- ✅ 性能优化和安全防护
- ✅ 模块化和可维护的代码结构

## 未来改进

1. **流式响应**: 支持流式生成响应
2. **消息搜索**: 实现会话历史搜索功能
3. **导出功能**: 支持会话导出为各种格式
4. **多用户支持**: 扩展为多用户会话管理
5. **实时同步**: 支持多设备间的会话同步
