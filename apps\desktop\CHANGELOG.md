# Changelog

All notable changes to MixVideo Desktop will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.3] - 2025-01-13

### 🐛 Fixed
- **命令行闪现问题**: 修复了 Windows 上 FFmpeg/FFprobe 执行时命令行窗口闪现的问题
- **用户体验改善**: 使用 CREATE_NO_WINDOW 标志隐藏控制台窗口，操作更加流畅

### 🔧 Technical
- 添加 Windows 特定的 CommandExt 导入
- 创建 `create_hidden_command()` 辅助函数统一处理命令执行
- 替换所有 FFmpeg/FFprobe 命令调用使用隐藏控制台模式
- 保持所有功能完整性和性能不变

### 📦 Affected Components
- FFmpeg 可用性检查
- 视频/音频元数据提取
- 场景检测功能
- 视频切分操作
- 缩略图生成
- 版本信息获取

## [0.1.2] - 2025-01-13

### 🚀 Added
- **性能监控系统**: 实现了完整的性能监控框架
  - 全局性能监控器 `PERFORMANCE_MONITOR`
  - 操作计时器自动记录执行时间和成功率
  - 性能指标统计：平均响应时间、最慢操作、错误率
  - 新增 Tauri 命令：`get_performance_report`、`cleanup_performance_data`、`record_performance_metric`

- **统一错误处理系统**: 完善的错误类型定义和处理
  - `AppError`、`MaterialError`、`ProjectError`、`DatabaseError`、`SystemError`
  - 错误处理工具：文件路径验证、目录验证、存储空间检查
  - 详细的错误信息和用户友好的错误消息

- **结构化日志系统**: 基于 tracing 框架的日志系统
  - 自动日志文件轮转和清理
  - 多级别日志支持：DEBUG/INFO/WARN/ERROR
  - 结构化日志记录和专用日志器 `StructuredLogger`

- **项目卡片增强**: 首页项目列表功能大幅提升
  - 项目统计信息展示：素材数量、文件大小、类型分布
  - 打开项目文件夹功能
  - 加载状态和错误处理优化

- **素材卡片增强**: 素材详情展示功能完善
  - 丰富的元数据展示：视频、音频、图片信息
  - 处理统计信息：场景检测、切分状态
  - 颜色编码的信息分类

### 🔧 Improved
- **视频处理优化**: 
  - 场景检测算法优化，支持多种备用方案
  - 视频切分输出路径优化
  - 处理进度和状态反馈改进

- **用户体验提升**:
  - 响应式布局优化
  - 加载状态和错误提示改进
  - 文件操作便捷性提升

### 🛠️ Technical
- **架构改进**: 严格遵循 Tauri 开发规范
  - 四层架构清晰分离
  - 模块化设计和类型安全
  - 性能优化和安全权限配置

- **代码质量**: 
  - 添加 tracing、lazy_static、thiserror 等依赖
  - 完善的错误处理和日志记录
  - 性能监控集成到关键操作

### 📊 Performance
- 应用启动时间优化
- 关键操作性能监控
- 内存使用和响应时间改进

## [0.1.1] - 2025-01-12

### 🚀 Added
- 基础项目管理功能
- 素材导入和处理系统
- 视频场景检测和切分功能
- FFmpeg 集成和视频处理

### 🔧 Improved
- 用户界面优化
- 文件处理流程改进

## [0.1.0] - 2025-01-11

### 🚀 Added
- 初始版本发布
- 基础应用框架
- 项目创建和管理
- Tauri 桌面应用基础架构

---

## 版本说明

- **Major (主版本号)**: 不兼容的 API 修改
- **Minor (次版本号)**: 向下兼容的功能性新增
- **Patch (修订号)**: 向下兼容的问题修正

## 贡献指南

请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何为项目做贡献。

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
