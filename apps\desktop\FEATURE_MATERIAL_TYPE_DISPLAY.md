# 素材类型展示功能开发

## 功能概述

根据promptx/tauri-desktop-app-expert规定的开发规范，实现了素材管理的类型区分展示功能：

### 1. 图片素材展示
- **功能**：直接展示图片内容
- **实现**：通过后端API `get_material_thumbnail_base64` 获取图片的base64数据
- **支持格式**：JPG, JPEG, PNG, GIF, WEBP, BMP, TIFF, SVG
- **特点**：
  - 懒加载机制，只有当图片可见时才加载
  - 缓存机制，避免重复请求
  - 自动检测MIME类型
  - 保持原始宽高比

### 2. 视频素材展示
- **功能**：展示视频缩略图
- **实现**：使用FFmpeg生成视频首帧缩略图
- **特点**：
  - 自动生成160px宽度的缩略图
  - 保持视频原始宽高比
  - 缓存生成的缩略图文件
  - 支持重试机制

### 3. 音频素材展示
- **功能**：提供音频播放控件
- **实现**：通过后端API `get_audio_file_base64` 获取音频数据并播放
- **支持格式**：MP3, WAV, FLAC, AAC, OGG, WMA, M4A
- **特点**：
  - 点击播放/暂停控制
  - 播放状态指示
  - 音频数据通过base64传输
  - 播放结束自动重置状态

## 技术实现

### 后端改进

1. **扩展 `get_material_thumbnail_base64` 命令**
   - 添加对图片类型的支持
   - 根据素材类型分别处理
   - 自动检测文件MIME类型

2. **新增 `get_audio_file_base64` 命令**
   - 专门处理音频文件访问
   - 支持多种音频格式
   - 安全的文件访问机制

### 前端改进

1. **MaterialThumbnail组件增强**
   - 支持三种素材类型的不同展示方式
   - 音频播放控件集成
   - 改进的错误处理和加载状态

2. **音频播放功能**
   - 使用HTML5 Audio API
   - 播放状态管理
   - 用户友好的播放界面

## 文件修改列表

### 后端文件
- `apps/desktop/src-tauri/src/presentation/commands/material_commands.rs`
  - 扩展 `get_material_thumbnail_base64` 支持图片
  - 新增 `get_audio_file_base64` 命令
- `apps/desktop/src-tauri/src/lib.rs`
  - 注册新的音频API命令

### 前端文件
- `apps/desktop/src/components/MaterialThumbnail.tsx`
  - 重构组件支持三种素材类型
  - 添加音频播放功能
  - 改进懒加载和缓存机制
- `apps/desktop/src/types/material.ts`
  - 添加新的音频API类型定义

## 使用示例

```tsx
// 在MaterialCard或其他组件中使用
<MaterialThumbnail
  material={material}
  size="medium"
  thumbnailCache={thumbnailCache}
  setThumbnailCache={setThumbnailCache}
/>
```

## 安全考虑

1. **文件访问安全**：所有文件访问都通过后端API，避免直接使用file://协议
2. **路径清理**：自动处理Windows长路径前缀
3. **文件存在性检查**：在访问文件前验证文件是否存在
4. **错误处理**：完善的错误处理和用户反馈

## 性能优化

1. **懒加载**：只有当素材可见时才加载内容
2. **缓存机制**：避免重复请求相同素材
3. **异步处理**：所有文件操作都是异步的
4. **内存管理**：音频元素的正确清理

## 测试建议

1. 测试不同格式的图片文件显示
2. 测试视频缩略图生成
3. 测试音频播放功能
4. 测试文件不存在的错误处理
5. 测试大文件的加载性能

## 后续改进

1. 可以考虑添加图片缩略图生成以提高加载速度
2. 音频播放可以添加进度条和音量控制
3. 可以添加更多音频格式支持
4. 考虑添加视频预览播放功能
