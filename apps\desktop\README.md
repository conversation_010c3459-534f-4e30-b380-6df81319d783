# @mixvideo/desktop

MixVideo 桌面应用 - 基于 Tauri + React + TypeScript 构建的跨平台桌面应用。

## 🚀 快速开始

### 开发环境要求

- Node.js 18+
- Rust 1.70+
- PNPM 8+

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
# 或
pnpm tauri:dev
```

### 构建应用

```bash
pnpm tauri:build
```

## 📁 项目结构

```
apps/desktop/
├── src/                    # React 前端源码
│   ├── components/         # React 组件
│   ├── pages/             # 页面组件
│   ├── hooks/             # 自定义 Hooks
│   ├── services/          # API 服务
│   ├── types/             # TypeScript 类型
│   └── utils/             # 工具函数
├── src-tauri/             # Rust 后端源码
│   ├── src/               # Rust 源码
│   ├── Cargo.toml         # Rust 依赖配置
│   └── tauri.conf.json    # Tauri 配置
├── public/                # 静态资源
└── package.json           # 前端依赖配置
```

## 🛠️ 技术栈

- **前端**: React 18 + TypeScript + Vite
- **后端**: Rust + Tauri 2.0
- **UI**: 待定 (可选择 Ant Design、Material-UI 等)
- **状态管理**: 待定 (可选择 Zustand、Redux Toolkit 等)

## 📝 开发指南

### 推荐 IDE 设置

- [VS Code](https://code.visualstudio.com/) + [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
