# 便捷小工具功能 - v0.1.33

## 功能概述

新增便捷小工具页面，提供AI检索图片/数据清洗功能，支持JSONL格式数据的去重处理。

## 功能特性

### AI检索图片/数据清洗工具

- **文件格式支持**: JSONL (JSON Lines) 格式
- **数据去重**: 基于URI字段进行精确匹配去重
- **进度显示**: 实时显示处理进度和状态
- **批量处理**: 支持大数据量文件处理
- **结果统计**: 显示原始数据量、去除数量、最终结果数量

## 使用方法

1. **选择全部数据文件**: 包含所有数据的JSONL文件
2. **选择要去除的数据文件**: 包含需要去除数据的JSONL文件
3. **选择输出文件**: 指定处理结果的保存位置
4. **开始处理**: 点击"开始处理"按钮执行数据清洗

## 数据格式说明

### 全部数据文件格式
```json
{
  "kind": "storage#object",
  "id": "...",
  "uri": "gs://bucket/path/to/file.jpg",
  // 其他字段...
}
```

### 要去除的数据文件格式
```json
{
  "id": "uuid",
  "schema_id": "default_schema",
  "json_data": "{\"uri\":\"gs://bucket/path/to/file.jpg\", ...}"
}
```

## 处理逻辑

1. **解析要去除的数据**: 从`json_data`字段中提取`uri`值
2. **构建URI集合**: 将所有要去除的URI存储在HashSet中
3. **过滤全部数据**: 遍历全部数据文件，过滤掉URI匹配的记录
4. **保存结果**: 将过滤后的数据保存到输出文件

## 技术实现

### 后端 (Rust)
- **命令**: `clean_jsonl_data`
- **文件**: `src-tauri/src/presentation/commands/tools_commands.rs`
- **特性**: 
  - 异步处理
  - 进度事件发送
  - 内存优化的流式处理
  - 错误处理和恢复

### 前端 (React + TypeScript)
- **页面**: `src/pages/Tools.tsx`
- **路由**: `/tools`
- **特性**:
  - 文件选择对话框
  - 实时进度显示
  - 结果统计展示
  - 错误处理和用户反馈

## 导航集成

在主导航栏中新增"便捷工具"菜单项，使用扳手图标，提供快速访问入口。

## 测试数据

项目包含测试数据文件：
- `test_data/sample_all_data.jsonl`: 示例全部数据文件
- `test_data/sample_remove_data.jsonl`: 示例要去除的数据文件

## 性能优化

- **流式处理**: 逐行读取文件，避免内存溢出
- **进度更新**: 每处理100行更新一次进度，平衡性能和用户体验
- **错误容错**: 解析失败的行会被跳过并记录警告

## 开发规范遵循

- 遵循Tauri开发规范的四层架构设计
- 使用TypeScript确保类型安全
- 遵循UI/UX设计标准，提供优雅的用户界面
- 实现完整的错误处理和用户反馈机制

## 未来扩展

- 支持更多数据格式 (CSV, JSON等)
- 添加更多数据清洗规则
- 支持自定义匹配字段
- 添加数据预览功能
