use serde::{Serialize, Deserialize};
use uuid::Uuid;
use chrono::Utc;

/// 剪映草稿内容结构 - 用于导出
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct JianYingDraftContent {
    pub canvas_config: <PERSON><PERSON><PERSON>ingCanvasConfig,
    pub color_space: i32,
    pub config: Ji<PERSON>YingConfig,
    pub cover: Option<String>,
    pub create_time: i64,
    pub duration: u64,
    pub extra_info: Option<String>,
    pub fps: f64,
    pub free_render_index_mode_on: bool,
    pub group_container: Option<String>,
    pub id: String,
    pub keyframe_graph_list: Vec<String>,
    pub keyframes: JianYingKeyframes,
    pub last_modified_platform: JianYingPlatform,
    pub materials: JianYingMaterials,
    pub render_index_track_mode_on: bool,
    pub retouch_cover: Option<String>,
    pub source: String,
    pub static_cover_image_path: String,
    pub time_marks: Option<String>,
    pub tracks: Vec<JianYingTrack>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct JianYingCanvasConfig {
    pub height: u32,
    pub ratio: String,
    pub width: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct JianYingConfig {
    pub adjust_max_index: i32,
    pub attachment_info: Vec<String>,
    pub combination_max_index: i32,
    pub export_range: Option<String>,
    pub extract_audio_last_index: i32,
    pub lyrics_recognition_id: String,
    pub lyrics_sync: bool,
    pub lyrics_taskinfo: Vec<String>,
    pub maintrack_adsorb: bool,
    pub material_save_mode: i32,
    pub multi_language_current: String,
    pub multi_language_list: Vec<String>,
    pub multi_language_main: String,
    pub multi_language_mode: String,
    pub original_sound_last_index: i32,
    pub record_audio_last_index: i32,
    pub sticker_max_index: i32,
    pub subtitle_keywords_config: Option<String>,
    pub subtitle_recognition_id: String,
    pub subtitle_sync: bool,
    pub subtitle_taskinfo: Vec<String>,
    pub system_font_list: Vec<String>,
    pub video_mute: bool,
    pub zoom_info_params: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingKeyframes {
    pub adjusts: Vec<String>,
    pub audios: Vec<String>,
    pub effects: Vec<String>,
    pub filters: Vec<String>,
    pub handwrites: Vec<String>,
    pub stickers: Vec<String>,
    pub texts: Vec<String>,
    pub videos: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingPlatform {
    pub app_id: i32,
    pub app_source: String,
    pub app_version: String,
    pub device_id: String,
    pub hard_disk_id: String,
    pub mac_address: String,
    pub os: String,
    pub os_version: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingMaterials {
    pub ai_translates: Vec<String>,
    pub audio_balances: Vec<String>,
    pub audio_effects: Vec<String>,
    pub audio_fades: Vec<String>,
    pub audio_track_indexes: Vec<String>,
    pub audios: Vec<JianYingAudio>,
    pub beats: Vec<String>,
    pub canvases: Vec<String>,
    pub chromas: Vec<String>,
    pub color_curves: Vec<String>,
    pub digital_humans: Vec<String>,
    pub drafts: Vec<String>,
    pub effects: Vec<String>,
    pub flowers: Vec<String>,
    pub green_screens: Vec<String>,
    pub handwrites: Vec<String>,
    pub hsl: Vec<String>,
    pub images: Vec<String>,
    pub log_color_wheels: Vec<String>,
    pub loudnesses: Vec<String>,
    pub manual_deformations: Vec<String>,
    pub masks: Vec<String>,
    pub material_animations: Vec<String>,
    pub material_colors: Vec<String>,
    pub multi_language_refs: Vec<String>,
    pub placeholders: Vec<String>,
    pub plugin_effects: Vec<String>,
    pub primary_color_wheels: Vec<String>,
    pub realtime_denoises: Vec<String>,
    pub shapes: Vec<String>,
    pub smart_crops: Vec<String>,
    pub smart_relights: Vec<String>,
    pub sound_channel_mappings: Vec<String>,
    pub speeds: Vec<JianYingSpeed>,
    pub stickers: Vec<String>,
    pub tail_leaders: Vec<String>,
    pub text_templates: Vec<String>,
    pub texts: Vec<String>,
    pub time_marks: Vec<String>,
    pub transitions: Vec<String>,
    pub video_effects: Vec<String>,
    pub video_trackings: Vec<String>,
    pub videos: Vec<JianYingVideo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingTrack {
    pub attribute: i32,
    pub flag: i32,
    pub id: String,
    pub is_default_name: bool,
    pub name: String,
    pub segments: Vec<JianYingSegment>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingSegment {
    pub caption_info: Option<String>,
    pub cartoon: bool,
    pub clip: JianYingClip,
    pub common_keyframes: Vec<String>,
    pub enable_adjust: bool,
    pub enable_color_correct_adjust: bool,
    pub enable_color_curves: bool,
    pub enable_color_match_adjust: bool,
    pub enable_color_wheels: bool,
    pub enable_lut: bool,
    pub enable_smart_color_adjust: bool,
    pub extra_material_refs: Vec<String>,
    pub group_id: String,
    pub hdr_settings: JianYingHdrSettings,
    pub id: String,
    pub intensifies_audio: bool,
    pub is_placeholder: bool,
    pub is_tone_modify: bool,
    pub keyframe_refs: Vec<String>,
    pub last_nonzero_volume: f64,
    pub material_id: String,
    pub render_index: i32,
    pub responsive_layout: JianYingResponsiveLayout,
    pub reverse: bool,
    pub source_timerange: JianYingTimeRange,
    pub speed: f64,
    pub target_timerange: JianYingTimeRange,
    pub template_id: String,
    pub template_scene: String,
    pub track_attribute: i32,
    pub track_render_index: i32,
    pub uniform_scale: JianYingUniformScale,
    pub visible: bool,
    pub volume: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingAudio {
    pub app_id: i32,
    pub category_id: String,
    pub category_name: String,
    pub check_flag: i32,
    pub copyright_limit_type: String,
    pub duration: u64,
    pub effect_id: String,
    pub formula_id: String,
    pub id: String,
    pub intensifies_path: String,
    pub is_ai_clone_tone: bool,
    pub is_text_edit_overdub: bool,
    pub is_ugc: bool,
    pub local_material_id: String,
    pub music_id: String,
    pub name: String,
    pub path: String,
    pub query: String,
    pub request_id: String,
    pub resource_id: String,
    pub search_id: String,
    pub source_from: String,
    pub source_platform: i32,
    pub team_id: String,
    pub text_id: String,
    pub tone_category_id: String,
    pub tone_category_name: String,
    pub tone_effect_id: String,
    pub tone_effect_name: String,
    pub tone_platform: String,
    pub tone_second_category_id: String,
    pub tone_second_category_name: String,
    pub tone_speaker: String,
    pub tone_type: String,
    #[serde(rename = "type")]
    pub material_type: String,
    pub video_id: String,
    pub wave_points: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingSpeed {
    pub curve_speed: Option<String>,
    pub id: String,
    pub mode: i32,
    pub speed: f64,
    #[serde(rename = "type")]
    pub speed_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingVideo {
    pub aigc_type: String,
    pub audio_fade: Option<String>,
    pub cartoon_path: String,
    pub category_id: String,
    pub category_name: String,
    pub check_flag: i32,
    pub crop: JianYingCrop,
    pub crop_ratio: String,
    pub crop_scale: f64,
    pub duration: u64,
    pub extra_type_option: i32,
    pub formula_id: String,
    pub freeze: Option<String>,
    pub has_audio: bool,
    pub height: u32,
    pub id: String,
    pub intensifies_audio_path: String,
    pub intensifies_path: String,
    pub is_ai_generate_content: bool,
    pub is_copyright: bool,
    pub is_text_edit_overdub: bool,
    pub is_unified_beauty_mode: bool,
    pub local_id: String,
    pub local_material_id: String,
    pub material_id: String,
    pub material_name: String,
    pub material_url: String,
    pub matting: JianYingMatting,
    pub media_path: String,
    pub object_locked: Option<String>,
    pub origin_material_id: String,
    pub path: String,
    pub picture_from: String,
    pub picture_set_category_id: String,
    pub picture_set_category_name: String,
    pub request_id: String,
    pub reverse_intensifies_path: String,
    pub reverse_path: String,
    pub smart_motion: Option<String>,
    pub source: i32,
    pub source_platform: i32,
    pub stable: JianYingStable,
    pub team_id: String,
    #[serde(rename = "type")]
    pub video_type: String,
    pub video_algorithm: JianYingVideoAlgorithm,
    pub width: u32,
}

// 辅助结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingClip {
    pub alpha: f64,
    pub flip: JianYingFlip,
    pub rotation: f64,
    pub scale: JianYingScale,
    pub transform: JianYingTransform,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingFlip {
    pub horizontal: bool,
    pub vertical: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingScale {
    pub x: f64,
    pub y: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingTransform {
    pub x: f64,
    pub y: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingHdrSettings {
    pub intensity: f64,
    pub mode: i32,
    pub nits: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingResponsiveLayout {
    pub enable: bool,
    pub horizontal_pos_layout: i32,
    pub size_layout: i32,
    pub target_follow: String,
    pub vertical_pos_layout: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingTimeRange {
    pub duration: u64,
    pub start: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingUniformScale {
    pub on: bool,
    pub value: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingCrop {
    pub lower_left_x: f64,
    pub lower_left_y: f64,
    pub lower_right_x: f64,
    pub lower_right_y: f64,
    pub upper_left_x: f64,
    pub upper_left_y: f64,
    pub upper_right_x: f64,
    pub upper_right_y: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingMatting {
    pub flag: i32,
    pub has_use_quick_brush: bool,
    pub has_use_quick_eraser: bool,
    #[serde(rename = "interactiveTime")]
    pub interactive_time: Vec<String>,
    pub path: String,
    pub strokes: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingStable {
    pub matrix_path: String,
    pub stable_level: i32,
    pub time_range: JianYingTimeRange,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JianYingVideoAlgorithm {
    pub algorithms: Vec<String>,
    pub complement_frame_config: Option<String>,
    pub deflicker: Option<String>,
    pub gameplay_configs: Vec<String>,
    pub motion_blur_config: Option<String>,
    pub noise_reduction: Option<String>,
    pub path: String,
    pub quality_enhance: Option<String>,
    pub time_range: Option<JianYingTimeRange>,
}

/// 剪映导出服务
pub struct JianYingExportService;

impl JianYingExportService {
    /// 创建默认的剪映草稿内容
    pub fn create_default_draft_content() -> JianYingDraftContent {
        let draft_id = Uuid::new_v4().to_string();
        let now = Utc::now().timestamp();

        JianYingDraftContent {
            canvas_config: JianYingCanvasConfig {
                height: 1920,
                ratio: "9:16".to_string(),
                width: 1080,
            },
            color_space: 0,
            config: JianYingConfig {
                adjust_max_index: 1,
                attachment_info: vec![],
                combination_max_index: 1,
                export_range: None,
                extract_audio_last_index: 1,
                lyrics_recognition_id: String::new(),
                lyrics_sync: true,
                lyrics_taskinfo: vec![],
                maintrack_adsorb: true,
                material_save_mode: 0,
                multi_language_current: "none".to_string(),
                multi_language_list: vec![],
                multi_language_main: "none".to_string(),
                multi_language_mode: "none".to_string(),
                original_sound_last_index: 1,
                record_audio_last_index: 1,
                sticker_max_index: 1,
                subtitle_keywords_config: None,
                subtitle_recognition_id: String::new(),
                subtitle_sync: true,
                subtitle_taskinfo: vec![],
                system_font_list: vec![],
                video_mute: false,
                zoom_info_params: None,
            },
            cover: None,
            create_time: now,
            duration: 0,
            extra_info: None,
            fps: 30.0,
            free_render_index_mode_on: false,
            group_container: None,
            id: draft_id,
            keyframe_graph_list: vec![],
            keyframes: JianYingKeyframes {
                adjusts: vec![],
                audios: vec![],
                effects: vec![],
                filters: vec![],
                handwrites: vec![],
                stickers: vec![],
                texts: vec![],
                videos: vec![],
            },
            last_modified_platform: JianYingPlatform {
                app_id: 3704,
                app_source: "lv".to_string(),
                app_version: "5.9.0".to_string(),
                device_id: "0594836068dad896e25a104fc9dbabab".to_string(),
                hard_disk_id: "92ff8fc0225cc7379b7488c983cc022b".to_string(),
                mac_address: "32d6cbfd9256fd8884fac27c2658c25c".to_string(),
                os: "windows".to_string(),
                os_version: "10.0.26100".to_string(),
            },
            materials: JianYingMaterials {
                ai_translates: vec![],
                audio_balances: vec![],
                audio_effects: vec![],
                audio_fades: vec![],
                audio_track_indexes: vec![],
                audios: vec![],
                beats: vec![],
                canvases: vec![],
                chromas: vec![],
                color_curves: vec![],
                digital_humans: vec![],
                drafts: vec![],
                effects: vec![],
                flowers: vec![],
                green_screens: vec![],
                handwrites: vec![],
                hsl: vec![],
                images: vec![],
                log_color_wheels: vec![],
                loudnesses: vec![],
                manual_deformations: vec![],
                masks: vec![],
                material_animations: vec![],
                material_colors: vec![],
                multi_language_refs: vec![],
                placeholders: vec![],
                plugin_effects: vec![],
                primary_color_wheels: vec![],
                realtime_denoises: vec![],
                shapes: vec![],
                smart_crops: vec![],
                smart_relights: vec![],
                sound_channel_mappings: vec![],
                speeds: vec![],
                stickers: vec![],
                tail_leaders: vec![],
                text_templates: vec![],
                texts: vec![],
                time_marks: vec![],
                transitions: vec![],
                video_effects: vec![],
                video_trackings: vec![],
                videos: vec![],
            },
            render_index_track_mode_on: true,
            retouch_cover: None,
            source: "default".to_string(),
            static_cover_image_path: String::new(),
            time_marks: None,
            tracks: vec![],
        }
    }
}
