#[cfg(test)]
mod tests {
    use crate::business::services::cloud_upload_service::CloudUploadService;
    use crate::business::services::tests::test_utils::*;

    #[test]
    fn test_detect_content_type() {
        let service = CloudUploadService::new();
        
        // 测试视频文件
        assert_eq!(service.detect_content_type("video.mp4"), "video/mp4");
        assert_eq!(service.detect_content_type("video.mov"), "video/mp4");
        assert_eq!(service.detect_content_type("video.avi"), "video/mp4");
        assert_eq!(service.detect_content_type("video.mkv"), "video/mp4");
        assert_eq!(service.detect_content_type("video.webm"), "video/mp4");
        
        // 测试音频文件
        assert_eq!(service.detect_content_type("audio.mp3"), "audio/mpeg");
        assert_eq!(service.detect_content_type("audio.wav"), "audio/mpeg");
        assert_eq!(service.detect_content_type("audio.aac"), "audio/mpeg");
        assert_eq!(service.detect_content_type("audio.flac"), "audio/mpeg");
        assert_eq!(service.detect_content_type("audio.ogg"), "audio/mpeg");
        
        // 测试图片文件
        assert_eq!(service.detect_content_type("image.jpg"), "image/jpeg");
        assert_eq!(service.detect_content_type("image.jpeg"), "image/jpeg");
        assert_eq!(service.detect_content_type("image.png"), "image/png");
        assert_eq!(service.detect_content_type("image.gif"), "image/gif");
        assert_eq!(service.detect_content_type("image.webp"), "image/webp");
        
        // 测试其他文件
        assert_eq!(service.detect_content_type("document.pdf"), "application/pdf");
        assert_eq!(service.detect_content_type("data.json"), "application/json");
        assert_eq!(service.detect_content_type("text.txt"), "text/plain");
        assert_eq!(service.detect_content_type("unknown.xyz"), "application/octet-stream");
        
        // 测试大小写不敏感
        assert_eq!(service.detect_content_type("VIDEO.MP4"), "video/mp4");
        assert_eq!(service.detect_content_type("IMAGE.JPG"), "image/jpeg");
    }

    #[test]
    fn test_generate_remote_key() {
        let service = CloudUploadService::new();
        
        let key1 = service.generate_remote_key("/path/to/video.mp4");
        let key2 = service.generate_remote_key("/path/to/video.mp4");
        
        // 每次生成的key应该不同（因为包含UUID和时间戳）
        assert_ne!(key1, key2);
        
        // key应该包含文件名
        assert!(key1.contains("video.mp4"));
        assert!(key2.contains("video.mp4"));
        
        // key应该以templates/开头
        assert!(key1.starts_with("templates/"));
        assert!(key2.starts_with("templates/"));
        
        // 测试没有文件名的路径
        let key3 = service.generate_remote_key("/path/to/");
        assert!(key3.contains("unknown"));
    }

    #[test]
    fn test_service_creation() {
        let service = CloudUploadService::new();
        
        // 测试默认配置
        assert_eq!(service.get_base_url(), "https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run");
        assert_eq!(service.get_bearer_token(), "bowong7777");
        assert_eq!(service.get_timeout(), std::time::Duration::from_secs(120));
    }

    #[test]
    fn test_service_with_custom_config() {
        let custom_base_url = "https://custom.api.com".to_string();
        let custom_token = "custom_token".to_string();
        let custom_timeout = 60;
        
        let service = CloudUploadService::with_config(
            custom_base_url.clone(),
            custom_token.clone(),
            custom_timeout,
        );
        
        assert_eq!(service.get_base_url(), custom_base_url);
        assert_eq!(service.get_bearer_token(), custom_token);
        assert_eq!(service.get_timeout(), std::time::Duration::from_secs(custom_timeout));
    }

    #[tokio::test]
    async fn test_upload_nonexistent_file() {
        let service = CloudUploadService::new();
        
        let result = service.upload_file(
            "nonexistent_file.mp4",
            None,
            None,
        ).await;
        
        assert!(result.is_ok());
        let upload_result = result.unwrap();
        assert!(!upload_result.success);
        assert!(upload_result.error_message.is_some());
        assert!(upload_result.error_message.unwrap().contains("文件不存在"));
        assert_eq!(upload_result.file_size, 0);
    }

    #[tokio::test]
    async fn test_upload_existing_file_without_network() {
        let temp_dir = create_temp_dir();
        let test_file = create_test_video_file(&temp_dir, "test_video.mp4");
        
        let service = CloudUploadService::new();
        
        // 这个测试会因为网络请求失败而失败，但我们可以验证文件存在性检查
        let result = service.upload_file(
            test_file.to_str().unwrap(),
            Some("test/video.mp4".to_string()),
            None,
        ).await;
        
        // 由于没有真实的网络环境，这个测试预期会失败
        // 但我们可以验证它不是因为"文件不存在"而失败
        assert!(result.is_ok());
        let upload_result = result.unwrap();
        
        if !upload_result.success {
            // 如果失败，错误信息不应该是"文件不存在"
            if let Some(error_msg) = &upload_result.error_message {
                assert!(!error_msg.contains("文件不存在"));
            }
        }
        
        // 文件大小应该被正确读取
        assert!(upload_result.file_size > 0);
    }

    #[tokio::test]
    async fn test_batch_upload_empty_list() {
        let service = CloudUploadService::new();
        
        let result = service.upload_files(
            vec![],
            Some(3),
            None,
        ).await;
        
        assert!(result.is_ok());
        let results = result.unwrap();
        assert!(results.is_empty());
    }

    #[tokio::test]
    async fn test_batch_upload_with_mixed_files() {
        let temp_dir = create_temp_dir();
        let existing_file = create_test_video_file(&temp_dir, "existing.mp4");
        let nonexistent_file = temp_dir.path().join("nonexistent.mp4");
        
        let service = CloudUploadService::new();
        
        let file_paths = vec![
            existing_file.to_string_lossy().to_string(),
            nonexistent_file.to_string_lossy().to_string(),
        ];
        
        let result = service.upload_files(
            file_paths,
            Some(2),
            None,
        ).await;
        
        assert!(result.is_ok());
        let results = result.unwrap();
        assert_eq!(results.len(), 2);
        
        // 第一个文件（存在的）应该有文件大小
        assert!(results[0].file_size > 0);
        
        // 第二个文件（不存在的）应该失败
        assert!(!results[1].success);
        assert_eq!(results[1].file_size, 0);
        assert!(results[1].error_message.is_some());
    }

    #[test]
    fn test_service_clone() {
        let service1 = CloudUploadService::new();
        let service2 = service1.clone();
        
        // 克隆的服务应该有相同的配置
        assert_eq!(service1.get_base_url(), service2.get_base_url());
        assert_eq!(service1.get_bearer_token(), service2.get_bearer_token());
        assert_eq!(service1.get_timeout(), service2.get_timeout());
    }

    // 模拟测试：测试上传进度回调
    #[tokio::test]
    async fn test_upload_with_progress_callback() {
        let temp_dir = create_temp_dir();
        let test_file = create_test_video_file(&temp_dir, "test_with_progress.mp4");
        
        let service = CloudUploadService::new();
        
        // 创建进度回调
        let progress_called = std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false));
        let progress_called_clone = progress_called.clone();
        
        let progress_callback = Box::new(move |current: u64, total: u64| {
            progress_called_clone.store(true, std::sync::atomic::Ordering::Relaxed);
            println!("Progress: {}/{}", current, total);
        });
        
        let result = service.upload_file(
            test_file.to_str().unwrap(),
            None,
            Some(progress_callback),
        ).await;
        
        assert!(result.is_ok());
        
        // 注意：由于网络请求可能失败，我们不能保证进度回调一定被调用
        // 但如果上传成功，进度回调应该被调用
        let upload_result = result.unwrap();
        if upload_result.success {
            assert!(progress_called.load(std::sync::atomic::Ordering::Relaxed));
        }
    }

    // 测试文件路径处理
    #[test]
    fn test_file_path_handling() {
        let service = CloudUploadService::new();
        
        // 测试不同的文件路径格式
        let paths = vec![
            "/absolute/path/to/file.mp4",
            "relative/path/to/file.mp4",
            "C:\\Windows\\path\\to\\file.mp4",
            "./current/dir/file.mp4",
            "../parent/dir/file.mp4",
        ];
        
        for path in paths {
            let key = service.generate_remote_key(path);
            assert!(key.starts_with("templates/"));
            assert!(key.contains("file.mp4"));
        }
    }

    // 测试特殊字符处理
    #[test]
    fn test_special_characters_in_filename() {
        let service = CloudUploadService::new();
        
        let paths = vec![
            "/path/to/文件名.mp4",
            "/path/to/file with spaces.mp4",
            "/path/to/file-with-dashes.mp4",
            "/path/to/file_with_underscores.mp4",
            "/path/to/file.with.dots.mp4",
        ];
        
        for path in paths {
            let key = service.generate_remote_key(path);
            assert!(key.starts_with("templates/"));
            // 确保文件名被正确提取
            assert!(key.len() > "templates/".len());
        }
    }
}
