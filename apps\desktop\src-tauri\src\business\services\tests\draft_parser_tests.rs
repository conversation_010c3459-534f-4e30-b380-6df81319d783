#[cfg(test)]
mod tests {
    use crate::business::services::draft_parser::DraftContentParser;
    use crate::business::services::tests::test_utils::*;
    use crate::data::models::template::{TemplateMaterialType, TrackType, ImportStatus};

    #[test]
    fn test_parse_valid_draft_content() {
        let content = get_test_draft_content();
        let result = DraftContentParser::parse_content(&content, None, None);
        
        assert!(result.is_ok());
        let parse_result = result.unwrap();
        
        // 检查模板基本信息
        let template = &parse_result.template;
        assert_eq!(template.canvas_config.width, 1920);
        assert_eq!(template.canvas_config.height, 1080);
        assert_eq!(template.canvas_config.ratio, "16:9");
        assert_eq!(template.duration, 30000000);
        assert_eq!(template.fps, 30.0);
        assert_eq!(template.import_status, ImportStatus::Pending);
        
        // 检查素材
        assert_eq!(template.materials.len(), 4); // 1 video + 1 audio + 1 image + 1 text
        
        // 检查视频素材
        let video_material = template.materials.iter()
            .find(|m| matches!(m.material_type, TemplateMaterialType::Video))
            .expect("Should have video material");
        assert_eq!(video_material.name, "test_video.mp4");
        assert_eq!(video_material.original_path, "test_video.mp4");
        assert_eq!(video_material.duration, Some(15000000));
        assert_eq!(video_material.width, Some(1920));
        assert_eq!(video_material.height, Some(1080));
        
        // 检查音频素材
        let audio_material = template.materials.iter()
            .find(|m| matches!(m.material_type, TemplateMaterialType::Audio))
            .expect("Should have audio material");
        assert_eq!(audio_material.name, "test_audio.mp3");
        assert_eq!(audio_material.original_path, "test_audio.mp3");
        assert_eq!(audio_material.duration, Some(30000000));
        
        // 检查图片素材
        let image_material = template.materials.iter()
            .find(|m| matches!(m.material_type, TemplateMaterialType::Image))
            .expect("Should have image material");
        assert_eq!(image_material.name, "test_image.jpg");
        assert_eq!(image_material.original_path, "test_image.jpg");
        assert_eq!(image_material.width, Some(1920));
        assert_eq!(image_material.height, Some(1080));
        
        // 检查文本素材
        let text_material = template.materials.iter()
            .find(|m| matches!(m.material_type, TemplateMaterialType::Text))
            .expect("Should have text material");
        assert_eq!(text_material.name, "测试文本");
        assert!(text_material.original_path.is_empty()); // 文本素材没有文件路径
        assert!(text_material.metadata.is_some());
        
        // 检查轨道
        assert_eq!(template.tracks.len(), 2); // 1 video track + 1 audio track
        
        // 检查视频轨道
        let video_track = template.tracks.iter()
            .find(|t| matches!(t.track_type, TrackType::Video))
            .expect("Should have video track");
        assert_eq!(video_track.segments.len(), 1);
        assert_eq!(video_track.track_index, 0);
        
        // 检查音频轨道
        let audio_track = template.tracks.iter()
            .find(|t| matches!(t.track_type, TrackType::Audio))
            .expect("Should have audio track");
        assert_eq!(audio_track.segments.len(), 1);
        assert_eq!(audio_track.track_index, 1);
        
        // 检查轨道片段
        let video_segment = &video_track.segments[0];
        assert_eq!(video_segment.start_time, 0);
        assert_eq!(video_segment.duration, 15000000);
        assert_eq!(video_segment.segment_index, 0);
        assert!(video_segment.template_material_id.is_some());
        
        // 检查解析结果
        assert!(parse_result.missing_files.is_empty());
        assert!(parse_result.warnings.is_empty());
    }

    #[test]
    fn test_parse_invalid_json() {
        let content = get_invalid_draft_content();
        let result = DraftContentParser::parse_content(&content, None, None);
        
        assert!(result.is_err());
        let error = result.unwrap_err();
        assert!(error.to_string().contains("JSON解析失败"));
    }

    #[test]
    fn test_parse_with_missing_files() {
        let content = get_missing_files_draft_content();
        let result = DraftContentParser::parse_content(&content, None, None);
        
        assert!(result.is_ok());
        let parse_result = result.unwrap();
        
        // 应该检测到缺失文件
        assert_eq!(parse_result.missing_files.len(), 1);
        assert_eq!(parse_result.missing_files[0], "missing_video.mp4");
        
        // 模板仍应正常解析
        assert_eq!(parse_result.template.materials.len(), 1);
    }

    #[test]
    fn test_parse_file() {
        let temp_dir = create_temp_dir();
        let content = get_test_draft_content();
        let file_path = create_test_draft_file(&temp_dir, &content);
        
        let result = DraftContentParser::parse_file(
            file_path.to_str().unwrap(), 
            Some("测试模板".to_string())
        );
        
        assert!(result.is_ok());
        let parse_result = result.unwrap();
        
        assert_eq!(parse_result.template.name, "测试模板");
        assert_eq!(parse_result.template.source_file_path, Some(file_path.to_string_lossy().to_string()));
    }

    #[test]
    fn test_parse_nonexistent_file() {
        let result = DraftContentParser::parse_file("nonexistent.json", None);
        
        assert!(result.is_err());
        let error = result.unwrap_err();
        assert!(error.to_string().contains("无法读取文件"));
    }

    #[test]
    fn test_validate_result() {
        let content = get_test_draft_content();
        let parse_result = DraftContentParser::parse_content(&content, None, None).unwrap();
        
        let errors = DraftContentParser::validate_result(&parse_result);
        assert!(errors.is_empty()); // 有效的解析结果应该没有错误
    }

    #[test]
    fn test_validate_empty_template() {
        use crate::data::models::template::{Template, CanvasConfig};
        
        let template = Template::new(
            String::new(), // 空名称
            CanvasConfig { width: 0, height: 0, ratio: "".to_string() }, // 无效画布
            0, // 无效时长
            0.0, // 无效帧率
        );
        
        let parse_result = crate::business::services::draft_parser::ParseResult {
            template,
            missing_files: Vec::new(),
            warnings: Vec::new(),
        };
        
        let errors = DraftContentParser::validate_result(&parse_result);
        assert!(!errors.is_empty()); // 应该有多个验证错误
        assert!(errors.iter().any(|e| e.contains("模板名称不能为空")));
        assert!(errors.iter().any(|e| e.contains("模板时长不能为0")));
        assert!(errors.iter().any(|e| e.contains("帧率必须大于0")));
        assert!(errors.iter().any(|e| e.contains("画布尺寸不能为0")));
    }

    #[test]
    fn test_template_name_inference() {
        let content = get_test_draft_content();
        
        // 测试从文件路径推断模板名称
        let result = DraftContentParser::parse_content(
            &content, 
            None, 
            Some("/path/to/my_template.json".to_string())
        );
        
        assert!(result.is_ok());
        let parse_result = result.unwrap();
        assert_eq!(parse_result.template.name, "my_template");
        
        // 测试使用自定义名称
        let result = DraftContentParser::parse_content(
            &content, 
            Some("自定义模板名称".to_string()), 
            Some("/path/to/my_template.json".to_string())
        );
        
        assert!(result.is_ok());
        let parse_result = result.unwrap();
        assert_eq!(parse_result.template.name, "自定义模板名称");
    }

    #[test]
    fn test_material_file_existence_check() {
        let temp_dir = create_temp_dir();
        
        // 创建一个存在的测试文件
        let existing_file = create_test_video_file(&temp_dir, "existing_video.mp4");
        
        // 修改测试内容，使用存在的文件路径
        let mut content = get_test_draft_content();
        content = content.replace("test_video.mp4", existing_file.to_str().unwrap());
        
        let result = DraftContentParser::parse_content(&content, None, None);
        assert!(result.is_ok());
        
        let parse_result = result.unwrap();
        
        // 存在的文件不应该在missing_files列表中
        assert!(!parse_result.missing_files.contains(&existing_file.to_string_lossy().to_string()));
        
        // 但其他不存在的文件应该在列表中
        assert!(parse_result.missing_files.iter().any(|f| f.contains("test_audio.mp3")));
        assert!(parse_result.missing_files.iter().any(|f| f.contains("test_image.jpg")));
    }
}
