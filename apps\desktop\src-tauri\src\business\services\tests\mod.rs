pub mod draft_parser_tests;
pub mod cloud_upload_service_tests;
pub mod watermark_tests;
pub mod template_service_tests;
pub mod template_foreign_key_test;

// 测试工具函数
pub mod test_utils {
    use std::path::PathBuf;
    use tempfile::TempDir;
    use std::fs;

    /// 创建临时测试目录
    pub fn create_temp_dir() -> TempDir {
        tempfile::tempdir().expect("Failed to create temp dir")
    }

    /// 创建测试用的draft_content.json文件
    pub fn create_test_draft_file(temp_dir: &TempDir, content: &str) -> PathBuf {
        let file_path = temp_dir.path().join("draft_content.json");
        fs::write(&file_path, content).expect("Failed to write test file");
        file_path
    }

    /// 创建测试用的视频文件
    pub fn create_test_video_file(temp_dir: &TempDir, filename: &str) -> PathBuf {
        let file_path = temp_dir.path().join(filename);
        fs::write(&file_path, b"fake video content").expect("Failed to write test video file");
        file_path
    }

    /// 获取测试用的draft_content.json内容
    pub fn get_test_draft_content() -> String {
        serde_json::json!({
            "id": "test-template-id",
            "canvas_config": {
                "width": 1920,
                "height": 1080,
                "ratio": "16:9"
            },
            "duration": 30000000,
            "fps": 30.0,
            "materials": {
                "videos": [
                    {
                        "id": "video-1",
                        "material_name": "test_video.mp4",
                        "path": "test_video.mp4",
                        "duration": 15000000,
                        "width": 1920,
                        "height": 1080,
                        "has_audio": true,
                        "type": "video"
                    }
                ],
                "audios": [
                    {
                        "id": "audio-1",
                        "name": "test_audio.mp3",
                        "path": "test_audio.mp3",
                        "duration": 30000000,
                        "type": "audio"
                    }
                ],
                "images": [
                    {
                        "id": "image-1",
                        "material_name": "test_image.jpg",
                        "path": "test_image.jpg",
                        "width": 1920,
                        "height": 1080,
                        "type": "image"
                    }
                ],
                "texts": [
                    {
                        "id": "text-1",
                        "content": "测试文本",
                        "font_family": "Arial",
                        "font_size": 24.0,
                        "color": "#FFFFFF"
                    }
                ]
            },
            "tracks": [
                {
                    "id": "track-1",
                    "type": "video",
                    "segments": [
                        {
                            "id": "segment-1",
                            "material_id": "video-1",
                            "start": 0,
                            "duration": 15000000,
                            "target_timerange": {
                                "start": 0,
                                "duration": 15000000
                            }
                        }
                    ]
                },
                {
                    "id": "track-2",
                    "type": "audio",
                    "segments": [
                        {
                            "id": "segment-2",
                            "material_id": "audio-1",
                            "start": 0,
                            "duration": 30000000,
                            "target_timerange": {
                                "start": 0,
                                "duration": 30000000
                            }
                        }
                    ]
                }
            ]
        }).to_string()
    }

    /// 获取无效的draft_content.json内容
    pub fn get_invalid_draft_content() -> String {
        r#"{
            "invalid": "json"
        }"#.to_string()
    }

    /// 获取缺失文件的draft_content.json内容
    pub fn get_missing_files_draft_content() -> String {
        r#"{
            "id": "test-template-id",
            "canvas_config": {
                "width": 1920,
                "height": 1080,
                "ratio": "16:9"
            },
            "duration": 30000000,
            "fps": 30.0,
            "materials": {
                "videos": [
                    {
                        "id": "video-1",
                        "material_name": "missing_video.mp4",
                        "path": "missing_video.mp4",
                        "duration": 15000000,
                        "width": 1920,
                        "height": 1080,
                        "has_audio": true,
                        "type": "video"
                    }
                ]
            }
        }"#.to_string()
    }
}
