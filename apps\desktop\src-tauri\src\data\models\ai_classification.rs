use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// AI分类数据模型
/// 遵循 Tauri 开发规范的数据模型设计原则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiClassification {
    /// 分类唯一标识符
    pub id: String,
    /// 分类名称
    pub name: String,
    /// 分类提示词
    pub prompt_text: String,
    /// 分类描述
    pub description: Option<String>,
    /// 是否激活
    pub is_active: bool,
    /// 排序顺序
    pub sort_order: i32,
    /// 匹配权重（用于按顺序匹配，数值越大优先级越高）
    pub weight: i32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建AI分类请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAiClassificationRequest {
    /// 分类名称
    pub name: String,
    /// 分类提示词
    pub prompt_text: String,
    /// 分类描述
    pub description: Option<String>,
    /// 排序顺序
    pub sort_order: Option<i32>,
    /// 匹配权重
    pub weight: Option<i32>,
}

/// 更新AI分类请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAiClassificationRequest {
    /// 分类名称
    pub name: Option<String>,
    /// 分类提示词
    pub prompt_text: Option<String>,
    /// 分类描述
    pub description: Option<String>,
    /// 是否激活
    pub is_active: Option<bool>,
    /// 排序顺序
    pub sort_order: Option<i32>,
    /// 匹配权重
    pub weight: Option<i32>,
}

/// AI分类查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiClassificationQuery {
    /// 是否只查询激活的分类
    pub active_only: Option<bool>,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向
    pub sort_order: Option<String>,
    /// 分页大小
    pub limit: Option<i32>,
    /// 分页偏移
    pub offset: Option<i32>,
}

impl Default for AiClassificationQuery {
    fn default() -> Self {
        Self {
            active_only: Some(true),
            sort_by: Some("sort_order".to_string()),
            sort_order: Some("ASC".to_string()),
            limit: None,
            offset: None,
        }
    }
}

/// AI分类预览数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiClassificationPreview {
    /// 分类列表
    pub classifications: Vec<AiClassification>,
    /// 生成的完整提示词
    pub full_prompt: String,
}

impl AiClassification {
    /// 创建新的AI分类实例
    pub fn new(
        id: String,
        name: String,
        prompt_text: String,
        description: Option<String>,
        sort_order: i32,
        weight: i32,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            name,
            prompt_text,
            description,
            is_active: true,
            sort_order,
            weight,
            created_at: now,
            updated_at: now,
        }
    }

    /// 更新分类信息
    pub fn update(&mut self, request: UpdateAiClassificationRequest) {
        if let Some(name) = request.name {
            self.name = name;
        }
        if let Some(prompt_text) = request.prompt_text {
            self.prompt_text = prompt_text;
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(is_active) = request.is_active {
            self.is_active = is_active;
        }
        if let Some(sort_order) = request.sort_order {
            self.sort_order = sort_order;
        }
        if let Some(weight) = request.weight {
            self.weight = weight;
        }
        self.updated_at = Utc::now();
    }

    /// 生成分类字符串（用于提示词模板）
    pub fn to_category_string(&self) -> String {
        format!("**{}**: {}", self.name, self.prompt_text)
    }
}

/// 生成完整的AI分类提示词
pub fn generate_full_prompt(classifications: &[AiClassification]) -> String {
    let active_classifications: Vec<&AiClassification> = classifications
        .iter()
        .filter(|c| c.is_active)
        .collect();

    if active_classifications.is_empty() {
        return "暂无激活的分类，无法生成提示词".to_string();
    }

    // 生成分类名称列表
    let category_names = active_classifications
        .iter()
        .map(|c| c.name.clone())
        .collect::<Vec<_>>()
        .join("\n   - ");

    // 生成详细分类描述
    let categories_str = active_classifications
        .iter()
        .map(|c| c.to_category_string())
        .collect::<Vec<_>>()
        .join("\n   - ");

    format!(
        r#"请分析这个视频的内容，并将其分类到以下类别之一：
   - {}

请按以下步骤进行分析：

1. **冲突处理**：
   - 按最大可见面积分类

2. **内容分类**（仅对包含目标商品且质量合格的视频）：
   - {}

请返回JSON格式的结果：

{{
    "category": "分类结果",
    "confidence": 0.85,
    "reasoning": "详细的分类理由，包括商品匹配情况和内容特征",
    "features": ["观察到的关键特征1", "关键特征2", "关键特征3"],
    "product_match": true/false,
    "quality_score": 0.9
}}

**分类优先级**：
1. 商品匹配 > 内容分类
2. 质量合格 > 内容丰富
3. 明确分类 > 模糊归类

请仔细观察视频内容，确保分类准确性。"#,
        category_names,
        categories_str
    )
}
