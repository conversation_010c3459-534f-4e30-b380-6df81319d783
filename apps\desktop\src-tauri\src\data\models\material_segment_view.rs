use serde::{Deserialize, Serialize};
use crate::data::models::material::MaterialSegment;
use std::collections::HashMap;

/// MaterialSegment聚合视图数据模型
/// 遵循 Tauri 开发规范的数据模型设计原则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaterialSegmentView {
    /// 项目ID
    pub project_id: String,
    /// 按AI分类聚合的片段
    pub by_classification: Vec<ClassificationGroup>,
    /// 按模特聚合的片段
    pub by_model: Vec<ModelGroup>,
    /// 统计信息
    pub stats: MaterialSegmentStats,
}

/// AI分类分组
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassificationGroup {
    /// 分类名称
    pub category: String,
    /// 分类下的片段数量
    pub segment_count: usize,
    /// 分类下的片段总时长（秒）
    pub total_duration: f64,
    /// 分类下的片段列表
    pub segments: Vec<SegmentWithDetails>,
}

/// 模特分组
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ModelGroup {
    /// 模特ID
    pub model_id: String,
    /// 模特名称
    pub model_name: String,
    /// 模特下的片段数量
    pub segment_count: usize,
    /// 模特下的片段总时长（秒）
    pub total_duration: f64,
    /// 模特下的片段列表
    pub segments: Vec<SegmentWithDetails>,
}

/// 带详细信息的片段
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SegmentWithDetails {
    /// 片段信息
    pub segment: MaterialSegment,
    /// 素材名称
    pub material_name: String,
    /// 素材类型
    pub material_type: String,
    /// 分类信息
    pub classification: Option<ClassificationInfo>,
    /// 模特信息
    pub model: Option<ModelInfo>,
}

/// 分类信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassificationInfo {
    /// 分类名称
    pub category: String,
    /// 置信度
    pub confidence: f64,
    /// 分类理由
    pub reasoning: String,
    /// 关键特征
    pub features: Vec<String>,
    /// 商品匹配度
    pub product_match: bool,
    /// 质量评分
    pub quality_score: f64,
}

/// 模特信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelInfo {
    /// 模特ID
    pub id: String,
    /// 模特名称
    pub name: String,
    /// 模特类型
    pub model_type: String,
}

/// 片段统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaterialSegmentStats {
    /// 总片段数
    pub total_segments: usize,
    /// 已分类片段数
    pub classified_segments: usize,
    /// 未分类片段数
    pub unclassified_segments: usize,
    /// 分类覆盖率
    pub classification_coverage: f64,
    /// 分类统计
    pub classification_counts: HashMap<String, usize>,
    /// 模特统计
    pub model_counts: HashMap<String, usize>,
    /// 总时长（秒）
    pub total_duration: f64,
}

/// 片段查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaterialSegmentQuery {
    /// 项目ID
    pub project_id: String,
    /// 分类过滤
    pub category_filter: Option<String>,
    /// 模特ID过滤
    pub model_id_filter: Option<String>,
    /// 最小时长过滤（秒）
    pub min_duration: Option<f64>,
    /// 最大时长过滤（秒）
    pub max_duration: Option<f64>,
    /// 搜索关键词
    pub search_term: Option<String>,
    /// 排序字段
    pub sort_by: Option<SegmentSortField>,
    /// 排序方向
    pub sort_direction: Option<SortDirection>,
    /// 分页大小
    pub page_size: Option<usize>,
    /// 页码
    pub page: Option<usize>,
}

/// 片段排序字段
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SegmentSortField {
    /// 创建时间
    CreatedAt,
    /// 时长
    Duration,
    /// 分类
    Category,
    /// 模特
    Model,
    /// 置信度
    Confidence,
}

/// 排序方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortDirection {
    /// 升序
    Ascending,
    /// 降序
    Descending,
}

impl MaterialSegmentView {
    /// 创建新的MaterialSegment聚合视图
    pub fn new(project_id: String) -> Self {
        Self {
            project_id,
            by_classification: Vec::new(),
            by_model: Vec::new(),
            stats: MaterialSegmentStats {
                total_segments: 0,
                classified_segments: 0,
                unclassified_segments: 0,
                classification_coverage: 0.0,
                classification_counts: HashMap::new(),
                model_counts: HashMap::new(),
                total_duration: 0.0,
            },
        }
    }
}
