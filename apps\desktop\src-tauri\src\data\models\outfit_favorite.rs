use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::data::models::outfit_recommendation::OutfitRecommendation;

/// 收藏的穿搭方案
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutfitFavorite {
    /// 收藏ID
    pub id: String,
    /// 原始方案数据（JSON序列化存储）
    pub recommendation_data: OutfitRecommendation,
    /// 用户自定义名称
    pub custom_name: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

impl OutfitFavorite {
    /// 创建新的收藏方案
    pub fn new(recommendation: OutfitRecommendation, custom_name: Option<String>) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            recommendation_data: recommendation,
            custom_name,
            created_at: Utc::now(),
        }
    }

    /// 获取显示名称（优先使用自定义名称，否则使用原方案标题）
    pub fn display_name(&self) -> &str {
        self.custom_name.as_ref().unwrap_or(&self.recommendation_data.title)
    }

    /// 获取方案描述
    pub fn description(&self) -> &str {
        &self.recommendation_data.description
    }

    /// 获取方案风格标签
    pub fn style_tags(&self) -> &[String] {
        &self.recommendation_data.style_tags
    }

    /// 获取适合场合
    pub fn occasions(&self) -> &[String] {
        &self.recommendation_data.occasions
    }
}

/// 收藏方案请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SaveOutfitToFavoritesRequest {
    /// 要收藏的方案
    pub recommendation: OutfitRecommendation,
    /// 用户自定义名称
    pub custom_name: Option<String>,
}

/// 收藏方案响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SaveOutfitToFavoritesResponse {
    /// 收藏ID
    pub favorite_id: String,
    /// 收藏成功的方案
    pub favorite: OutfitFavorite,
}

/// 获取收藏列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetFavoriteOutfitsResponse {
    /// 收藏方案列表
    pub favorites: Vec<OutfitFavorite>,
    /// 总数量
    pub total_count: usize,
}

/// 基于收藏方案的素材检索请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchMaterialsByFavoriteRequest {
    /// 收藏方案ID
    pub favorite_id: String,
    /// 分页信息
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
}

/// 方案对比请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompareOutfitFavoritesRequest {
    /// 第一个收藏方案ID
    pub favorite_id_1: String,
    /// 第二个收藏方案ID
    pub favorite_id_2: String,
    /// 分页信息
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
}

/// 方案对比响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompareOutfitFavoritesResponse {
    /// 第一个方案信息
    pub favorite_1: OutfitFavorite,
    /// 第一个方案的检索结果
    pub materials_1: crate::data::models::material_search::MaterialSearchResponse,
    /// 第二个方案信息
    pub favorite_2: OutfitFavorite,
    /// 第二个方案的检索结果
    pub materials_2: crate::data::models::material_search::MaterialSearchResponse,
    /// 对比生成时间
    pub compared_at: DateTime<Utc>,
}
