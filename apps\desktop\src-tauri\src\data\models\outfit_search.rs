use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use crate::data::models::gemini_analysis::ColorHSV;

/// 搜索相关性阈值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RelevanceThreshold {
    #[serde(rename = "LOWEST")]
    Lowest,
    #[serde(rename = "LOW")]
    Low,
    #[serde(rename = "MEDIUM")]
    Medium,
    #[serde(rename = "HIGH")]
    High,
}

impl RelevanceThreshold {
    /// 获取阈值对应的数值
    pub fn to_value(&self) -> f64 {
        match self {
            RelevanceThreshold::Lowest => 0.3,
            RelevanceThreshold::Low => 0.5,
            RelevanceThreshold::Medium => 0.7,
            RelevanceThreshold::High => 0.9,
        }
    }
}

impl Default for RelevanceThreshold {
    fn default() -> Self {
        RelevanceThreshold::High
    }
}

/// 颜色过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorFilter {
    /// 是否启用颜色过滤
    pub enabled: bool,
    /// 目标颜色
    pub color: ColorHSV,
    /// 色相阈值
    pub hue_threshold: f64,
    /// 饱和度阈值
    pub saturation_threshold: f64,
    /// 明度阈值
    pub value_threshold: f64,
}

impl Default for ColorFilter {
    fn default() -> Self {
        Self {
            enabled: false,
            color: ColorHSV::new(0.0, 0.0, 0.0),
            hue_threshold: 0.05,
            saturation_threshold: 0.05,
            value_threshold: 0.20,
        }
    }
}

/// 搜索配置
/// 扩展配置以支持更复杂的搜索过滤逻辑
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchConfig {
    /// 相关性阈值
    pub relevance_threshold: RelevanceThreshold,
    /// 环境标签过滤
    pub environments: Vec<String>,
    /// 类别过滤
    pub categories: Vec<String>,
    /// 颜色过滤器（按类别）
    pub color_filters: HashMap<String, ColorFilter>,
    /// 设计风格过滤（按类别）
    pub design_styles: HashMap<String, Vec<String>>,
    /// 最大关键词数量
    pub max_keywords: usize,
    /// 是否启用调试模式
    pub debug_mode: bool,
    /// 自定义过滤器字符串
    pub custom_filters: Vec<String>,
    /// 查询增强模式
    pub query_enhancement_enabled: bool,
    /// 颜色阈值配置
    pub color_thresholds: ColorThresholds,
}

/// 颜色阈值全局配置
/// 参考 Python 实现中的阈值设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorThresholds {
    /// 默认色相阈值
    pub default_hue_threshold: f64,
    /// 默认饱和度阈值
    pub default_saturation_threshold: f64,
    /// 默认明度阈值
    pub default_value_threshold: f64,
}

impl Default for ColorThresholds {
    fn default() -> Self {
        Self {
            default_hue_threshold: 0.05,
            default_saturation_threshold: 0.05,
            default_value_threshold: 0.20,
        }
    }
}

impl Default for SearchConfig {
    fn default() -> Self {
        Self {
            relevance_threshold: RelevanceThreshold::default(),
            environments: Vec::new(),
            categories: Vec::new(),
            color_filters: HashMap::new(),
            design_styles: HashMap::new(),
            max_keywords: 10,
            debug_mode: false,
            custom_filters: Vec::new(),
            query_enhancement_enabled: true,
            color_thresholds: ColorThresholds::default(),
        }
    }
}

/// 搜索请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchRequest {
    /// 搜索查询字符串
    pub query: String,
    /// 搜索配置
    pub config: SearchConfig,
    /// 页面大小
    pub page_size: usize,
    /// 页面偏移量
    pub page_offset: usize,
}

impl Default for SearchRequest {
    fn default() -> Self {
        Self {
            query: "model".to_string(),
            config: SearchConfig::default(),
            page_size: 9,
            page_offset: 0,
        }
    }
}

/// 搜索结果中的产品信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductInfo {
    /// 产品类别
    pub category: String,
    /// 产品描述
    pub description: String,
    /// 主要颜色
    pub color_pattern: ColorHSV,
    /// 设计风格
    pub design_styles: Vec<String>,
}

/// 单个搜索结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    /// 结果ID
    pub id: String,
    /// 图片URL
    pub image_url: String,
    /// 风格描述
    pub style_description: String,
    /// 环境标签
    pub environment_tags: Vec<String>,
    /// 产品信息列表
    pub products: Vec<ProductInfo>,
    /// 相关性评分
    pub relevance_score: f64,
}

/// 搜索响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResponse {
    /// 搜索结果列表
    pub results: Vec<SearchResult>,
    /// 总结果数量
    pub total_size: usize,
    /// 下一页令牌
    pub next_page_token: Option<String>,
    /// 搜索耗时（毫秒）
    pub search_time_ms: u64,
    /// 搜索时间戳
    pub searched_at: DateTime<Utc>,
}

/// 搜索历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchHistory {
    /// 历史记录ID
    pub id: String,
    /// 搜索查询
    pub query: String,
    /// 搜索配置
    pub config: SearchConfig,
    /// 结果数量
    pub results_count: usize,
    /// 搜索耗时（毫秒）
    pub search_time_ms: u64,
    /// 搜索时间
    pub created_at: DateTime<Utc>,
}

/// LLM问答请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMQueryRequest {
    /// 用户输入的情景描述
    pub user_input: String,
    /// 会话ID（可选）
    pub session_id: Option<String>,
}

/// LLM问答响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMQueryResponse {
    /// LLM回答内容
    pub answer: String,
    /// 相关的搜索结果
    pub related_results: Vec<SearchResult>,
    /// 响应时间（毫秒）
    pub response_time_ms: u64,
    /// 响应时间戳
    pub responded_at: DateTime<Utc>,
}

/// 全局配置
#[derive(Debug, Clone)]
pub struct OutfitSearchGlobalConfig {
    /// Google Cloud项目ID
    pub google_project_id: String,
    /// Vertex AI应用ID
    pub vertex_ai_app_id: String,
    /// 存储桶名称
    pub storage_bucket_name: String,
    /// 数据存储ID
    pub data_store_id: String,
    /// Cloudflare项目ID
    pub cloudflare_project_id: String,
    /// Cloudflare网关ID
    pub cloudflare_gateway_id: String,
}

impl Default for OutfitSearchGlobalConfig {
    fn default() -> Self {
        Self {
            google_project_id: "gen-lang-client-0413414134".to_string(),
            vertex_ai_app_id: "jeans-search_1751353769585".to_string(),
            storage_bucket_name: "fashion_image_block".to_string(),
            data_store_id: "jeans_pattern_data_store".to_string(),
            cloudflare_project_id: "67720b647ff2b55cf37ba3ef9e677083".to_string(),
            cloudflare_gateway_id: "bowong-dev".to_string(),
        }
    }
}

/// 搜索过滤器构建器
/// 基于 Google Cloud Search API 规范实现复杂过滤器构建
pub struct SearchFilterBuilder;

impl SearchFilterBuilder {
    /// 构建搜索过滤器字符串
    /// 参考 Python 实现，支持复杂的嵌套过滤逻辑
    pub fn build_filters(config: &SearchConfig) -> String {
        let mut filters = Vec::new();

        // 类别过滤 - 每个类别创建独立的过滤器组
        if !config.categories.is_empty() {
            for category in &config.categories {
                let category_filter = Self::build_category_filter(category, config);
                if !category_filter.is_empty() {
                    filters.push(category_filter);
                }
            }
        }

        // 环境标签过滤 - 独立的环境过滤器
        if !config.environments.is_empty() {
            let env_filter = Self::build_environment_filter(&config.environments);
            filters.push(env_filter);
        }

        // 使用 AND 连接所有过滤器组
        let result = filters.join(" AND ");

        // 调试日志
        if !result.is_empty() {
            eprintln!("构建的过滤器字符串: {}", result);
        }

        result
    }

    /// 为单个类别构建过滤器组
    /// 类似 Python 中的 inner_filters 逻辑
    fn build_category_filter(category: &str, config: &SearchConfig) -> String {
        let mut inner_filters = vec![
            format!("products.category: ANY(\"{}\")", category)
        ];

        // 颜色检测过滤
        if let Some(color_filter) = config.color_filters.get(category) {
            if color_filter.enabled {
                let color_filters = Self::build_color_filters(color_filter);
                inner_filters.extend(color_filters);
            }
        }

        // 设计风格过滤
        if let Some(styles) = config.design_styles.get(category) {
            if !styles.is_empty() {
                let styles_filter = Self::build_design_styles_filter(styles);
                inner_filters.push(styles_filter);
            }
        }

        // 返回括号包围的 AND 组合
        if inner_filters.len() > 1 {
            format!("({})", inner_filters.join(" AND "))
        } else {
            inner_filters.into_iter().next().unwrap_or_default()
        }
    }

    /// 构建环境标签过滤器
    fn build_environment_filter(environments: &[String]) -> String {
        let env_str = environments.iter()
            .map(|e| format!("\"{}\"", e))
            .collect::<Vec<_>>()
            .join(",");
        format!("environment_tags: ANY({})", env_str)
    }

    /// 构建设计风格过滤器
    fn build_design_styles_filter(styles: &[String]) -> String {
        let styles_str = styles.iter()
            .map(|s| format!("\"{}\"", s))
            .collect::<Vec<_>>()
            .join(",");
        format!("products.design_styles: ANY({})", styles_str)
    }

    /// 构建颜色过滤器
    /// 参考 Python 实现的 HSV 颜色范围过滤
    fn build_color_filters(color_filter: &ColorFilter) -> Vec<String> {
        let hsv = &color_filter.color;

        // 计算颜色范围，确保在 [0, 1] 区间内
        let hue_min = (hsv.hue - color_filter.hue_threshold).max(0.0);
        let hue_max = (hsv.hue + color_filter.hue_threshold).min(1.0);
        let sat_min = (hsv.saturation - color_filter.saturation_threshold).max(0.0);
        let sat_max = (hsv.saturation + color_filter.saturation_threshold).min(1.0);
        let val_min = (hsv.value - color_filter.value_threshold).max(0.0);
        let val_max = (hsv.value + color_filter.value_threshold).min(1.0);

        vec![
            format!("products.color_pattern.Hue: IN({}, {})", hue_min, hue_max),
            format!("products.color_pattern.Saturation: IN({}, {})", sat_min, sat_max),
            format!("products.color_pattern.Value: IN({}, {})", val_min, val_max),
        ]
    }

    /// 构建查询关键词
    /// 参考 Python 实现，支持关键词优先级和数量限制
    pub fn build_query_keywords(config: &SearchConfig) -> Vec<String> {
        let mut keywords = Vec::new();

        // 优先添加环境关键词（高优先级）
        keywords.extend(config.environments.clone());

        // 添加设计风格关键词（按类别添加）
        for styles in config.design_styles.values() {
            keywords.extend(styles.clone());
        }

        // 限制关键词数量，参考 Python 中的 max_keywords 逻辑
        if keywords.len() > config.max_keywords {
            keywords.truncate(config.max_keywords);
        }

        // 调试日志
        if !keywords.is_empty() {
            eprintln!("构建的查询关键词: {:?}", keywords);
        }

        keywords
    }

    /// 构建增强的查询字符串
    /// 参考 Python 实现，将基础查询与关键词组合
    pub fn build_enhanced_query(base_query: &str, config: &SearchConfig) -> String {
        // 如果查询增强被禁用，直接返回原始查询
        if !config.query_enhancement_enabled {
            return base_query.to_string();
        }

        let keywords = Self::build_query_keywords(config);

        let result = if keywords.is_empty() {
            base_query.to_string()
        } else {
            let keywords_str = keywords.join(" ");
            if base_query.trim().is_empty() {
                format!("model {}", keywords_str)
            } else {
                format!("{} {}", base_query.trim(), keywords_str)
            }
        };

        // 调试日志
        if config.debug_mode {
            eprintln!("查询构建详情:");
            eprintln!("  原始查询: '{}'", base_query);
            eprintln!("  关键词: {:?}", keywords);
            eprintln!("  查询增强: {}", config.query_enhancement_enabled);
            eprintln!("  最终查询: '{}'", result);
        }

        result
    }

    /// 验证搜索配置
    /// 检查配置的有效性并提供调试信息
    pub fn validate_config(config: &SearchConfig) -> Result<(), String> {
        // 检查类别配置
        if config.categories.is_empty() && config.environments.is_empty() {
            if config.debug_mode {
                eprintln!("警告: 没有设置任何类别或环境过滤器");
            }
        }

        // 检查颜色过滤器配置
        for (category, color_filter) in &config.color_filters {
            if color_filter.enabled {
                if !config.categories.contains(category) {
                    return Err(format!("颜色过滤器类别 '{}' 不在选定类别中", category));
                }

                // 验证颜色值范围
                let hsv = &color_filter.color;
                if hsv.hue < 0.0 || hsv.hue > 1.0 ||
                   hsv.saturation < 0.0 || hsv.saturation > 1.0 ||
                   hsv.value < 0.0 || hsv.value > 1.0 {
                    return Err(format!("类别 '{}' 的颜色值超出有效范围 [0, 1]", category));
                }
            }
        }

        // 检查设计风格配置
        for (category, styles) in &config.design_styles {
            if !styles.is_empty() && !config.categories.contains(category) {
                return Err(format!("设计风格类别 '{}' 不在选定类别中", category));
            }
        }

        if config.debug_mode {
            eprintln!("搜索配置验证通过");
        }

        Ok(())
    }

    /// 生成搜索配置摘要
    /// 用于调试和日志记录
    pub fn generate_config_summary(config: &SearchConfig) -> String {
        let mut summary = Vec::new();

        summary.push(format!("相关性阈值: {:?}", config.relevance_threshold));

        if !config.categories.is_empty() {
            summary.push(format!("类别: {:?}", config.categories));
        }

        if !config.environments.is_empty() {
            summary.push(format!("环境: {:?}", config.environments));
        }

        let active_color_filters: Vec<_> = config.color_filters.iter()
            .filter(|(_, filter)| filter.enabled)
            .map(|(category, _)| category.clone())
            .collect();
        if !active_color_filters.is_empty() {
            summary.push(format!("颜色过滤: {:?}", active_color_filters));
        }

        let active_style_filters: Vec<_> = config.design_styles.iter()
            .filter(|(_, styles)| !styles.is_empty())
            .map(|(category, styles)| format!("{}:{:?}", category, styles))
            .collect();
        if !active_style_filters.is_empty() {
            summary.push(format!("设计风格: [{}]", active_style_filters.join(", ")));
        }

        if !config.custom_filters.is_empty() {
            summary.push(format!("自定义过滤器: {:?}", config.custom_filters));
        }

        summary.push(format!("最大关键词: {}", config.max_keywords));
        summary.push(format!("查询增强: {}", config.query_enhancement_enabled));

        summary.join("; ")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::data::models::gemini_analysis::ColorHSV;

    #[test]
    fn test_relevance_threshold_values() {
        assert_eq!(RelevanceThreshold::Lowest.to_value(), 0.3);
        assert_eq!(RelevanceThreshold::Low.to_value(), 0.5);
        assert_eq!(RelevanceThreshold::Medium.to_value(), 0.7);
        assert_eq!(RelevanceThreshold::High.to_value(), 0.9);
    }

    #[test]
    fn test_color_filter_default() {
        let filter = ColorFilter::default();
        assert!(!filter.enabled);
        assert_eq!(filter.hue_threshold, 0.05);
        assert_eq!(filter.saturation_threshold, 0.05);
        assert_eq!(filter.value_threshold, 0.20);
    }

    #[test]
    fn test_search_config_default() {
        let config = SearchConfig::default();
        assert!(matches!(config.relevance_threshold, RelevanceThreshold::High));
        assert!(config.categories.is_empty());
        assert!(config.environments.is_empty());
        assert!(config.color_filters.is_empty());
        assert!(config.design_styles.is_empty());
        assert_eq!(config.max_keywords, 10);
        assert!(!config.debug_mode);
        assert!(config.custom_filters.is_empty());
        assert!(config.query_enhancement_enabled);
    }

    #[test]
    fn test_search_request_default() {
        let request = SearchRequest::default();
        assert_eq!(request.query, "model");
        assert_eq!(request.page_size, 9);
        assert_eq!(request.page_offset, 0);
    }

    #[test]
    fn test_search_filter_builder_empty_config() {
        let config = SearchConfig::default();
        let filters = SearchFilterBuilder::build_filters(&config);
        assert!(filters.is_empty());
    }

    #[test]
    fn test_search_filter_builder_with_categories() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string(), "下装".to_string()];

        let filters = SearchFilterBuilder::build_filters(&config);
        assert!(filters.contains("products.category: ANY(\"上装\")"));
        assert!(filters.contains("products.category: ANY(\"下装\")"));
    }

    #[test]
    fn test_search_filter_builder_with_environments() {
        let mut config = SearchConfig::default();
        config.environments = vec!["Outdoor".to_string(), "Indoor".to_string()];

        let filters = SearchFilterBuilder::build_filters(&config);
        assert!(filters.contains("environment_tags: ANY(\"Outdoor\",\"Indoor\")"));
    }

    #[test]
    fn test_search_filter_builder_with_color_filters() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];

        let color_filter = ColorFilter {
            enabled: true,
            color: ColorHSV::new(0.5, 0.8, 0.9),
            hue_threshold: 0.05,
            saturation_threshold: 0.05,
            value_threshold: 0.20,
        };
        config.color_filters.insert("上装".to_string(), color_filter);

        let filters = SearchFilterBuilder::build_filters(&config);
        assert!(filters.contains("products.color_pattern.Hue: IN("));
        assert!(filters.contains("products.color_pattern.Saturation: IN("));
        assert!(filters.contains("products.color_pattern.Value: IN("));
    }

    #[test]
    fn test_search_filter_builder_with_design_styles() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];
        config.design_styles.insert("上装".to_string(), vec!["休闲".to_string(), "正式".to_string()]);

        let filters = SearchFilterBuilder::build_filters(&config);
        assert!(filters.contains("products.design_styles: ANY(\"休闲\",\"正式\")"));
    }

    #[test]
    fn test_query_keywords_builder() {
        let mut config = SearchConfig::default();
        config.design_styles.insert("上装".to_string(), vec!["休闲".to_string(), "正式".to_string()]);
        config.environments = vec!["Outdoor".to_string()];

        let keywords = SearchFilterBuilder::build_query_keywords(&config);
        assert!(keywords.contains(&"休闲".to_string()));
        assert!(keywords.contains(&"正式".to_string()));
        assert!(keywords.contains(&"Outdoor".to_string()));
    }

    #[test]
    fn test_query_keywords_builder_max_limit() {
        let mut config = SearchConfig::default();
        config.max_keywords = 3;

        // 添加超过限制的关键词
        config.design_styles.insert("上装".to_string(), vec!["休闲".to_string(), "正式".to_string()]);
        config.design_styles.insert("下装".to_string(), vec!["运动".to_string(), "街头".to_string()]);
        config.environments = vec!["Outdoor".to_string(), "Indoor".to_string()];

        let keywords = SearchFilterBuilder::build_query_keywords(&config);
        assert!(keywords.len() <= 3);
    }

    #[test]
    fn test_product_info_creation() {
        let color = ColorHSV::new(0.6, 0.5, 0.7);
        let product = ProductInfo {
            category: "牛仔裤".to_string(),
            description: "蓝色牛仔裤".to_string(),
            color_pattern: color.clone(),
            design_styles: vec!["休闲".to_string()],
        };

        assert_eq!(product.category, "牛仔裤");
        assert_eq!(product.description, "蓝色牛仔裤");
        assert_eq!(product.color_pattern, color);
        assert_eq!(product.design_styles.len(), 1);
    }

    #[test]
    fn test_search_result_creation() {
        let color = ColorHSV::new(0.6, 0.5, 0.7);
        let product = ProductInfo {
            category: "上装".to_string(),
            description: "白色衬衫".to_string(),
            color_pattern: color,
            design_styles: vec!["正式".to_string()],
        };

        let result = SearchResult {
            id: "test-id".to_string(),
            image_url: "https://example.com/image.jpg".to_string(),
            style_description: "商务风格".to_string(),
            environment_tags: vec!["Office".to_string()],
            products: vec![product],
            relevance_score: 0.85,
        };

        assert_eq!(result.id, "test-id");
        assert_eq!(result.style_description, "商务风格");
        assert_eq!(result.relevance_score, 0.85);
        assert_eq!(result.products.len(), 1);
        assert_eq!(result.environment_tags.len(), 1);
    }

    #[test]
    fn test_llm_query_request() {
        let request = LLMQueryRequest {
            user_input: "如何搭配牛仔裤？".to_string(),
            session_id: Some("session-123".to_string()),
        };

        assert_eq!(request.user_input, "如何搭配牛仔裤？");
        assert_eq!(request.session_id, Some("session-123".to_string()));
    }

    // 增强过滤器功能测试
    #[test]
    fn test_enhanced_filter_builder_complex_category() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];

        // 添加颜色过滤器
        let color_filter = ColorFilter {
            enabled: true,
            color: ColorHSV::new(0.5, 0.8, 0.9),
            hue_threshold: 0.05,
            saturation_threshold: 0.05,
            value_threshold: 0.20,
        };
        config.color_filters.insert("上装".to_string(), color_filter);

        // 添加设计风格
        config.design_styles.insert("上装".to_string(), vec!["休闲".to_string(), "正式".to_string()]);

        let filters = SearchFilterBuilder::build_filters(&config);

        // 验证包含所有过滤条件
        assert!(filters.contains("products.category: ANY(\"上装\")"));
        assert!(filters.contains("products.color_pattern.Hue: IN("));
        assert!(filters.contains("products.color_pattern.Saturation: IN("));
        assert!(filters.contains("products.color_pattern.Value: IN("));
        assert!(filters.contains("products.design_styles: ANY(\"休闲\",\"正式\")"));

        // 验证括号结构
        assert!(filters.contains("(") && filters.contains(")"));
    }

    #[test]
    fn test_enhanced_query_builder() {
        let mut config = SearchConfig::default();
        config.environments = vec!["Outdoor".to_string()];
        config.design_styles.insert("上装".to_string(), vec!["休闲".to_string()]);
        config.max_keywords = 5;

        let enhanced_query = SearchFilterBuilder::build_enhanced_query("牛仔裤", &config);

        assert!(enhanced_query.contains("牛仔裤"));
        assert!(enhanced_query.contains("Outdoor"));
        assert!(enhanced_query.contains("休闲"));
    }

    #[test]
    fn test_enhanced_query_builder_empty_base() {
        let mut config = SearchConfig::default();
        config.environments = vec!["Outdoor".to_string()];

        let enhanced_query = SearchFilterBuilder::build_enhanced_query("", &config);

        assert!(enhanced_query.starts_with("model"));
        assert!(enhanced_query.contains("Outdoor"));
    }

    #[test]
    fn test_enhanced_query_builder_disabled() {
        let mut config = SearchConfig::default();
        config.query_enhancement_enabled = false;
        config.environments = vec!["Outdoor".to_string()];

        let enhanced_query = SearchFilterBuilder::build_enhanced_query("牛仔裤", &config);

        assert_eq!(enhanced_query, "牛仔裤");
    }

    #[test]
    fn test_config_validation_success() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];

        let color_filter = ColorFilter {
            enabled: true,
            color: ColorHSV::new(0.5, 0.8, 0.9),
            hue_threshold: 0.05,
            saturation_threshold: 0.05,
            value_threshold: 0.20,
        };
        config.color_filters.insert("上装".to_string(), color_filter);

        let result = SearchFilterBuilder::validate_config(&config);
        assert!(result.is_ok());
    }

    #[test]
    fn test_config_validation_invalid_color_category() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];

        let color_filter = ColorFilter {
            enabled: true,
            color: ColorHSV::new(0.5, 0.8, 0.9),
            hue_threshold: 0.05,
            saturation_threshold: 0.05,
            value_threshold: 0.20,
        };
        config.color_filters.insert("下装".to_string(), color_filter); // 不在categories中

        let result = SearchFilterBuilder::validate_config(&config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("不在选定类别中"));
    }

    #[test]
    fn test_config_validation_invalid_color_values() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];

        // 直接创建带有无效值的ColorHSV，绕过new函数的clamp
        let invalid_color = ColorHSV {
            hue: 1.5,        // 超出范围
            saturation: 0.8,
            value: 0.9,
        };

        let color_filter = ColorFilter {
            enabled: true,
            color: invalid_color,
            hue_threshold: 0.05,
            saturation_threshold: 0.05,
            value_threshold: 0.20,
        };
        config.color_filters.insert("上装".to_string(), color_filter);

        let result = SearchFilterBuilder::validate_config(&config);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("超出有效范围"));
    }

    #[test]
    fn test_config_summary_generation() {
        let mut config = SearchConfig::default();
        config.categories = vec!["上装".to_string()];
        config.environments = vec!["Outdoor".to_string()];
        config.max_keywords = 15;
        config.debug_mode = true;

        let summary = SearchFilterBuilder::generate_config_summary(&config);

        assert!(summary.contains("类别"));
        assert!(summary.contains("环境"));
        assert!(summary.contains("最大关键词: 15"));
    }

    #[test]
    fn test_color_thresholds_default() {
        let thresholds = ColorThresholds::default();
        assert_eq!(thresholds.default_hue_threshold, 0.05);
        assert_eq!(thresholds.default_saturation_threshold, 0.05);
        assert_eq!(thresholds.default_value_threshold, 0.20);
    }

    #[test]
    fn test_outfit_search_global_config_default() {
        let config = OutfitSearchGlobalConfig::default();
        assert_eq!(config.google_project_id, "gen-lang-client-0413414134");
        assert_eq!(config.vertex_ai_app_id, "jeans-search_1751353769585");
        assert_eq!(config.storage_bucket_name, "fashion_image_block");
        assert_eq!(config.data_store_id, "jeans_pattern_data_store");
        assert_eq!(config.cloudflare_project_id, "67720b647ff2b55cf37ba3ef9e677083");
        assert_eq!(config.cloudflare_gateway_id, "bowong-dev");
    }
}
