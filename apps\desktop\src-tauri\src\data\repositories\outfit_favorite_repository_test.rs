#[cfg(test)]
mod tests {
    use super::*;
    use crate::infrastructure::database::Database;
    use crate::data::models::outfit_recommendation::OutfitRecommendation;
    use std::sync::Arc;
    use tempfile::tempdir;

    async fn create_test_database() -> Arc<Database> {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        let database = Database::new(db_path.to_str().unwrap()).await.unwrap();
        Arc::new(database)
    }

    fn create_test_recommendation() -> OutfitRecommendation {
        OutfitRecommendation {
            id: "test-recommendation-1".to_string(),
            title: "测试穿搭方案".to_string(),
            description: "这是一个测试用的穿搭方案".to_string(),
            overall_style: "休闲".to_string(),
            style_tags: vec!["简约".to_string(), "舒适".to_string()],
            occasions: vec!["日常".to_string(), "工作".to_string()],
            seasons: vec!["春季".to_string(), "秋季".to_string()],
            color_theme: "中性色调".to_string(),
            primary_colors: vec![],
            groups: vec![],
            ai_confidence: 0.85,
            tiktok_optimization: None,
            created_at: chrono::Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_save_and_get_favorite() {
        let database = create_test_database().await;
        let repository = OutfitFavoriteRepository::new(database);
        let recommendation = create_test_recommendation();

        // 保存收藏
        let favorite = repository
            .save_to_favorites(recommendation.clone(), Some("我的测试收藏".to_string()))
            .await
            .unwrap();

        assert_eq!(favorite.custom_name, Some("我的测试收藏".to_string()));
        assert_eq!(favorite.recommendation_data.id, recommendation.id);

        // 获取收藏
        let retrieved = repository
            .get_favorite_by_id(&favorite.id)
            .await
            .unwrap()
            .unwrap();

        assert_eq!(retrieved.id, favorite.id);
        assert_eq!(retrieved.custom_name, favorite.custom_name);
        assert_eq!(retrieved.recommendation_data.title, recommendation.title);
    }

    #[tokio::test]
    async fn test_get_all_favorites() {
        let database = create_test_database().await;
        let repository = OutfitFavoriteRepository::new(database);

        // 保存多个收藏
        let recommendation1 = create_test_recommendation();
        let mut recommendation2 = create_test_recommendation();
        recommendation2.id = "test-recommendation-2".to_string();
        recommendation2.title = "第二个测试方案".to_string();

        repository
            .save_to_favorites(recommendation1, Some("收藏1".to_string()))
            .await
            .unwrap();

        repository
            .save_to_favorites(recommendation2, Some("收藏2".to_string()))
            .await
            .unwrap();

        // 获取所有收藏
        let favorites = repository.get_all_favorites().await.unwrap();

        assert_eq!(favorites.len(), 2);
        assert!(favorites.iter().any(|f| f.custom_name == Some("收藏1".to_string())));
        assert!(favorites.iter().any(|f| f.custom_name == Some("收藏2".to_string())));
    }

    #[tokio::test]
    async fn test_remove_favorite() {
        let database = create_test_database().await;
        let repository = OutfitFavoriteRepository::new(database);
        let recommendation = create_test_recommendation();

        // 保存收藏
        let favorite = repository
            .save_to_favorites(recommendation, None)
            .await
            .unwrap();

        // 确认存在
        let exists = repository
            .get_favorite_by_id(&favorite.id)
            .await
            .unwrap()
            .is_some();
        assert!(exists);

        // 删除收藏
        let removed = repository
            .remove_from_favorites(&favorite.id)
            .await
            .unwrap();
        assert!(removed);

        // 确认已删除
        let not_exists = repository
            .get_favorite_by_id(&favorite.id)
            .await
            .unwrap()
            .is_none();
        assert!(not_exists);
    }

    #[tokio::test]
    async fn test_update_custom_name() {
        let database = create_test_database().await;
        let repository = OutfitFavoriteRepository::new(database);
        let recommendation = create_test_recommendation();

        // 保存收藏
        let favorite = repository
            .save_to_favorites(recommendation, Some("原始名称".to_string()))
            .await
            .unwrap();

        // 更新名称
        let updated = repository
            .update_custom_name(&favorite.id, Some("新名称".to_string()))
            .await
            .unwrap();
        assert!(updated);

        // 验证更新
        let retrieved = repository
            .get_favorite_by_id(&favorite.id)
            .await
            .unwrap()
            .unwrap();
        assert_eq!(retrieved.custom_name, Some("新名称".to_string()));
    }

    #[tokio::test]
    async fn test_is_recommendation_favorited() {
        let database = create_test_database().await;
        let repository = OutfitFavoriteRepository::new(database);
        let recommendation = create_test_recommendation();

        // 初始状态未收藏
        let not_favorited = repository
            .is_recommendation_favorited(&recommendation.id)
            .await
            .unwrap();
        assert!(!not_favorited);

        // 保存收藏
        repository
            .save_to_favorites(recommendation.clone(), None)
            .await
            .unwrap();

        // 确认已收藏
        let favorited = repository
            .is_recommendation_favorited(&recommendation.id)
            .await
            .unwrap();
        assert!(favorited);
    }

    #[tokio::test]
    async fn test_get_favorites_count() {
        let database = create_test_database().await;
        let repository = OutfitFavoriteRepository::new(database);

        // 初始计数为0
        let initial_count = repository.get_favorites_count().await.unwrap();
        assert_eq!(initial_count, 0);

        // 添加收藏
        let recommendation = create_test_recommendation();
        repository
            .save_to_favorites(recommendation, None)
            .await
            .unwrap();

        // 验证计数
        let count_after_add = repository.get_favorites_count().await.unwrap();
        assert_eq!(count_after_add, 1);
    }

    #[tokio::test]
    async fn test_favorite_display_name() {
        let recommendation = create_test_recommendation();
        
        // 测试有自定义名称的情况
        let favorite_with_custom = OutfitFavorite::new(
            recommendation.clone(),
            Some("自定义名称".to_string())
        );
        assert_eq!(favorite_with_custom.display_name(), "自定义名称");

        // 测试无自定义名称的情况
        let favorite_without_custom = OutfitFavorite::new(recommendation.clone(), None);
        assert_eq!(favorite_without_custom.display_name(), &recommendation.title);
    }
}
