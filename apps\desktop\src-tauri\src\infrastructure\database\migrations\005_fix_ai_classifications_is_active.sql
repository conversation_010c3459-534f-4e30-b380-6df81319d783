-- 修复AI分类表的is_active字段类型（从BOOLEAN改为INTEGER）
-- 创建新表
CREATE TABLE ai_classifications_new (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    prompt_text TEXT NOT NULL,
    description TEXT,
    is_active INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 复制数据，将BOOLEAN转换为INTEGER
INSERT INTO ai_classifications_new
SELECT id, name, prompt_text, description,
       CASE WHEN is_active THEN 1 ELSE 0 END as is_active,
       sort_order, created_at, updated_at
FROM ai_classifications;

-- 删除旧表并重命名新表
DROP TABLE ai_classifications;
ALTER TABLE ai_classifications_new RENAME TO ai_classifications;
