-- SQLite不支持DROP COLUMN，需要重建表
CREATE TABLE template_matching_results_new (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL,
    project_id TEXT NOT NULL,
    matched_segments TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
);

-- 迁移数据（排除导出状态字段）
INSERT INTO template_matching_results_new 
SELECT id, template_id, project_id, matched_segments, created_at, updated_at
FROM template_matching_results;

-- 删除旧表并重命名新表
DROP TABLE template_matching_results;
ALTER TABLE template_matching_results_new RENAME TO template_matching_results;
