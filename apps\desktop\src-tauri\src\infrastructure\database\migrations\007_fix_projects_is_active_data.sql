-- 修复项目表的is_active字段数据
-- 将 "true" 字符串转换为 1
UPDATE projects SET is_active = 1 WHERE is_active = 'true';

-- 将 "false" 字符串转换为 0
UPDATE projects SET is_active = 0 WHERE is_active = 'false';

-- 如果所有项目的 is_active 都是 0，可能是之前的迁移错误，恢复为 1
-- 但只有在确实存在项目且全部为0的情况下才执行
UPDATE projects 
SET is_active = 1 
WHERE id IN (
    SELECT id FROM projects 
    WHERE (SELECT COUNT(*) FROM projects) > 0 
    AND (SELECT COUNT(*) FROM projects WHERE is_active = 1) = 0
);
