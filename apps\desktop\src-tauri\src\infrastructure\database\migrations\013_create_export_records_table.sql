-- 创建export_records表
-- 用于记录每次导出操作的详细信息

CREATE TABLE IF NOT EXISTS export_records (
    id TEXT PRIMARY KEY,
    matching_result_id TEXT NOT NULL,
    project_id TEXT NOT NULL,
    template_id TEXT NOT NULL,
    export_type TEXT NOT NULL,  -- JSON格式的ExportType
    export_format TEXT NOT NULL,  -- JSON格式的ExportFormat
    file_path TEXT NOT NULL,
    file_size INTEGER,  -- 文件大小（字节）
    export_status TEXT NOT NULL DEFAULT '"InProgress"',  -- JSON格式的ExportStatus
    export_duration_ms INTEGER NOT NULL DEFAULT 0,
    error_message TEXT,
    metadata TEXT,  -- JSON格式的额外元数据
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    FOREIGN KEY (matching_result_id) REFERENCES template_matching_results (id) ON DELETE CASCADE,
    <PERSON>OR<PERSON>G<PERSON> KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_export_records_matching_result_id ON export_records (matching_result_id);
CREATE INDEX IF NOT EXISTS idx_export_records_project_id ON export_records (project_id);
CREATE INDEX IF NOT EXISTS idx_export_records_template_id ON export_records (template_id);
CREATE INDEX IF NOT EXISTS idx_export_records_export_type ON export_records (export_type);
CREATE INDEX IF NOT EXISTS idx_export_records_export_status ON export_records (export_status);
CREATE INDEX IF NOT EXISTS idx_export_records_is_active ON export_records (is_active);
CREATE INDEX IF NOT EXISTS idx_export_records_created_at ON export_records (created_at);
