-- 添加默认的AI分类数据
-- 插入四个基本的视频分类类型

-- 生成UUID的函数（SQLite兼容）
-- 使用随机字符串生成简单的UUID格式

INSERT OR IGNORE INTO ai_classifications (
    id, name, prompt_text, description, is_active, sort_order, created_at, updated_at
) VALUES
(
    'ai_class_' || hex(randomblob(16)),
    '全身',
    '头顶到脚底完整入镜，肢体可见度≥90%，能完整看到鞋子和发型',
    '完整的全身镜头，从头到脚都清晰可见',
    1,
    1,
    datetime('now', 'utc') || 'Z',
    datetime('now', 'utc') || 'Z'
),
(
    'ai_class_' || hex(randomblob(16)),
    '上半身',
    '头部到腰部，手臂动作完整，可见手部动作，腰部以下缺失',
    '上半身镜头，包含头部、躯干和手臂',
    1,
    2,
    datetime('now', 'utc') || 'Z',
    datetime('now', 'utc') || 'Z'
),
(
    'ai_class_' || hex(randomblob(16)),
    '中段特写',
    '躯干核心区域（胸到膝），腰带/腹部/臀部特写',
    '身体中段的特写镜头',
    1,
    3,
    datetime('now', 'utc') || 'Z',
    datetime('now', 'utc') || 'Z'
),
(
    'ai_class_' || hex(randomblob(16)),
    '下半身',
    '腰部到脚底，腿部动作完整，可见脚步移动，胸部以上缺失',
    '下半身镜头，包含腰部、腿部和脚部',
    1,
    4,
    datetime('now', 'utc') || 'Z',
    datetime('now', 'utc') || 'Z'
);

-- 创建索引以优化查询性能（如果不存在）
CREATE INDEX IF NOT EXISTS idx_ai_classifications_name ON ai_classifications (name);
CREATE INDEX IF NOT EXISTS idx_ai_classifications_is_active ON ai_classifications (is_active);
CREATE INDEX IF NOT EXISTS idx_ai_classifications_sort_order ON ai_classifications (sort_order);
