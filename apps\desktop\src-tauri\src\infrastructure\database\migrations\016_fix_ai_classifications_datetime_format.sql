-- 修复ai_classifications表中的日期时间格式
-- 将SQLite的CURRENT_TIMESTAMP格式转换为RFC3339格式

-- 更新所有现有记录的created_at和updated_at字段
-- 将 'YYYY-MM-DD HH:MM:SS' 格式转换为 'YYYY-MM-DDTHH:MM:SSZ' 格式
UPDATE ai_classifications 
SET 
    created_at = CASE 
        WHEN created_at LIKE '____-__-__ __:__:__' THEN 
            REPLACE(created_at, ' ', 'T') || 'Z'
        ELSE 
            created_at
    END,
    updated_at = CASE 
        WHEN updated_at LIKE '____-__-__ __:__:__' THEN 
            REPLACE(updated_at, ' ', 'T') || 'Z'
        ELSE 
            updated_at
    END
WHERE 
    created_at LIKE '____-__-__ __:__:__' 
    OR updated_at LIKE '____-__-__ __:__:__';
