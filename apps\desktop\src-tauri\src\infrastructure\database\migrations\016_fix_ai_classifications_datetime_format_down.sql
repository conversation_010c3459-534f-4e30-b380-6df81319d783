-- 回滚：将RFC3339格式转换回SQLite的CURRENT_TIMESTAMP格式
-- 将 'YYYY-MM-DDTHH:MM:SSZ' 格式转换为 'YYYY-MM-DD HH:MM:SS' 格式

UPDATE ai_classifications 
SET 
    created_at = CASE 
        WHEN created_at LIKE '____-__-__T__:__:__Z' THEN 
            REPLACE(REPLACE(created_at, 'T', ' '), 'Z', '')
        ELSE 
            created_at
    END,
    updated_at = CASE 
        WHEN updated_at LIKE '____-__-__T__:__:__Z' THEN 
            REPLACE(REPLACE(updated_at, 'T', ' '), 'Z', '')
        ELSE 
            updated_at
    END
WHERE 
    created_at LIKE '____-__-__T__:__:__Z' 
    OR updated_at LIKE '____-__-__T__:__:__Z';
