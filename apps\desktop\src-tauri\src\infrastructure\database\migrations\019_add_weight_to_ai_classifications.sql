-- 为ai_classifications表添加weight字段
-- 用于支持按顺序匹配功能

-- 添加weight字段，默认值为0
ALTER TABLE ai_classifications ADD COLUMN weight INTEGER NOT NULL DEFAULT 0;

-- 为现有数据设置默认权重值
-- 按照sort_order的顺序设置权重，sort_order越小权重越高
UPDATE ai_classifications 
SET weight = CASE 
    WHEN sort_order = 1 THEN 10
    WHEN sort_order = 2 THEN 9
    WHEN sort_order = 3 THEN 8
    WHEN sort_order = 4 THEN 7
    WHEN sort_order = 5 THEN 6
    WHEN sort_order = 6 THEN 5
    WHEN sort_order = 7 THEN 4
    WHEN sort_order = 8 THEN 3
    WHEN sort_order = 9 THEN 2
    ELSE 1
END;

-- 创建索引以提高按权重查询的性能
CREATE INDEX IF NOT EXISTS idx_ai_classifications_weight ON ai_classifications(weight DESC);
