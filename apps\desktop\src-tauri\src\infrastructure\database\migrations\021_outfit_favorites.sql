-- 创建穿搭方案收藏表
CREATE TABLE IF NOT EXISTS outfit_favorites (
    id TEXT PRIMARY KEY,
    recommendation_data TEXT NOT NULL, -- JSON序列化的OutfitRecommendation数据
    custom_name TEXT, -- 用户自定义名称
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_outfit_favorites_created_at ON outfit_favorites (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_outfit_favorites_custom_name ON outfit_favorites (custom_name);
