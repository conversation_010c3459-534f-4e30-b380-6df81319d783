-- 创建图片生成记录表
CREATE TABLE IF NOT EXISTS image_generation_records (
    id TEXT PRIMARY KEY,
    task_id TEXT,
    prompt TEXT NOT NULL,
    reference_image_path TEXT,
    reference_image_url TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    progress REAL NOT NULL DEFAULT 0.0,
    result_urls TEXT NOT NULL DEFAULT '[]',
    error_message TEXT,
    created_at TEXT NOT NULL,
    started_at TEXT,
    completed_at TEXT,
    duration_ms INTEGER
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_image_generation_records_task_id ON image_generation_records(task_id);
CREATE INDEX IF NOT EXISTS idx_image_generation_records_created_at ON image_generation_records(created_at);
CREATE INDEX IF NOT EXISTS idx_image_generation_records_status ON image_generation_records(status);
