-- 创建声音克隆记录表
CREATE TABLE IF NOT EXISTS voice_clone_records (
    id TEXT PRIMARY KEY,
    voice_id TEXT NOT NULL UNIQUE,
    voice_name TEXT,
    clone_text TEXT NOT NULL,
    audio_url TEXT NOT NULL,
    file_id INTEGER,
    model TEXT,
    need_noise_reduction BOOLEAN DEFAULT 1,
    prefix TEXT,
    audio_file_path TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_voice_clone_records_voice_id ON voice_clone_records(voice_id);
CREATE INDEX IF NOT EXISTS idx_voice_clone_records_created_at ON voice_clone_records(created_at);
CREATE INDEX IF NOT EXISTS idx_voice_clone_records_status ON voice_clone_records(status);
