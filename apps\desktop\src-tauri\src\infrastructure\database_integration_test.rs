#[cfg(test)]
mod integration_tests {
    use crate::infrastructure::database::Database;
    use tempfile::TempDir;

    fn create_test_database() -> (Database, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");

        let database = Database::new_with_path(db_path.to_str().unwrap()).unwrap();

        (database, temp_dir)
    }

    #[test]
    fn test_database_initialization_with_migrations() {
        let (database, _temp_dir) = create_test_database();
        
        // 检查数据库健康状态
        let health = database.check_database_health().unwrap();

        // 打印健康状态详情用于调试
        println!("数据库健康状态详情:");
        println!("  整体健康: {}", health.overall_healthy);
        println!("  版本最新: {}", health.version_up_to_date);
        println!("  连接健康: {}", health.connection_healthy);
        println!("  表完整: {}", health.tables_complete);
        println!("  迁移健康: {}", health.migrations_healthy);
        println!("  缺失表: {:?}", health.missing_tables);
        println!("  失败迁移: {:?}", health.failed_migrations);

        // 验证数据库是否健康
        assert!(health.overall_healthy, "数据库应该是健康的");
        assert!(health.version_up_to_date, "数据库版本应该是最新的");
        assert!(health.connection_healthy, "数据库连接应该是健康的");
        assert!(health.tables_complete, "数据库表结构应该是完整的");
        assert!(health.migrations_healthy, "数据库迁移应该是健康的");
        assert!(health.missing_tables.is_empty(), "不应该有缺失的表");
        assert!(health.failed_migrations.is_empty(), "不应该有失败的迁移");
        
        println!("数据库健康检查通过:");
        println!("  当前版本: v{}", health.current_version);
        println!("  最新版本: v{}", health.latest_version);
        println!("  连接状态: {}", health.connection_status);
    }

    #[test]
    fn test_migration_history() {
        let (database, _temp_dir) = create_test_database();
        
        // 获取迁移历史
        let history = database.get_migration_history().unwrap();
        
        // 应该有迁移记录
        assert!(!history.is_empty(), "应该有迁移历史记录");
        
        // 验证迁移记录的完整性
        for (version, description, applied_at, success) in &history {
            assert!(success, "迁移 v{}: {} 应该成功", version, description);
            assert!(!description.is_empty(), "迁移描述不应该为空");
            assert!(!applied_at.is_empty(), "应用时间不应该为空");
        }
        
        println!("迁移历史记录:");
        for (version, description, applied_at, success) in history {
            println!("  v{}: {} - {} ({})", 
                version, 
                description, 
                applied_at,
                if success { "成功" } else { "失败" }
            );
        }
    }

    #[test]
    fn test_version_info() {
        let (database, _temp_dir) = create_test_database();
        
        // 获取版本信息
        let (current_version, latest_version) = database.get_database_version_info().unwrap();
        
        // 验证版本信息
        assert!(current_version > 0, "当前版本应该大于0");
        assert!(latest_version > 0, "最新版本应该大于0");
        assert_eq!(current_version, latest_version, "当前版本应该等于最新版本");
        
        println!("版本信息: v{} (最新: v{})", current_version, latest_version);
    }

    #[test]
    fn test_database_operations_after_migration() {
        let (database, _temp_dir) = create_test_database();
        
        // 测试基本的数据库操作是否正常工作
        
        // 1. 测试项目操作
        let project_id = "test-project-id".to_string();
        let project_name = "测试项目".to_string();
        let project_path = "/test/path".to_string();
        
        // 创建项目应该成功（这里只是验证表结构正确，不测试具体业务逻辑）
        let result = database.execute_sql_simple(&format!(
            "INSERT INTO projects (id, name, path) VALUES ('{}', '{}', '{}')",
            project_id, project_name, project_path
        ));

        assert!(result.is_ok(), "插入项目应该成功");

        // 2. 验证外键约束工作正常
        let material_result = database.execute_sql_simple(
            "INSERT INTO materials (id, project_id, name, original_path, file_size, md5_hash, material_type)
             VALUES ('test-material', 'non-existent-project', 'test', '/test', 100, 'hash', 'Video')"
        );

        assert!(material_result.is_err(), "插入不存在项目的素材应该失败（外键约束）");

        // 3. 验证索引工作正常
        let query_result = database.prepare_sql("SELECT * FROM projects WHERE name = ?1");
        assert!(query_result.is_ok() && query_result.unwrap(), "按名称查询项目应该成功（索引存在）");
        
        println!("数据库操作测试通过");
    }

    #[test]
    fn test_migration_system_robustness() {
        let (database, _temp_dir) = create_test_database();
        
        // 测试重复迁移的幂等性
        let health_before = database.check_database_health().unwrap();
        
        // 再次运行迁移（应该是幂等的）
        let migration_result = database.run_migrations();
        assert!(migration_result.is_ok(), "重复迁移应该成功");
        
        let health_after = database.check_database_health().unwrap();
        
        // 状态应该保持一致
        assert_eq!(health_before.current_version, health_after.current_version, "版本应该保持一致");
        assert_eq!(health_before.overall_healthy, health_after.overall_healthy, "健康状态应该保持一致");
        
        println!("迁移系统鲁棒性测试通过");
    }
}
