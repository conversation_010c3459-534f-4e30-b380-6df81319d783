use crate::business::services::draft_parser::DraftContentParser;

/// 测试解析剪映草稿文件
#[tauri::command]
pub async fn test_parse_draft_file(
    file_path: String,
) -> Result<String, String> {
    match DraftContentParser::parse_file(&file_path, None) {
        Ok(parse_result) => {
            let summary = serde_json::json!({
                "success": true,
                "template_name": parse_result.template.name,
                "duration": parse_result.template.duration,
                "fps": parse_result.template.fps,
                "canvas_config": parse_result.template.canvas_config,
                "materials_count": parse_result.template.materials.len(),
                "tracks_count": parse_result.template.tracks.len(),
                "missing_files_count": parse_result.missing_files.len(),
                "warnings_count": parse_result.warnings.len(),
                "missing_files": parse_result.missing_files,
                "warnings": parse_result.warnings
            });
            Ok(summary.to_string())
        }
        Err(e) => {
            let error_info = serde_json::json!({
                "success": false,
                "error": e.to_string(),
                "error_type": "ParseError"
            });
            Ok(error_info.to_string())
        }
    }
}

/// 验证模板数据结构
#[tauri::command]
pub async fn validate_template_structure(
    file_path: String,
) -> Result<String, String> {
    match DraftContentParser::parse_file(&file_path, None) {
        Ok(parse_result) => {
            let template = &parse_result.template;
            let mut validation_results = Vec::new();
            
            // 验证基本信息
            if template.name.is_empty() {
                validation_results.push("模板名称为空".to_string());
            }
            
            if template.duration == 0 {
                validation_results.push("模板时长为0".to_string());
            }
            
            if template.materials.is_empty() {
                validation_results.push("没有素材".to_string());
            }
            
            if template.tracks.is_empty() {
                validation_results.push("没有轨道".to_string());
            }
            
            // 验证轨道和片段
            for (track_idx, track) in template.tracks.iter().enumerate() {
                if track.segments.is_empty() {
                    validation_results.push(format!("轨道 {} 没有片段", track_idx));
                }
                
                for (seg_idx, segment) in track.segments.iter().enumerate() {
                    if segment.duration == 0 {
                        validation_results.push(format!("轨道 {} 片段 {} 时长为0", track_idx, seg_idx));
                    }
                    
                    // 检查素材引用是否存在
                    if let Some(ref material_id) = segment.template_material_id {
                        let material_exists = template.materials.iter()
                            .any(|m| m.id == *material_id);
                        if !material_exists {
                            validation_results.push(format!("轨道 {} 片段 {} 引用的素材不存在: {}",
                                track_idx, seg_idx, material_id));
                        }
                    }
                }
            }
            
            let result = serde_json::json!({
                "success": true,
                "valid": validation_results.is_empty(),
                "issues": validation_results,
                "summary": {
                    "materials": template.materials.len(),
                    "tracks": template.tracks.len(),
                    "total_segments": template.tracks.iter().map(|t| t.segments.len()).sum::<usize>()
                }
            });
            
            Ok(result.to_string())
        }
        Err(e) => {
            let error_info = serde_json::json!({
                "success": false,
                "error": e.to_string()
            });
            Ok(error_info.to_string())
        }
    }
}
