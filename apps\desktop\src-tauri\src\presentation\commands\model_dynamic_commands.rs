use tauri::{command, State};
use crate::app_state::AppState;
use crate::business::services::model_dynamic_service::ModelDynamicService;
use crate::data::models::model_dynamic::{
    ModelDynamic, CreateModelDynamicRequest, UpdateModelDynamicRequest, ModelDynamicStats
};

/// 创建模特动态命令
/// 遵循 Tauri 开发规范的命令设计模式
#[command]
pub async fn create_model_dynamic(
    state: State<'_, AppState>,
    request: CreateModelDynamicRequest,
) -> Result<ModelDynamic, String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::create_dynamic(repository, request)
        .map_err(|e| e.to_string())
}

/// 获取模特动态详情命令
#[command]
pub async fn get_model_dynamic_by_id(
    state: State<'_, AppState>,
    id: String,
) -> Result<Option<ModelDynamic>, String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::get_dynamic_by_id(repository, &id)
        .map_err(|e| e.to_string())
}

/// 获取模特的所有动态命令
#[command]
pub async fn get_model_dynamics_by_model_id(
    state: State<'_, AppState>,
    model_id: String,
) -> Result<Vec<ModelDynamic>, String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::get_dynamics_by_model_id(repository, &model_id)
        .map_err(|e| e.to_string())
}

/// 更新模特动态命令
#[command]
pub async fn update_model_dynamic(
    state: State<'_, AppState>,
    id: String,
    request: UpdateModelDynamicRequest,
) -> Result<ModelDynamic, String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::update_dynamic(repository, &id, request)
        .map_err(|e| e.to_string())
}

/// 删除模特动态命令
#[command]
pub async fn delete_model_dynamic(
    state: State<'_, AppState>,
    id: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::delete_dynamic(repository, &id)
        .map_err(|e| e.to_string())
}

/// 获取模特动态统计命令
#[command]
pub async fn get_model_dynamic_stats(
    state: State<'_, AppState>,
    model_id: String,
) -> Result<ModelDynamicStats, String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::get_stats_by_model_id(repository, &model_id)
        .map_err(|e| e.to_string())
}

/// 重新生成视频命令
#[command]
pub async fn regenerate_dynamic_video(
    _state: State<'_, AppState>,
    dynamic_id: String,
    video_id: String,
) -> Result<(), String> {
    // TODO: 实现视频重新生成逻辑
    // 这里可以调用视频生成服务来重新生成指定的视频
    println!("重新生成视频: dynamic_id={}, video_id={}", dynamic_id, video_id);
    Ok(())
}

/// 获取可用的AI模型列表命令
#[command]
pub async fn get_available_ai_models() -> Result<Vec<serde_json::Value>, String> {
    // 返回可用的AI模型列表
    let models = vec![
        serde_json::json!({
            "id": "jimeng",
            "name": "极梦",
            "description": "高质量视频生成模型",
            "is_available": true,
            "max_video_count": 9
        })
    ];
    
    Ok(models)
}

/// 更新视频生成进度命令
#[command]
pub async fn update_video_generation_progress(
    state: State<'_, AppState>,
    dynamic_id: String,
    video_id: String,
    progress: u32,
) -> Result<(), String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::update_video_progress(repository, &dynamic_id, &video_id, progress)
        .map_err(|e| e.to_string())
}

/// 标记视频生成失败命令
#[command]
pub async fn mark_video_generation_failed(
    state: State<'_, AppState>,
    dynamic_id: String,
    video_id: String,
    error_message: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_dynamic_repository()
        .map_err(|e| format!("获取模特动态仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特动态仓库未初始化")?;

    ModelDynamicService::mark_video_as_failed(repository, &dynamic_id, &video_id, error_message)
        .map_err(|e| e.to_string())
}
