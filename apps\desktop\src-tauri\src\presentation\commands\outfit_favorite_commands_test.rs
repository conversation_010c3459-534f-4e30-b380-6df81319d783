#[cfg(test)]
mod tests {
    use super::*;
    use crate::infrastructure::database::Database;
    use crate::presentation::state::AppState;
    use crate::data::models::outfit_recommendation::OutfitRecommendation;
    use std::sync::{Arc, Mutex};
    use tempfile::tempdir;
    use tauri::State;

    async fn create_test_app_state() -> AppState {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        let database = Database::new(db_path.to_str().unwrap()).await.unwrap();
        
        AppState {
            database: Mutex::new(Some(Arc::new(database))),
            gemini_service: Mutex::new(None),
        }
    }

    fn create_test_recommendation() -> OutfitRecommendation {
        OutfitRecommendation {
            id: "test-recommendation-1".to_string(),
            title: "测试穿搭方案".to_string(),
            description: "这是一个测试用的穿搭方案".to_string(),
            overall_style: "休闲".to_string(),
            style_tags: vec!["简约".to_string(), "舒适".to_string()],
            occasions: vec!["日常".to_string(), "工作".to_string()],
            seasons: vec!["春季".to_string(), "秋季".to_string()],
            color_theme: "中性色调".to_string(),
            primary_colors: vec![],
            groups: vec![],
            ai_confidence: 0.85,
            tiktok_optimization: None,
            created_at: chrono::Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_save_and_get_favorite_commands() {
        let app_state = create_test_app_state().await;
        let state = State::from(&app_state);
        let recommendation = create_test_recommendation();

        // 测试保存收藏
        let save_request = SaveOutfitToFavoritesRequest {
            recommendation: recommendation.clone(),
            custom_name: Some("测试收藏".to_string()),
        };

        let save_response = save_outfit_to_favorites(state.clone(), save_request)
            .await
            .unwrap();

        assert_eq!(save_response.favorite.custom_name, Some("测试收藏".to_string()));
        assert_eq!(save_response.favorite.recommendation_data.id, recommendation.id);

        // 测试获取收藏列表
        let get_response = get_favorite_outfits(state.clone()).await.unwrap();

        assert_eq!(get_response.total_count, 1);
        assert_eq!(get_response.favorites.len(), 1);
        assert_eq!(get_response.favorites[0].id, save_response.favorite_id);
    }

    #[tokio::test]
    async fn test_remove_favorite_command() {
        let app_state = create_test_app_state().await;
        let state = State::from(&app_state);
        let recommendation = create_test_recommendation();

        // 先保存一个收藏
        let save_request = SaveOutfitToFavoritesRequest {
            recommendation,
            custom_name: None,
        };

        let save_response = save_outfit_to_favorites(state.clone(), save_request)
            .await
            .unwrap();

        // 测试删除收藏
        let removed = remove_from_favorites(state.clone(), save_response.favorite_id.clone())
            .await
            .unwrap();

        assert!(removed);

        // 验证已删除
        let get_response = get_favorite_outfits(state.clone()).await.unwrap();
        assert_eq!(get_response.total_count, 0);
    }

    #[tokio::test]
    async fn test_is_outfit_favorited_command() {
        let app_state = create_test_app_state().await;
        let state = State::from(&app_state);
        let recommendation = create_test_recommendation();

        // 初始状态未收藏
        let not_favorited = is_outfit_favorited(state.clone(), recommendation.id.clone())
            .await
            .unwrap();
        assert!(!not_favorited);

        // 保存收藏
        let save_request = SaveOutfitToFavoritesRequest {
            recommendation: recommendation.clone(),
            custom_name: None,
        };

        save_outfit_to_favorites(state.clone(), save_request)
            .await
            .unwrap();

        // 验证已收藏
        let favorited = is_outfit_favorited(state.clone(), recommendation.id)
            .await
            .unwrap();
        assert!(favorited);
    }

    #[tokio::test]
    async fn test_multiple_favorites() {
        let app_state = create_test_app_state().await;
        let state = State::from(&app_state);

        // 创建多个不同的推荐方案
        let mut recommendation1 = create_test_recommendation();
        recommendation1.id = "test-recommendation-1".to_string();
        recommendation1.title = "方案1".to_string();

        let mut recommendation2 = create_test_recommendation();
        recommendation2.id = "test-recommendation-2".to_string();
        recommendation2.title = "方案2".to_string();

        // 保存多个收藏
        let save_request1 = SaveOutfitToFavoritesRequest {
            recommendation: recommendation1,
            custom_name: Some("收藏1".to_string()),
        };

        let save_request2 = SaveOutfitToFavoritesRequest {
            recommendation: recommendation2,
            custom_name: Some("收藏2".to_string()),
        };

        save_outfit_to_favorites(state.clone(), save_request1)
            .await
            .unwrap();

        save_outfit_to_favorites(state.clone(), save_request2)
            .await
            .unwrap();

        // 验证收藏列表
        let get_response = get_favorite_outfits(state.clone()).await.unwrap();

        assert_eq!(get_response.total_count, 2);
        assert_eq!(get_response.favorites.len(), 2);

        let custom_names: Vec<_> = get_response.favorites
            .iter()
            .map(|f| f.custom_name.as_ref().unwrap())
            .collect();

        assert!(custom_names.contains(&"收藏1".to_string()));
        assert!(custom_names.contains(&"收藏2".to_string()));
    }

    #[tokio::test]
    async fn test_error_handling() {
        let app_state = AppState {
            database: Mutex::new(None), // 故意设置为None来测试错误处理
            gemini_service: Mutex::new(None),
        };
        let state = State::from(&app_state);
        let recommendation = create_test_recommendation();

        // 测试数据库未初始化的错误
        let save_request = SaveOutfitToFavoritesRequest {
            recommendation,
            custom_name: None,
        };

        let result = save_outfit_to_favorites(state.clone(), save_request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Database not initialized"));

        // 测试获取收藏列表的错误
        let get_result = get_favorite_outfits(state.clone()).await;
        assert!(get_result.is_err());
        assert!(get_result.unwrap_err().contains("Database not initialized"));

        // 测试删除收藏的错误
        let remove_result = remove_from_favorites(state.clone(), "non-existent".to_string()).await;
        assert!(remove_result.is_err());
        assert!(remove_result.unwrap_err().contains("Database not initialized"));
    }
}
