use tauri::State;
use crate::app_state::AppState;
use crate::business::services::project_template_binding_service::ProjectTemplateBindingService;
use crate::data::models::project_template_binding::{
    ProjectTemplateBinding, CreateProjectTemplateBindingRequest, UpdateProjectTemplateBindingRequest,
    ProjectTemplateBindingQueryParams, ProjectTemplateBindingDetail,
    BatchCreateProjectTemplateBindingRequest, BatchDeleteProjectTemplateBindingRequest,
};

/// 辅助函数：创建服务实例
fn create_service(state: &State<'_, AppState>) -> Result<ProjectTemplateBindingService, String> {
    let database = state.get_database();
    ProjectTemplateBindingService::new(database).map_err(|e| e.to_string())
}

/// 创建项目-模板绑定
#[tauri::command]
pub async fn create_project_template_binding(
    request: CreateProjectTemplateBindingRequest,
    state: State<'_, AppState>,
) -> Result<ProjectTemplateBinding, String> {
    let service = create_service(&state)?;
    
    service.create_binding(request)
        .await
        .map_err(|e| e.to_string())
}

/// 更新项目-模板绑定
#[tauri::command]
pub async fn update_project_template_binding(
    id: String,
    request: UpdateProjectTemplateBindingRequest,
    state: State<'_, AppState>,
) -> Result<ProjectTemplateBinding, String> {
    let service = create_service(&state)?;

    service.update_binding(&id, request)
        .await
        .map_err(|e| e.to_string())
}

/// 删除项目-模板绑定
#[tauri::command]
pub async fn delete_project_template_binding(
    id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let service = create_service(&state)?;

    service.delete_binding(&id)
        .await
        .map_err(|e| e.to_string())
}

/// 根据项目ID和模板ID删除绑定
#[tauri::command]
pub async fn delete_project_template_binding_by_ids(
    project_id: String,
    template_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let service = create_service(&state)?;

    service.delete_binding_by_project_and_template(&project_id, &template_id)
        .await
        .map_err(|e| e.to_string())
}

/// 获取项目-模板绑定详情
#[tauri::command]
pub async fn get_project_template_binding(
    id: String,
    state: State<'_, AppState>,
) -> Result<ProjectTemplateBinding, String> {
    let service = create_service(&state)?;

    service.get_binding(&id)
        .await
        .map_err(|e| e.to_string())
}

/// 查询项目-模板绑定列表
#[tauri::command]
pub async fn list_project_template_bindings(
    params: ProjectTemplateBindingQueryParams,
    state: State<'_, AppState>,
) -> Result<Vec<ProjectTemplateBinding>, String> {
    let service = create_service(&state)?;

    service.list_bindings(params)
        .await
        .map_err(|e| e.to_string())
}

/// 获取项目的模板列表
#[tauri::command]
pub async fn get_templates_by_project(
    project_id: String,
    state: State<'_, AppState>,
) -> Result<Vec<ProjectTemplateBindingDetail>, String> {
    let service = create_service(&state)?;

    service.get_templates_by_project(&project_id)
        .await
        .map_err(|e| e.to_string())
}

/// 获取模板的项目列表
#[tauri::command]
pub async fn get_projects_by_template(
    template_id: String,
    state: State<'_, AppState>,
) -> Result<Vec<ProjectTemplateBindingDetail>, String> {
    let service = create_service(&state)?;

    service.get_projects_by_template(&template_id)
        .await
        .map_err(|e| e.to_string())
}

/// 批量创建项目-模板绑定
#[tauri::command]
pub async fn batch_create_project_template_bindings(
    request: BatchCreateProjectTemplateBindingRequest,
    state: State<'_, AppState>,
) -> Result<Vec<ProjectTemplateBinding>, String> {
    let service = create_service(&state)?;

    service.batch_create_bindings(request)
        .await
        .map_err(|e| e.to_string())
}

/// 批量删除项目-模板绑定
#[tauri::command]
pub async fn batch_delete_project_template_bindings(
    request: BatchDeleteProjectTemplateBindingRequest,
    state: State<'_, AppState>,
) -> Result<u32, String> {
    let service = create_service(&state)?;

    service.batch_delete_bindings(request)
        .await
        .map_err(|e| e.to_string())
}

/// 激活项目-模板绑定
#[tauri::command]
pub async fn activate_project_template_binding(
    id: String,
    state: State<'_, AppState>,
) -> Result<ProjectTemplateBinding, String> {
    let service = create_service(&state)?;

    service.activate_binding(&id)
        .await
        .map_err(|e| e.to_string())
}

/// 停用项目-模板绑定
#[tauri::command]
pub async fn deactivate_project_template_binding(
    id: String,
    state: State<'_, AppState>,
) -> Result<ProjectTemplateBinding, String> {
    let service = create_service(&state)?;

    service.deactivate_binding(&id)
        .await
        .map_err(|e| e.to_string())
}

/// 获取项目的主要模板绑定
#[tauri::command]
pub async fn get_primary_template_binding_for_project(
    project_id: String,
    state: State<'_, AppState>,
) -> Result<Option<ProjectTemplateBinding>, String> {
    let service = create_service(&state)?;

    service.get_primary_binding_for_project(&project_id)
        .await
        .map_err(|e| e.to_string())
}

/// 设置项目的主要模板
#[tauri::command]
pub async fn set_primary_template_for_project(
    project_id: String,
    template_id: String,
    state: State<'_, AppState>,
) -> Result<ProjectTemplateBinding, String> {
    let service = create_service(&state)?;

    service.set_primary_template(&project_id, &template_id)
        .await
        .map_err(|e| e.to_string())
}

/// 检查项目-模板绑定是否存在
#[tauri::command]
pub async fn check_project_template_binding_exists(
    project_id: String,
    template_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let service = create_service(&state)?;

    service.binding_exists(&project_id, &template_id)
        .await
        .map_err(|e| e.to_string())
}
