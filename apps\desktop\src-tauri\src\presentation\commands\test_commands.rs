use tauri::State;
use std::sync::Arc;
use tracing::{info, warn, error, debug};

use crate::infrastructure::database::Database;

/// 测试数据库连接的命令
#[tauri::command]
pub async fn test_database_connection(
    database: State<'_, Arc<Database>>,
) -> Result<String, String> {
    // 简单测试数据库是否可访问
    let _db = database.inner();
    Ok("数据库状态管理成功".to_string())
}

/// 测试模板表是否存在
#[tauri::command]
pub async fn test_template_table(
    database: State<'_, Arc<Database>>,
) -> Result<String, String> {
    let conn_arc = database.inner().get_connection();

    // 在作用域内使用连接
    {
        let conn = conn_arc.lock()
            .map_err(|e| format!("获取数据库连接失败: {}", e))?;

        // 检查模板表是否存在
        let query = "SELECT name FROM sqlite_master WHERE type='table' AND name='templates'";
        let mut stmt = conn.prepare(query)
            .map_err(|e| format!("准备查询失败: {}", e))?;

        let table_exists = stmt.exists([])
            .map_err(|e| format!("查询执行失败: {}", e))?;

        if table_exists {
            Ok("模板表存在".to_string())
        } else {
            Err("模板表不存在".to_string())
        }
    }
}

/// 测试日志输出
#[tauri::command]
pub async fn test_logging() -> Result<String, String> {
    info!("这是一条 INFO 级别的日志");
    warn!("这是一条 WARN 级别的日志");
    error!("这是一条 ERROR 级别的日志");
    debug!("这是一条 DEBUG 级别的日志");

    println!("这是一条 println! 输出");
    eprintln!("这是一条 eprintln! 输出");

    Ok("日志测试完成".to_string())
}
