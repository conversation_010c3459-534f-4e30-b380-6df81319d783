#[cfg(test)]
mod batch_delete_tests {
    use crate::data::models::material::{BatchDeleteResult, BatchDeleteFailedItem};

    #[test]
    fn test_batch_delete_result_creation() {
        // 测试BatchDeleteResult结构体的创建
        let result = BatchDeleteResult {
            total_count: 3,
            success_count: 1,
            failed_count: 2,
            successful_ids: vec!["success1".to_string()],
            failed_items: vec![
                BatchDeleteFailedItem {
                    id: "failed1".to_string(),
                    error_message: "素材不存在".to_string(),
                },
                BatchDeleteFailedItem {
                    id: "failed2".to_string(),
                    error_message: "删除失败".to_string(),
                },
            ],
        };

        assert_eq!(result.total_count, 3);
        assert_eq!(result.success_count, 1);
        assert_eq!(result.failed_count, 2);
        assert_eq!(result.successful_ids.len(), 1);
        assert_eq!(result.failed_items.len(), 2);
        assert_eq!(result.successful_ids[0], "success1");
        assert_eq!(result.failed_items[0].id, "failed1");
        assert_eq!(result.failed_items[0].error_message, "素材不存在");
    }

    #[test]
    fn test_batch_delete_failed_item_creation() {
        let failed_item = BatchDeleteFailedItem {
            id: "test_id".to_string(),
            error_message: "测试错误消息".to_string(),
        };

        assert_eq!(failed_item.id, "test_id");
        assert_eq!(failed_item.error_message, "测试错误消息");
    }
}
