#[cfg(test)]
mod priority_order_matching_tests {
    use crate::data::models::template::SegmentMatchingRule;
    use crate::data::models::ai_classification::AiClassification;
    use chrono::Utc;

    #[test]
    fn test_priority_order_rule_creation() {
        let category_ids = vec!["cat1".to_string(), "cat2".to_string(), "cat3".to_string()];
        let rule = SegmentMatchingRule::PriorityOrder { 
            category_ids: category_ids.clone() 
        };

        match rule {
            SegmentMatchingRule::PriorityOrder { category_ids: ids } => {
                assert_eq!(ids.len(), 3);
                assert_eq!(ids[0], "cat1");
                assert_eq!(ids[1], "cat2");
                assert_eq!(ids[2], "cat3");
            }
            _ => panic!("Expected PriorityOrder rule"),
        }
    }

    #[test]
    fn test_priority_order_rule_display_name() {
        let category_ids = vec!["cat1".to_string(), "cat2".to_string()];
        let rule = SegmentMatchingRule::PriorityOrder { category_ids };

        let display_name = rule.display_name();
        assert_eq!(display_name, "按顺序匹配: 2 个分类");
    }

    #[test]
    fn test_priority_order_rule_helper_methods() {
        let category_ids = vec!["cat1".to_string()];
        let rule = SegmentMatchingRule::PriorityOrder { 
            category_ids: category_ids.clone() 
        };

        assert!(rule.is_priority_order());
        assert!(!rule.is_fixed_material());
        assert!(!rule.is_ai_classification());
        assert!(!rule.is_random_match());

        let retrieved_ids = rule.get_category_ids();
        assert!(retrieved_ids.is_some());
        assert_eq!(retrieved_ids.unwrap(), &category_ids);
    }

    #[test]
    fn test_ai_classification_with_weight() {
        let now = Utc::now();
        let classification = AiClassification {
            id: "test_id".to_string(),
            name: "全身".to_string(),
            prompt_text: "头顶到脚底完整入镜".to_string(),
            description: Some("全身分类".to_string()),
            is_active: true,
            sort_order: 1,
            weight: 10,
            created_at: now,
            updated_at: now,
        };

        assert_eq!(classification.weight, 10);
        assert_eq!(classification.name, "全身");
    }

    #[test]
    fn test_weight_sorting_logic() {
        let now = Utc::now();
        
        let mut classifications = vec![
            AiClassification {
                id: "cat1".to_string(),
                name: "上半身".to_string(),
                prompt_text: "上半身".to_string(),
                description: None,
                is_active: true,
                sort_order: 2,
                weight: 8,
                created_at: now,
                updated_at: now,
            },
            AiClassification {
                id: "cat2".to_string(),
                name: "全身".to_string(),
                prompt_text: "全身".to_string(),
                description: None,
                is_active: true,
                sort_order: 1,
                weight: 10,
                created_at: now,
                updated_at: now,
            },
            AiClassification {
                id: "cat3".to_string(),
                name: "下半身".to_string(),
                prompt_text: "下半身".to_string(),
                description: None,
                is_active: true,
                sort_order: 3,
                weight: 7,
                created_at: now,
                updated_at: now,
            },
        ];

        // 按权重降序排序
        classifications.sort_by(|a, b| b.weight.cmp(&a.weight));

        assert_eq!(classifications[0].name, "全身");
        assert_eq!(classifications[0].weight, 10);
        assert_eq!(classifications[1].name, "上半身");
        assert_eq!(classifications[1].weight, 8);
        assert_eq!(classifications[2].name, "下半身");
        assert_eq!(classifications[2].weight, 7);
    }

    #[test]
    fn test_empty_category_ids_in_priority_order() {
        let rule = SegmentMatchingRule::PriorityOrder { 
            category_ids: vec![] 
        };

        let display_name = rule.display_name();
        assert_eq!(display_name, "按顺序匹配: 0 个分类");

        let category_ids = rule.get_category_ids();
        assert!(category_ids.is_some());
        assert!(category_ids.unwrap().is_empty());
    }
}
