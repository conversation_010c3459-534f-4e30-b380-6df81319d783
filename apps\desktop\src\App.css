@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  /* 改进字体渲染 */
  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* 改进滚动行为 */
  * {
    scroll-behavior: smooth;
  }

  /* 改进焦点样式 */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 按钮样式系统 */
  .btn {
    @apply inline-flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden;
  }

  /* 按钮悬停效果 */
  .btn::before {
    @apply absolute inset-0 bg-white opacity-0 transition-opacity duration-200;
    content: '';
  }

  .btn:hover::before {
    @apply opacity-10;
  }

  .btn:active {
    @apply transform scale-95;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 focus:ring-primary-500 active:from-primary-800 active:to-primary-900 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500 border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:ring-gray-500 rounded-lg;
  }

  .btn-danger {
    @apply bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 focus:ring-red-500 active:from-red-800 active:to-red-900 shadow-lg hover:shadow-xl;
  }

  .btn-success {
    @apply bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 focus:ring-green-500 active:from-green-800 active:to-green-900 shadow-lg hover:shadow-xl;
  }

  .btn-warning {
    @apply bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 focus:ring-yellow-500 active:from-yellow-700 active:to-yellow-800 shadow-lg hover:shadow-xl;
  }

  .btn-sm {
    @apply px-3 py-2 text-xs rounded-lg;
  }

  .btn-lg {
    @apply px-6 py-3 text-base rounded-2xl;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg rounded-2xl;
  }

  /* 表单样式系统 */
  .form-group {
    @apply space-y-2;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 text-gray-900;
  }

  .form-input:hover {
    @apply border-gray-300;
  }

  .form-input.error {
    @apply border-red-300 bg-red-50 focus:ring-red-500 focus:border-red-500;
  }

  .form-input.success {
    @apply border-green-300 bg-green-50 focus:ring-green-500 focus:border-green-500;
  }

  .form-textarea {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 resize-none placeholder-gray-400 text-gray-900;
  }

  .form-textarea:hover {
    @apply border-gray-300;
  }

  .form-textarea.error {
    @apply border-red-300 bg-red-50 focus:ring-red-500 focus:border-red-500;
  }

  .form-textarea.success {
    @apply border-green-300 bg-green-50 focus:ring-green-500 focus:border-green-500;
  }

  .form-label {
    @apply block text-sm font-semibold text-gray-800 mb-2;
  }

  .form-label.required::after {
    @apply text-red-500 ml-1;
    content: '*';
  }

  .form-error {
    @apply flex items-center gap-2 text-sm text-red-600 mt-2 font-medium;
  }

  .form-success {
    @apply flex items-center gap-2 text-sm text-green-600 mt-2 font-medium;
  }

  .form-hint {
    @apply text-xs text-gray-500 mt-1;
  }

  .form-info {
    @apply text-sm text-blue-600 mt-2 font-medium;
  }

  .form-warning {
    @apply text-sm text-yellow-600 mt-2 font-medium;
  }

  /* 加载动画增强 */
  .spinner {
    @apply animate-spin;
  }

  .loading-pulse {
    @apply animate-pulse;
  }

  .loading-bounce {
    @apply animate-bounce;
  }

  /* 模态框样式增强 */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in;
  }

  .modal {
    @apply bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden animate-scale-in;
  }

  .modal-lg {
    @apply max-w-2xl;
  }

  .modal-xl {
    @apply max-w-4xl;
  }

  .modal-header {
    @apply flex items-center justify-between p-6 border-b border-gray-100 bg-gray-25;
  }

  .modal-close {
    @apply p-2 hover:bg-gray-100 rounded-xl transition-all duration-200 text-gray-400 hover:text-gray-600;
  }

  .modal-body {
    @apply p-6;
  }

  .modal-footer {
    @apply flex gap-3 p-6 border-t border-gray-100 bg-gray-25;
  }

  /* 卡片样式增强 */
  .card {
    @apply bg-white rounded-2xl border border-gray-100 shadow-sm hover:shadow-lg hover:shadow-primary-500/10 transition-all duration-300 overflow-hidden;
  }

  .card-elevated {
    @apply shadow-lg hover:shadow-xl;
  }

  .card-interactive {
    @apply cursor-pointer hover:-translate-y-1 hover:scale-[1.02] active:scale-[0.98];
  }

  .card-header {
    @apply p-6 border-b border-gray-100;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply p-6 border-t border-gray-100 bg-gray-25;
  }

  /* 菜单样式增强 */
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-xl border border-gray-100 py-2 z-50 animate-fade-in-down;
  }

  .dropdown-item {
    @apply flex items-center gap-3 w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-all duration-150 mx-1 rounded-lg;
  }

  .dropdown-item.danger {
    @apply text-red-600 hover:bg-red-50 hover:text-red-700;
  }

  .dropdown-item.success {
    @apply text-green-600 hover:bg-green-50 hover:text-green-700;
  }

  .dropdown-divider {
    @apply my-1 border-t border-gray-100;
  }

  /* 响应式工具类增强 */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 状态指示器 */
  .status-indicator {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium;
  }

  .status-success {
    @apply bg-green-100 text-green-800;
  }

  .status-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-error {
    @apply bg-red-100 text-red-800;
  }

  .status-info {
    @apply bg-blue-100 text-blue-800;
  }

  .status-neutral {
    @apply bg-gray-100 text-gray-800;
  }

  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-gray-100 text-gray-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  /* 分隔线样式 */
  .divider {
    @apply border-t border-gray-200;
  }

  .divider-vertical {
    @apply border-l border-gray-200;
  }

  /* 自定义滚动条增强 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-50 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400 transition-colors duration-200;
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    @apply bg-gray-50;
  }

  /* 焦点样式增强 */
  .focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* 交互状态 */
  .interactive {
    @apply transition-all duration-200 cursor-pointer;
  }

  .interactive:hover {
    @apply transform scale-105;
  }

  .interactive:active {
    @apply transform scale-95;
  }

  /* 玻璃态效果 */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-md border border-white border-opacity-20;
  }

  .glass-dark {
    @apply bg-gray-900 bg-opacity-80 backdrop-blur-md border border-gray-700 border-opacity-20;
  }

  /* 渐变背景 */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-gray-500 to-gray-600;
  }

  .gradient-success {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }

  .gradient-warning {
    @apply bg-gradient-to-r from-yellow-500 to-yellow-600;
  }

  .gradient-error {
    @apply bg-gradient-to-r from-red-500 to-red-600;
  }

  /* 文本渐变 */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent;
  }

  /* 阴影变体 */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.2);
  }

  .shadow-colored {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }

  /* Markdown 和代码高亮样式 */
  .prose {
    color: #374151;
    max-width: none;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #111827;
    font-weight: 600;
  }

  .prose p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .prose ul, .prose ol {
    margin-bottom: 1rem;
  }

  .prose li {
    margin-bottom: 0.25rem;
  }

  .prose blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    font-style: italic;
    color: #6b7280;
    margin: 1rem 0;
  }

  .prose code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'Courier New', monospace;
  }

  .prose pre {
    background-color: #f3f4f6;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
  }

  .prose pre code {
    background-color: transparent;
    padding: 0;
  }

  /* 简化的代码高亮样式 */
  .hljs {
    background: #f3f4f6 !important;
    color: #374151 !important;
  }

  .hljs-keyword, .hljs-selector-tag, .hljs-title {
    color: #7c3aed !important;
  }

  .hljs-string, .hljs-attr {
    color: #059669 !important;
  }

  .hljs-comment, .hljs-quote {
    color: #6b7280 !important;
    font-style: italic;
  }

  .hljs-number, .hljs-literal {
    color: #dc2626 !important;
  }

  .hljs-function, .hljs-class {
    color: #2563eb !important;
  }
}