import React, { useState } from 'react';
import { Model, CreateDynamicRequest, AIModelOption } from '../types/model';
import { Modal } from './Modal';
import {
  XMarkIcon,
  PhotoIcon,
  SparklesIcon,
  CpuChipIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline';

interface CreateDynamicModalProps {
  model: Model;
  onSubmit: (data: CreateDynamicRequest) => void;
  onCancel: () => void;
}

const CreateDynamicModal: React.FC<CreateDynamicModalProps> = ({
  model,
  onSubmit,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    prompt: '',
    source_image_path: '',
    ai_model: '极梦',
    video_count: 1
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 可用的AI模型选项
  const aiModelOptions: AIModelOption[] = [
    {
      id: 'jimeng',
      name: '极梦',
      description: '高质量视频生成模型',
      is_available: true,
      max_video_count: 9
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageSelect = async () => {
    try {
      // TODO: 实现图片选择功能
      // const selectedPath = await systemService.selectImageFile();
      // if (selectedPath) {
      //   handleInputChange('source_image_path', selectedPath);
      // }
      console.log('选择图片功能待实现');
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.prompt.trim()) {
      newErrors.prompt = '请输入提示词';
    }

    if (!formData.source_image_path) {
      newErrors.source_image_path = '请选择源图片';
    }

    if (formData.video_count < 1 || formData.video_count > 9) {
      newErrors.video_count = '视频个数必须在1-9之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const submitData: CreateDynamicRequest = {
        model_id: model.id,
        title: undefined,
        description: '模特动态', // 使用默认描述
        prompt: formData.prompt.trim(),
        source_image_path: formData.source_image_path,
        ai_model: formData.ai_model,
        video_count: formData.video_count
      };

      onSubmit(submitData);
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onCancel}
      title="生成视频"
      subtitle={`为 ${model.name} 生成AI视频`}
      icon={<SparklesIcon className="h-6 w-6" />}
      size="lg"
      variant="default"
      closeOnBackdropClick={false}
    >
      {/* 表单内容 */}
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">

          {/* 提示词 */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
              <SparklesIcon className="h-4 w-4" />
              AI提示词 *
            </label>
            <textarea
              value={formData.prompt}
              onChange={(e) => handleInputChange('prompt', e.target.value)}
              placeholder="描述您希望生成的视频内容..."
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-none text-sm ${errors.prompt ? 'border-red-300' : 'border-gray-300'
                }`}
            />
            {errors.prompt && (
              <p className="mt-1 text-sm text-red-600">{errors.prompt}</p>
            )}
          </div>

          {/* 源图片 */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
              <PhotoIcon className="h-4 w-4" />
              源图片 *
            </label>
            <div className="space-y-2">
              <button
                type="button"
                onClick={handleImageSelect}
                className={`w-full p-3 border-2 border-dashed rounded-lg transition-colors ${errors.source_image_path
                  ? 'border-red-300 bg-red-50'
                  : 'border-gray-300 hover:border-primary-400 hover:bg-primary-50'
                  }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <PhotoIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-600">选择图片</span>
                </div>
              </button>

              {formData.source_image_path && (
                <div className="relative">
                  <img
                    src={formData.source_image_path}
                    alt="源图片预览"
                    className="w-full h-32 object-contain bg-gray-50 rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => handleInputChange('source_image_path', '')}
                    className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </div>
              )}

              {errors.source_image_path && (
                <p className="text-sm text-red-600">{errors.source_image_path}</p>
              )}
            </div>
          </div>

          {/* AI模型选择 */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
              <CpuChipIcon className="h-4 w-4" />
              AI模型
            </label>
            <select
              value={formData.ai_model}
              onChange={(e) => handleInputChange('ai_model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors text-sm"
            >
              {aiModelOptions.map((option) => (
                <option key={option.id} value={option.name} disabled={!option.is_available}>
                  {option.name} - {option.description}
                </option>
              ))}
            </select>
          </div>

          {/* 视频个数 */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
              <VideoCameraIcon className="h-4 w-4" />
              生成视频个数
            </label>
            <div className="flex items-center gap-3">
              <input
                type="range"
                min="1"
                max="9"
                value={formData.video_count}
                onChange={(e) => handleInputChange('video_count', parseInt(e.target.value))}
                className="flex-1"
              />
              <div className="flex items-center gap-1">
                <span className="text-sm font-medium text-gray-700">
                  {formData.video_count}
                </span>
                <span className="text-xs text-gray-500">个</span>
              </div>
            </div>
            {errors.video_count && (
              <p className="mt-1 text-sm text-red-600">{errors.video_count}</p>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end gap-3 p-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? '生成中...' : '开始生成'}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateDynamicModal;
