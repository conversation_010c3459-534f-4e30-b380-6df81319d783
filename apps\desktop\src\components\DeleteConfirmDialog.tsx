import React from 'react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { LoadingSpinner } from './LoadingSpinner';
import { Modal } from './Modal';

interface DeleteConfirmDialogProps {
  /** 是否显示对话框 */
  isOpen: boolean;
  /** 对话框标题 */
  title: string;
  /** 确认消息 */
  message: string;
  /** 要删除的项目名称 */
  itemName?: string;
  /** 是否正在删除 */
  deleting?: boolean;
  /** 确认删除回调 */
  onConfirm: () => void;
  /** 取消回调 */
  onCancel: () => void;
}

/**
 * 删除确认对话框组件
 * 遵循前端开发规范的确认对话框设计，提供安全的删除确认流程
 */
export const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  itemName,
  deleting,
  onConfirm,
  onCancel,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={deleting ? () => {} : onCancel}
      title={title}
      icon={<ExclamationTriangleIcon className="h-6 w-6" />}
      size="sm"
      variant="danger"
      closeOnBackdropClick={!deleting}
      closeOnEscape={!deleting}
    >
      {/* 优化的内容区域 */}
      <div className="p-6">
        <p className="text-gray-600 mb-4 leading-relaxed">
          {message}
        </p>

        {itemName && (
          <div className="mb-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
            <p className="text-sm font-medium text-gray-700 mb-1">要删除的项目：</p>
            <p className="font-semibold text-gray-900 truncate" title={itemName}>
              {itemName}
            </p>
          </div>
        )}

        <div className="p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-xl mb-6">
          <div className="flex items-start gap-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800 mb-1">重要提醒</p>
              <p className="text-sm text-red-700">
                此操作无法撤销，请确认您要继续。
              </p>
            </div>
          </div>
        </div>
        {/* 按钮区域 */}
        <div className="flex gap-3 justify-end">
          <button
            type="button"
            onClick={onCancel}
            disabled={deleting}
            className="px-4 py-2.5 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
          <button
            type="button"
            onClick={onConfirm}
            disabled={deleting}
            className="px-4 py-2.5 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md min-w-[100px]"
          >
            {deleting ? (
              <div className="flex items-center justify-center gap-2">
                <LoadingSpinner size="small" />
                <span>删除中...</span>
              </div>
            ) : (
              '确认删除'
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};
