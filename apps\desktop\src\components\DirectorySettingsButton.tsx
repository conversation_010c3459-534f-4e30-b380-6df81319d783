import React, { useState } from 'react';
import { Settings } from 'lucide-react';
import DirectorySettingsModal from './DirectorySettingsModal';

interface DirectorySettingsButtonProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
}

const DirectorySettingsButton: React.FC<DirectorySettingsButtonProps> = ({
  className = '',
  size = 'md',
  variant = 'secondary',
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md',
    secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300 border border-gray-300 hover:border-gray-400',
    ghost: 'bg-transparent text-gray-600 hover:bg-gray-100 hover:text-gray-800',
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={`
          flex items-center space-x-2 rounded transition-all duration-200
          hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${sizeClasses[size]}
          ${variantClasses[variant]}
          ${className}
        `}
        title="目录设置 - 管理导入导出的默认目录"
      >
        <Settings className={`${iconSizes[size]} transition-transform duration-200 group-hover:rotate-90`} />
        <span>目录设置</span>
      </button>

      <DirectorySettingsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
};

export default DirectorySettingsButton;
