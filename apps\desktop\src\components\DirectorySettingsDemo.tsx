import React, { useState } from 'react';
import DirectorySettingsButton from './DirectorySettingsButton';
import QuickDirectoryButton from './QuickDirectoryButton';
import { invoke } from '@tauri-apps/api/core';

const DirectorySettingsDemo: React.FC = () => {
  const [selectedDirectory, setSelectedDirectory] = useState<string>('');
  const [autoRememberStatus, setAutoRememberStatus] = useState<boolean | null>(null);

  const checkAutoRememberStatus = async () => {
    try {
      const status = await invoke<boolean>('is_auto_remember_enabled');
      setAutoRememberStatus(status);
    } catch (error) {
      console.error('检查自动记忆状态失败:', error);
    }
  };

  const handleDirectorySelected = (directory: string) => {
    setSelectedDirectory(directory);
  };

  React.useEffect(() => {
    checkAutoRememberStatus();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">目录设置功能演示</h1>
      
      {/* 状态显示 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">当前状态</h2>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">自动记忆功能:</span>
            <span className={`text-sm font-medium px-2 py-1 rounded ${
              autoRememberStatus === true 
                ? 'bg-green-100 text-green-800' 
                : autoRememberStatus === false 
                ? 'bg-red-100 text-red-800' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {autoRememberStatus === true ? '已启用' : autoRememberStatus === false ? '已关闭' : '检查中...'}
            </span>
            <button
              onClick={checkAutoRememberStatus}
              className="text-xs text-blue-600 hover:text-blue-800 underline"
            >
              刷新状态
            </button>
          </div>
          {selectedDirectory && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">最后选择的目录:</span>
              <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                {selectedDirectory}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* 按钮演示 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 目录设置按钮 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">目录设置按钮</h3>
          <p className="text-sm text-gray-600 mb-4">
            点击打开目录设置对话框，可以管理所有导入导出的默认目录
          </p>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500 w-16">小号:</span>
              <DirectorySettingsButton size="sm" variant="primary" />
              <DirectorySettingsButton size="sm" variant="secondary" />
              <DirectorySettingsButton size="sm" variant="ghost" />
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500 w-16">中号:</span>
              <DirectorySettingsButton size="md" variant="primary" />
              <DirectorySettingsButton size="md" variant="secondary" />
              <DirectorySettingsButton size="md" variant="ghost" />
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500 w-16">大号:</span>
              <DirectorySettingsButton size="lg" variant="primary" />
              <DirectorySettingsButton size="lg" variant="secondary" />
              <DirectorySettingsButton size="lg" variant="ghost" />
            </div>
          </div>
        </div>

        {/* 快速目录选择按钮 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">快速目录选择</h3>
          <p className="text-sm text-gray-600 mb-4">
            直接选择特定类型的目录，会自动保存到对应的设置中
          </p>
          <div className="space-y-3">
            <QuickDirectoryButton
              settingType="material_import"
              onDirectorySelected={handleDirectorySelected}
              size="md"
              variant="primary"
            />
            <QuickDirectoryButton
              settingType="template_import"
              onDirectorySelected={handleDirectorySelected}
              size="md"
              variant="secondary"
            />
            <QuickDirectoryButton
              settingType="jianying_export"
              onDirectorySelected={handleDirectorySelected}
              size="md"
              variant="ghost"
            />
            <QuickDirectoryButton
              settingType="project_export"
              onDirectorySelected={handleDirectorySelected}
              size="sm"
              variant="secondary"
              showLabel={false}
            />
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded-lg border border-blue-200 p-4 mt-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">使用说明</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>目录设置按钮</strong>：打开完整的目录设置对话框，可以管理所有类型的默认目录</li>
          <li>• <strong>快速目录选择</strong>：直接选择特定类型的目录，立即保存到设置中</li>
          <li>• <strong>自动记忆功能</strong>：启用后，每次选择文件时会自动更新对应的默认目录</li>
          <li>• <strong>视觉反馈</strong>：开关状态有明显的颜色和图标区别，便于识别当前状态</li>
        </ul>
      </div>
    </div>
  );
};

export default DirectorySettingsDemo;
