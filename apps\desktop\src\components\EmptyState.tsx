import React from 'react';
import { FolderPlus, Sparkles, FileText, Users, Video, Image, Music, Search, Inbox, AlertCircle } from 'lucide-react';
import { InteractiveButton } from './InteractiveButton';

interface EmptyStateProps {
  variant?: 'default' | 'search' | 'error' | 'loading' | 'success';
  icon?: React.ReactNode;
  title: string;
  description: string;
  actionText?: string;
  onAction?: () => void;
  secondaryActionText?: string;
  onSecondaryAction?: () => void;
  illustration?: 'folder' | 'search' | 'users' | 'video' | 'image' | 'music' | 'document' | 'inbox' | 'error';
  size?: 'sm' | 'md' | 'lg';
  showTips?: boolean;
  tips?: string[];
  className?: string;
}

/**
 * 增强的空状态组件
 * 支持多种变体、插图和交互方式
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  variant = 'default',
  icon,
  title,
  description,
  actionText,
  onAction,
  secondaryActionText,
  onSecondaryAction,
  illustration = 'folder',
  size = 'md',
  showTips = false,
  tips = [],
  className = '',
}) => {
  const getIllustrationIcon = () => {
    if (icon) return icon;

    const iconMap = {
      folder: FolderPlus,
      search: Search,
      users: Users,
      video: Video,
      image: Image,
      music: Music,
      document: FileText,
      inbox: Inbox,
      error: AlertCircle,
    };

    const IconComponent = iconMap[illustration];
    return <IconComponent size={getIconSize()} className={getIconColor()} />;
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 48;
      case 'lg': return 80;
      default: return 64;
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'error': return 'text-red-500';
      case 'search': return 'text-blue-500';
      case 'success': return 'text-green-500';
      default: return 'text-primary-600';
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'error':
        return {
          bg: 'from-red-50 to-red-100',
          border: 'border-red-200',
          glow: 'from-red-100 to-red-200',
        };
      case 'search':
        return {
          bg: 'from-blue-50 to-blue-100',
          border: 'border-blue-200',
          glow: 'from-blue-100 to-blue-200',
        };
      case 'success':
        return {
          bg: 'from-green-50 to-green-100',
          border: 'border-green-200',
          glow: 'from-green-100 to-green-200',
        };
      default:
        return {
          bg: 'from-primary-50 to-blue-50',
          border: 'border-primary-100',
          glow: 'from-primary-100 to-blue-100',
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'py-12',
          iconContainer: 'p-4 rounded-2xl',
          title: 'text-lg',
          description: 'text-sm',
          spacing: 'space-y-3',
        };
      case 'lg':
        return {
          container: 'py-24',
          iconContainer: 'p-8 rounded-3xl',
          title: 'text-3xl',
          description: 'text-xl',
          spacing: 'space-y-6',
        };
      default:
        return {
          container: 'py-20',
          iconContainer: 'p-6 rounded-3xl',
          title: 'text-2xl',
          description: 'text-lg',
          spacing: 'space-y-4',
        };
    }
  };

  const styles = getVariantStyles();
  const sizeClasses = getSizeClasses();

  return (
    <div className={`flex flex-col items-center justify-center ${sizeClasses.container} text-center animate-fade-in-up ${className}`}>
      {/* 图标区域 */}
      <div className="relative mb-8">
        {/* 背景装饰 */}
        <div className={`absolute inset-0 bg-gradient-to-r ${styles.glow} rounded-full blur-2xl opacity-50 scale-150`}></div>

        {/* 主图标 */}
        <div className={`relative ${sizeClasses.iconContainer} bg-gradient-to-r ${styles.bg} border ${styles.border}`}>
          {getIllustrationIcon()}

          {/* 装饰性小图标 */}
          {variant === 'default' && (
            <div className="absolute -top-2 -right-2 p-2 bg-yellow-100 rounded-full animate-pulse">
              <Sparkles size={16} className="text-yellow-600" />
            </div>
          )}
        </div>
      </div>

      {/* 文本内容 */}
      <div className={`max-w-md ${sizeClasses.spacing}`}>
        <h3 className={`${sizeClasses.title} font-bold text-gray-900 mb-3`}>
          {title}
        </h3>
        <p className={`text-gray-600 leading-relaxed ${sizeClasses.description}`}>
          {description}
        </p>
      </div>

      {/* 操作按钮 */}
      {(actionText || secondaryActionText) && (
        <div className="mt-8 flex flex-col sm:flex-row gap-3">
          {actionText && onAction && (
            <InteractiveButton
              variant={variant === 'error' ? 'danger' : 'primary'}
              size="lg"
              onClick={onAction}
              icon={illustration === 'folder' ? <FolderPlus size={20} /> : undefined}
              className="shadow-glow animate-pulse-slow"
            >
              {actionText}
            </InteractiveButton>
          )}

          {secondaryActionText && onSecondaryAction && (
            <InteractiveButton
              variant="secondary"
              size="lg"
              onClick={onSecondaryAction}
            >
              {secondaryActionText}
            </InteractiveButton>
          )}
        </div>
      )}

      {/* 提示信息 */}
      {showTips && tips.length > 0 && (
        <div className="mt-6 space-y-2">
          {tips.map((tip, index) => (
            <p key={index} className="text-sm text-gray-500 flex items-center justify-center gap-2">
              <span className="text-yellow-500">💡</span>
              {tip}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * 预设的空状态组件
 */

// 项目列表空状态
export const EmptyProjectList: React.FC<{ onCreateProject: () => void }> = ({ onCreateProject }) => (
  <EmptyState
    illustration="folder"
    title="还没有项目"
    description="创建您的第一个项目开始使用 MixVideo"
    actionText="新建项目"
    onAction={onCreateProject}
    showTips
    tips={[
      "提示：您可以通过拖拽文件夹到此处快速创建项目",
      "支持导入现有的视频项目文件夹"
    ]}
  />
);

// 模特列表空状态
export const EmptyModelList: React.FC<{ onCreateModel: () => void }> = ({ onCreateModel }) => (
  <EmptyState
    illustration="users"
    title="还没有模特"
    description="添加模特信息以便更好地管理您的素材"
    actionText="添加模特"
    onAction={onCreateModel}
    size="md"
    showTips
    tips={["模特信息有助于素材的分类和管理"]}
  />
);

// 素材列表空状态
export const EmptyMaterialList: React.FC<{ onImportMaterial: () => void }> = ({ onImportMaterial }) => (
  <EmptyState
    illustration="video"
    title="还没有素材"
    description="导入视频、音频或图片素材开始创作"
    actionText="导入素材"
    onAction={onImportMaterial}
    showTips
    tips={[
      "支持拖拽文件到此处快速导入",
      "支持批量导入多个文件"
    ]}
  />
);

// 模板列表空状态
export const EmptyTemplateList: React.FC<{ onImportTemplate: () => void }> = ({ onImportTemplate }) => (
  <EmptyState
    illustration="document"
    title="还没有模板"
    description="导入模板文件来快速创建视频项目"
    actionText="导入模板"
    onAction={onImportTemplate}
    showTips
    tips={["模板可以帮助您快速搭建视频项目结构"]}
  />
);

// 搜索结果空状态
export const EmptySearchResult: React.FC<{ query: string; onClearSearch?: () => void }> = ({
  query,
  onClearSearch
}) => (
  <EmptyState
    variant="search"
    illustration="search"
    title="没有找到相关内容"
    description={`没有找到与 "${query}" 相关的结果，请尝试其他关键词`}
    actionText="清除搜索"
    onAction={onClearSearch}
    size="sm"
  />
);

// 错误状态
export const ErrorState: React.FC<{
  title?: string;
  description?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
}> = ({
  title = "出现了一些问题",
  description = "请稍后重试，或联系技术支持",
  onRetry,
  onGoBack
}) => (
  <EmptyState
    variant="error"
    illustration="error"
    title={title}
    description={description}
    actionText={onRetry ? "重试" : undefined}
    onAction={onRetry}
    secondaryActionText={onGoBack ? "返回" : undefined}
    onSecondaryAction={onGoBack}
    size="md"
  />
);

// 加载状态
export const LoadingState: React.FC<{ message?: string }> = ({
  message = "正在加载..."
}) => (
  <EmptyState
    variant="loading"
    illustration="inbox"
    title={message}
    description="请稍候，正在处理您的请求"
    size="sm"
  />
);
