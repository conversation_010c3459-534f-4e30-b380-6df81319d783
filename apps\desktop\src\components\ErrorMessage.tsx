import React from 'react';
import { AlertCircle, RefreshCw, X, AlertTriangle, Info, CheckCircle } from 'lucide-react';

interface ErrorMessageProps {
  message: string;
  type?: 'error' | 'warning' | 'info' | 'success';
  onRetry?: () => void;
  onDismiss?: () => void;
  title?: string;
}

/**
 * 增强的消息组件
 * 支持多种消息类型和更好的视觉设计
 */
export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  type = 'error',
  onRetry,
  onDismiss,
  title
}) => {
  const getConfig = () => {
    switch (type) {
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600',
          icon: AlertTriangle,
          defaultTitle: '警告'
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600',
          icon: Info,
          defaultTitle: '信息'
        };
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-600',
          icon: CheckCircle,
          defaultTitle: '成功'
        };
      default:
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600',
          icon: AlertCircle,
          defaultTitle: '错误'
        };
    }
  };

  const config = getConfig();
  const Icon = config.icon;

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-2xl p-6 animate-fade-in-up shadow-sm`}>
      <div className="flex items-start gap-4">
        {/* 图标 */}
        <div className={`${config.iconColor} flex-shrink-0 mt-0.5`}>
          <Icon size={24} />
        </div>

        {/* 内容 */}
        <div className="flex-1 min-w-0">
          {/* 标题 */}
          {(title || config.defaultTitle) && (
            <h4 className={`${config.textColor} font-semibold text-lg mb-2`}>
              {title || config.defaultTitle}
            </h4>
          )}

          {/* 消息内容 */}
          <p className={`${config.textColor} leading-relaxed`}>
            {message}
          </p>

          {/* 操作按钮 */}
          {(onRetry || onDismiss) && (
            <div className="flex items-center gap-3 mt-4">
              {onRetry && (
                <button
                  className="btn btn-secondary btn-sm"
                  onClick={onRetry}
                >
                  <RefreshCw size={14} />
                  重试
                </button>
              )}
              {onDismiss && (
                <button
                  className="btn btn-ghost btn-sm"
                  onClick={onDismiss}
                  title="关闭"
                >
                  <X size={14} />
                  关闭
                </button>
              )}
            </div>
          )}
        </div>

        {/* 右上角关闭按钮 */}
        {onDismiss && (
          <button
            className={`${config.iconColor} hover:bg-white hover:bg-opacity-50 p-1 rounded-lg transition-colors duration-200 flex-shrink-0`}
            onClick={onDismiss}
            title="关闭"
          >
            <X size={16} />
          </button>
        )}
      </div>
    </div>
  );
};
