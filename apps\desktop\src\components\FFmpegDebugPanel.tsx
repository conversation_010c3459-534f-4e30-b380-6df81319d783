import React, { useState } from 'react';
import { Play, AlertCircle } from 'lucide-react';
import { useMaterialStore } from '../store/materialStore';

/**
 * FFmpeg调试面板组件
 * 用于测试和调试FFmpeg功能
 */
export const FFmpegDebugPanel: React.FC = () => {
  const {
    // testSceneDetection,
    // getFFmpegStatus,
    selectMaterialFiles
  } = useMaterialStore();

  const [testFilePath, setTestFilePath] = useState('');
  const [testResult, setTestResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);



  // 选择测试文件
  const handleSelectTestFile = async () => {
    try {
      const files = await selectMaterialFiles();
      if (files.length > 0) {
        setTestFilePath(files[0]);
      }
    } catch (error) {
      console.error('选择文件失败:', error);
    }
  };

  // 测试场景检测
  const handleTestSceneDetection = async () => {
    if (!testFilePath) {
      setTestResult('请先选择测试文件');
      return;
    }

    setIsLoading(true);
    setTestResult('正在测试场景检测...');
    
    try {
      // const result = await testSceneDetection(testFilePath);
      setTestResult('场景检测测试功能暂时不可用');
    } catch (error) {
      setTestResult(`测试失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleSelectTestFile}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              选择测试文件
            </button>
            {testFilePath && (
              <span className="text-sm text-gray-600 truncate max-w-md">
                {testFilePath}
              </span>
            )}
          </div>

          <button
            onClick={handleTestSceneDetection}
            disabled={!testFilePath || isLoading}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            <Play className="w-4 h-4 mr-2" />
            {isLoading ? '测试中...' : '测试场景检测'}
          </button>
        </div>
      </div>

      {/* 测试结果 */}
      {testResult && (
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-3">测试结果</h4>
          <div className="bg-gray-50 rounded p-4 text-sm font-mono whitespace-pre-wrap max-h-96 overflow-y-auto">
            {testResult}
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded p-4">
        <h4 className="font-medium text-blue-900 mb-2 flex items-center">
          <AlertCircle className="w-4 h-4 mr-2" />
          使用说明
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• 选择一个视频文件进行场景检测测试</li>
          <li>• 查看测试结果，包括元数据提取和场景检测信息</li>
          <li>• 如果场景检测失败，检查错误信息进行调试</li>
        </ul>
      </div>
    </div>
  );
};
