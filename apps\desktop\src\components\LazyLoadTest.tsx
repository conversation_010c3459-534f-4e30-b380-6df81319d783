import React from 'react';
import { useLazyLoad } from '../hooks/useLazyLoad';

/**
 * 懒加载测试组件
 * 用于验证useLazyLoad Hook是否正常工作
 */
export const LazyLoadTest: React.FC = () => {
  const { isVisible, elementRef } = useLazyLoad(0.1, '50px');

  return (
    <div className="space-y-8">
      <h2 className="text-xl font-bold">懒加载测试</h2>
      
      {/* 占位内容，让测试元素在视口外 */}
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <p className="text-gray-600">向下滚动查看懒加载效果</p>
      </div>
      
      {/* 懒加载测试元素 */}
      <div 
        ref={elementRef}
        className="h-64 bg-blue-100 border-2 border-blue-300 rounded-lg flex items-center justify-center"
      >
        <div className="text-center">
          <p className="text-lg font-semibold mb-2">
            {isVisible ? '✅ 元素已可见' : '⏳ 等待元素可见'}
          </p>
          <p className="text-sm text-gray-600">
            {isVisible ? '懒加载已触发！' : '滚动到此处触发懒加载'}
          </p>
        </div>
      </div>
      
      {/* 更多占位内容 */}
      <div className="h-screen bg-gray-100 flex items-center justify-center">
        <p className="text-gray-600">测试完成</p>
      </div>
    </div>
  );
};
