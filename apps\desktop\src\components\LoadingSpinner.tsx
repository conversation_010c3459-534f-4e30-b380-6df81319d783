import React from 'react';
import { Loader2, Circle } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

/**
 * 增强的加载动画组件
 * 支持多种动画样式和尺寸
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  text,
  variant = 'spinner',
  color = 'primary',
  className = ''
}) => {
  const sizeMap = {
    small: { icon: 16, text: 'text-sm', padding: 'p-3' },
    medium: { icon: 24, text: 'text-base', padding: 'p-6' },
    large: { icon: 32, text: 'text-lg', padding: 'p-8' }
  };

  const colorMap = {
    primary: 'text-primary-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  };

  const currentSize = sizeMap[size];
  const currentColor = colorMap[color];

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <Circle
                key={i}
                size={currentSize.icon / 3}
                className={`${currentColor} animate-loading-dots`}
                style={{ animationDelay: `${i * 0.2}s` }}
                fill="currentColor"
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div className={`w-${currentSize.icon / 4} h-${currentSize.icon / 4} ${currentColor.replace('text-', 'bg-')} rounded-full animate-pulse-slow`} />
        );

      case 'bars':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                className={`w-1 h-${currentSize.icon / 4} ${currentColor.replace('text-', 'bg-')} animate-loading-bars`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        );

      default:
        return (
          <Loader2
            size={currentSize.icon}
            className={`animate-spin ${currentColor}`}
          />
        );
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center gap-4 ${currentSize.padding} animate-fade-in ${className}`}>
      {/* 加载动画 */}
      <div className="relative">
        {/* 背景光晕效果 */}
        {color === 'primary' && (
          <div className="absolute inset-0 bg-primary-200 rounded-full blur-lg opacity-30 animate-pulse"></div>
        )}

        {/* 主要动画 */}
        <div className="relative">
          {renderSpinner()}
        </div>
      </div>

      {/* 加载文本 */}
      {text && (
        <div className="text-center">
          <span className={`${currentColor} ${currentSize.text} font-medium animate-pulse`}>
            {text}
          </span>

          {/* 动态点点点效果 */}
          <span className="inline-flex ml-1">
            {[0, 1, 2].map((i) => (
              <span
                key={i}
                className={`${currentColor} animate-pulse`}
                style={{ animationDelay: `${i * 0.3}s` }}
              >
                .
              </span>
            ))}
          </span>
        </div>
      )}
    </div>
  );
};
