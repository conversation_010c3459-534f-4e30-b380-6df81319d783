import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'spinner' | 'dots' | 'pulse';
  className?: string;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  message = '加载中...',
  size = 'medium',
  variant = 'spinner',
  className = ''
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-4 h-4';
      case 'large':
        return 'w-8 h-8';
      default:
        return 'w-6 h-6';
    }
  };

  const getTextSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'text-xs';
      case 'large':
        return 'text-lg';
      default:
        return 'text-sm';
    }
  };

  const renderSpinner = () => (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <Loader2 className={`${getSizeClasses()} text-blue-600 animate-spin`} />
      {message && (
        <span className={`${getTextSizeClasses()} text-gray-600 font-medium`}>
          {message}
        </span>
      )}
    </div>
  );

  const renderDots = () => (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      {message && (
        <span className={`${getTextSizeClasses()} text-gray-600 font-medium mr-2`}>
          {message}
        </span>
      )}
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
        <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
      </div>
    </div>
  );

  const renderPulse = () => (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <div className={`${getSizeClasses()} bg-blue-600 rounded-full animate-pulse`}></div>
      {message && (
        <span className={`${getTextSizeClasses()} text-gray-600 font-medium`}>
          {message}
        </span>
      )}
    </div>
  );

  switch (variant) {
    case 'dots':
      return renderDots();
    case 'pulse':
      return renderPulse();
    default:
      return renderSpinner();
  }
};

export default LoadingState;
