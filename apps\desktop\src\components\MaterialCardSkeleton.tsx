import React from 'react';

interface MaterialCardSkeletonProps {
  count?: number;
}

const MaterialCardSkeleton: React.FC<MaterialCardSkeletonProps> = ({ count = 6 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
          {/* 头部 */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              {/* 文件类型图标 */}
              <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
              <div className="space-y-2">
                {/* 文件名 */}
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                {/* 状态 */}
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
            {/* 操作按钮 */}
            <div className="w-8 h-8 bg-gray-200 rounded"></div>
          </div>

          {/* 基本信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-12"></div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-12"></div>
              <div className="h-4 bg-gray-200 rounded w-20"></div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-12"></div>
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-12"></div>
              <div className="h-4 bg-gray-200 rounded w-18"></div>
            </div>
          </div>

          {/* 元数据信息 */}
          <div className="border-t border-gray-100 pt-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-14"></div>
                <div className="h-4 bg-gray-200 rounded w-18"></div>
              </div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded w-12"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>

          {/* 底部操作区域 */}
          <div className="border-t border-gray-100 pt-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <div className="h-8 bg-gray-200 rounded w-16"></div>
                <div className="h-8 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="h-8 bg-gray-200 rounded w-12"></div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default MaterialCardSkeleton;
