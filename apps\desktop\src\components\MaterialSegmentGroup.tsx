import React from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Tag, 
  Users, 
  Clock, 
  BarChart3,
  FileVideo,
  AlertCircle
} from 'lucide-react';
import { 
  ClassificationGroup, 
  ModelGroup, 
  MaterialSegmentViewMode 
} from '../types/materialSegmentView';
import { MaterialSegmentCard } from './MaterialSegmentCard';

interface MaterialSegmentGroupProps {
  group: ClassificationGroup | ModelGroup;
  viewMode: MaterialSegmentViewMode;
  isExpanded: boolean;
  selectedSegmentIds: Set<string>;
  onToggleExpand: (groupId: string) => void;
  onSelectSegment: (segmentId: string) => void;
}

/**
 * MaterialSegment分组组件
 * 遵循 Tauri 开发规范的组件设计模式
 */
export const MaterialSegmentGroup: React.FC<MaterialSegmentGroupProps> = ({
  group,
  viewMode,
  isExpanded,
  selectedSegmentIds,
  onToggleExpand,
  onSelectSegment,
}) => {
  const isClassificationGroup = viewMode === MaterialSegmentViewMode.ByClassification;
  const groupId = isClassificationGroup 
    ? (group as ClassificationGroup).category 
    : (group as ModelGroup).model_id;
  const groupName = isClassificationGroup 
    ? (group as ClassificationGroup).category 
    : (group as ModelGroup).model_name;

  // 获取分组图标
  const getGroupIcon = () => {
    if (isClassificationGroup) {
      return <Tag className="w-5 h-5 text-blue-500" />;
    } else {
      return <Users className="w-5 h-5 text-green-500" />;
    }
  };

  // 获取分组颜色主题
  const getGroupTheme = () => {
    if (isClassificationGroup) {
      return {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        text: 'text-blue-900',
        accent: 'text-blue-600',
      };
    } else {
      return {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-900',
        accent: 'text-green-600',
      };
    }
  };

  const theme = getGroupTheme();

  // 格式化时长
  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}m`;
    } else {
      return `${Math.round(seconds / 3600)}h`;
    }
  };

  // 计算选中的片段数量
  const selectedCount = group.segments.filter(segment => 
    selectedSegmentIds.has(segment.segment.id)
  ).length;

  return (
    <div className={`border rounded-lg ${theme.border} ${theme.bg} overflow-hidden`}>
      {/* 分组头部 */}
      <div 
        className="p-4 cursor-pointer hover:bg-opacity-80 transition-colors"
        onClick={() => onToggleExpand(groupId)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* 展开/折叠图标 */}
            <button className="p-1 hover:bg-white hover:bg-opacity-50 rounded">
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              )}
            </button>

            {/* 分组图标 */}
            {getGroupIcon()}

            {/* 分组名称 */}
            <div>
              <h3 className={`font-medium ${theme.text}`}>
                {groupName}
                {groupName === '未分类' && (
                  <AlertCircle className="w-4 h-4 inline ml-1 text-orange-500" />
                )}
                {groupName === '未关联模特' && (
                  <AlertCircle className="w-4 h-4 inline ml-1 text-orange-500" />
                )}
              </h3>
              {selectedCount > 0 && (
                <p className="text-xs text-gray-600 mt-1">
                  已选择 {selectedCount} 个片段
                </p>
              )}
            </div>
          </div>

          {/* 分组统计信息 */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <BarChart3 className="w-4 h-4 text-gray-500" />
              <span className={theme.accent}>{group.segment_count}</span>
              <span className="text-gray-600">个片段</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className={theme.accent}>{formatDuration(group.total_duration)}</span>
            </div>
          </div>
        </div>

        {/* 分组进度条 */}
        {isClassificationGroup && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
              <span>分类完成度</span>
              <span>{Math.round((group.segment_count / (group.segment_count || 1)) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div 
                className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${Math.round((group.segment_count / (group.segment_count || 1)) * 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* 分组内容 */}
      {isExpanded && (
        <div className="border-t border-gray-200 bg-white">
          {group.segments.length > 0 ? (
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {group.segments.map((segmentWithDetails) => (
                  <MaterialSegmentCard
                    key={segmentWithDetails.segment.id}
                    segmentWithDetails={segmentWithDetails}
                    isSelected={selectedSegmentIds.has(segmentWithDetails.segment.id)}
                    onSelect={() => onSelectSegment(segmentWithDetails.segment.id)}
                    viewMode={viewMode}
                  />
                ))}
              </div>

              {/* 分组底部统计 */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <FileVideo className="w-4 h-4 mr-1" />
                      {group.segments.length} 个片段
                    </span>
                    <span className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      总时长 {formatDuration(group.total_duration)}
                    </span>
                  </div>
                  
                  {/* 平均时长 */}
                  <span className="text-xs text-gray-500">
                    平均时长 {formatDuration(group.total_duration / group.segment_count)}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <FileVideo className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p>该分组暂无片段</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
