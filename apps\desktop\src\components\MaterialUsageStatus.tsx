import React from 'react';
import { 
  MaterialUsageStatusProps, 
  defaultUsageStatusTheme, 
  usageStatusLabels 
} from '../types/materialUsage';

/**
 * 素材使用状态显示组件
 * 显示素材片段的使用状态、使用次数等信息
 */
export const MaterialUsageStatus: React.FC<MaterialUsageStatusProps> = ({
  segment,
  showDetails = false,
  size = 'medium'
}) => {
  // 确定使用状态
  const getUsageStatus = () => {
    if (!segment.is_used || segment.usage_count === 0) {
      return 'unused';
    } else if (segment.usage_count === 1) {
      return 'used';
    } else {
      return 'multiple';
    }
  };

  const status = getUsageStatus();
  const theme = defaultUsageStatusTheme[status];
  
  // 根据尺寸设置样式
  const sizeClasses = {
    small: 'px-2 py-1 text-xs',
    medium: 'px-3 py-1.5 text-sm',
    large: 'px-4 py-2 text-base'
  };

  const iconSizeClasses = {
    small: 'w-3 h-3',
    medium: 'w-4 h-4',
    large: 'w-5 h-5'
  };

  // 格式化最后使用时间
  const formatLastUsedTime = (timestamp?: string) => {
    if (!timestamp) return null;
    
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return null;
    }
  };

  // 获取状态图标
  const getStatusIcon = () => {
    switch (status) {
      case 'unused':
        return (
          <svg className={`${iconSizeClasses[size]} ${theme.icon}`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'used':
        return (
          <svg className={`${iconSizeClasses[size]} ${theme.icon}`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'multiple':
        return (
          <svg className={`${iconSizeClasses[size]} ${theme.icon}`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col gap-1">
      {/* 主要状态标签 */}
      <div className={`
        inline-flex items-center gap-1.5 rounded-full border
        ${theme.bg} ${theme.text} ${theme.border} ${sizeClasses[size]}
        font-medium
      `}>
        {getStatusIcon()}
        <span>{usageStatusLabels[status]}</span>
        {segment.usage_count > 0 && (
          <span className="ml-1 font-semibold">
            ({segment.usage_count})
          </span>
        )}
      </div>

      {/* 详细信息 */}
      {showDetails && (
        <div className="text-xs text-gray-500 space-y-1">
          {segment.usage_count > 0 && segment.last_used_at && (
            <div>
              最后使用: {formatLastUsedTime(segment.last_used_at)}
            </div>
          )}
          <div>
            片段时长: {segment.duration.toFixed(1)}秒
          </div>
          <div>
            文件大小: {(segment.file_size / 1024 / 1024).toFixed(1)}MB
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * 简化版的使用状态徽章
 */
export const MaterialUsageBadge: React.FC<{
  usageCount: number;
  isUsed: boolean;
  size?: 'small' | 'medium';
}> = ({ usageCount, isUsed, size = 'small' }) => {
  const status = !isUsed || usageCount === 0 ? 'unused' : 
                 usageCount === 1 ? 'used' : 'multiple';
  
  const theme = defaultUsageStatusTheme[status];
  
  const sizeClasses = size === 'small' ? 'px-2 py-0.5 text-xs' : 'px-2 py-1 text-sm';

  return (
    <span className={`
      inline-flex items-center rounded-full
      ${theme.bg} ${theme.text} ${theme.border} border
      ${sizeClasses} font-medium
    `}>
      {usageCount > 0 ? usageCount : '未使用'}
    </span>
  );
};

/**
 * 使用状态进度条
 */
export const MaterialUsageProgress: React.FC<{
  totalSegments: number;
  usedSegments: number;
  className?: string;
}> = ({ totalSegments, usedSegments, className = '' }) => {
  const usageRate = totalSegments > 0 ? (usedSegments / totalSegments) * 100 : 0;
  
  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between text-sm text-gray-600 mb-1">
        <span>使用进度</span>
        <span>{usedSegments}/{totalSegments} ({usageRate.toFixed(1)}%)</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${usageRate}%` }}
        />
      </div>
    </div>
  );
};

/**
 * 使用状态统计卡片
 */
export const MaterialUsageStatsCard: React.FC<{
  title: string;
  value: number;
  total?: number;
  icon?: React.ReactNode;
  color?: 'blue' | 'green' | 'orange' | 'gray';
}> = ({ title, value, total, icon, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
    gray: 'bg-gray-50 text-gray-700 border-gray-200'
  };

  return (
    <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium opacity-75">{title}</p>
          <p className="text-2xl font-bold">
            {value}
            {total !== undefined && (
              <span className="text-sm font-normal opacity-75">/{total}</span>
            )}
          </p>
        </div>
        {icon && (
          <div className="opacity-75">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};
