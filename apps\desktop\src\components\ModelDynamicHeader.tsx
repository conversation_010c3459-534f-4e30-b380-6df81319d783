import React from 'react';
import { Model, ModelDynamicStats, Gender } from '../types/model';
import {
  UserIcon,
  SparklesIcon,
  VideoCameraIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface ModelDynamicHeaderProps {
  model: Model;
  stats: ModelDynamicStats | null;
  onCreateDynamic: () => void;
}

const ModelDynamicHeader: React.FC<ModelDynamicHeaderProps> = ({
  model,
  stats,
  onCreateDynamic
}) => {
  const getGenderIcon = (gender: Gender) => {
    switch (gender) {
      case Gender.Male:
        return '♂️';
      case Gender.Female:
        return '♀️';
      default:
        return '⚧️';
    }
  };

  const getGenderColor = (gender: Gender) => {
    switch (gender) {
      case Gender.Male:
        return 'text-blue-600 bg-blue-100';
      case Gender.Female:
        return 'text-pink-600 bg-pink-100';
      default:
        return 'text-purple-600 bg-purple-100';
    }
  };

  return (
    <div className="bg-gradient-to-r from-white via-primary-50/30 to-white rounded-xl shadow-sm border border-gray-200/50 p-6 relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100/30 to-primary-200/30 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-primary-100/20 to-primary-200/20 rounded-full translate-y-12 -translate-x-12 opacity-30"></div>

      <div className="relative z-10">
        {/* 主要信息区域 */}
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6 mb-6">
          {/* 头像和基本信息 */}
          <div className="flex items-center gap-4">
            <div className="relative">
              {model.avatar_path ? (
                <img
                  src={model.avatar_path}
                  alt={model.name}
                  className="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg"
                />
              ) : (
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center border-4 border-white shadow-lg">
                  <UserIcon className="h-8 w-8 text-gray-400" />
                </div>
              )}
              
              {/* 性别标识 */}
              <div className={`absolute -bottom-1 -right-1 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2 border-white shadow-sm ${getGenderColor(model.gender)}`}>
                {getGenderIcon(model.gender)}
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold text-gray-900">{model.name}</h1>
                {model.stage_name && (
                  <span className="text-lg text-gray-600">({model.stage_name})</span>
                )}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                {model.age && (
                  <span>{model.age}岁</span>
                )}
                {model.height && (
                  <span>{model.height}cm</span>
                )}
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getGenderColor(model.gender)}`}>
                  {model.gender === Gender.Male ? '男' : model.gender === Gender.Female ? '女' : '其他'}
                </span>
              </div>

              {model.description && (
                <p className="text-gray-700 max-w-md">{model.description}</p>
              )}

              {/* 标签 */}
              {model.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {model.tags.slice(0, 5).map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                  {model.tags.length > 5 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                      +{model.tags.length - 5}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 发布动态按钮 */}
          <div className="lg:ml-auto">
            <button
              onClick={onCreateDynamic}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium hover:scale-105 active:scale-95"
            >
              <PlusIcon className="h-5 w-5" />
              发布
            </button>
          </div>
        </div>

        {/* 统计信息 */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200 hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <SparklesIcon className="h-4 w-4 text-primary-600" />
                <span className="text-xs font-medium text-gray-600">总动态</span>
              </div>
              <div className="text-xl font-bold text-gray-900">{stats.total_dynamics}</div>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200 hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-gray-600">已发布</span>
              </div>
              <div className="text-xl font-bold text-gray-900">{stats.published_dynamics}</div>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200 hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <VideoCameraIcon className="h-4 w-4 text-blue-600" />
                <span className="text-xs font-medium text-gray-600">总视频</span>
              </div>
              <div className="text-xl font-bold text-gray-900">{stats.total_videos}</div>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200 hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-gray-600">已完成</span>
              </div>
              <div className="text-xl font-bold text-gray-900">{stats.completed_videos}</div>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200">
              <div className="flex items-center gap-2 mb-1">
                <ClockIcon className={`h-4 w-4 text-yellow-600 ${stats.generating_videos > 0 ? 'animate-pulse' : ''}`} />
                <span className="text-xs font-medium text-gray-600">生成中</span>
              </div>
              <div className="text-xl font-bold text-gray-900">{stats.generating_videos}</div>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200 hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <ExclamationCircleIcon className="h-4 w-4 text-red-600" />
                <span className="text-xs font-medium text-gray-600">失败</span>
              </div>
              <div className="text-xl font-bold text-gray-900">{stats.failed_videos}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelDynamicHeader;
