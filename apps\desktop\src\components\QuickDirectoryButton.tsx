import React from 'react';
import { Folder } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';

interface QuickDirectoryButtonProps {
  settingType: 'material_import' | 'template_import' | 'jianying_export' | 'project_export' | 'thumbnail_export';
  label?: string;
  onDirectorySelected?: (directory: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  showLabel?: boolean;
}

const QuickDirectoryButton: React.FC<QuickDirectoryButtonProps> = ({
  settingType,
  label,
  onDirectorySelected,
  className = '',
  size = 'md',
  variant = 'secondary',
  showLabel = true,
}) => {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300',
    ghost: 'bg-transparent text-gray-600 hover:bg-gray-100',
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const getDisplayName = () => {
    if (label) return label;
    
    switch (settingType) {
      case 'material_import':
        return '素材目录';
      case 'template_import':
        return '模板目录';
      case 'jianying_export':
        return '导出目录';
      case 'project_export':
        return '项目目录';
      case 'thumbnail_export':
        return '缩略图目录';
      default:
        return '选择目录';
    }
  };

  const getTitle = () => {
    switch (settingType) {
      case 'material_import':
        return '选择素材导入目录';
      case 'template_import':
        return '选择模板导入目录';
      case 'jianying_export':
        return '选择剪影导出目录';
      case 'project_export':
        return '选择项目导出目录';
      case 'thumbnail_export':
        return '选择缩略图导出目录';
      default:
        return '选择目录';
    }
  };

  const handleClick = async () => {
    try {
      const result = await invoke<string | null>('select_and_save_directory', {
        settingType,
        title: getTitle(),
      });

      if (result && onDirectorySelected) {
        onDirectorySelected(result);
      }
    } catch (error) {
      console.error('选择目录失败:', error);
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`
        flex items-center space-x-2 rounded transition-colors
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
      title={getTitle()}
    >
      <Folder className={iconSizes[size]} />
      {showLabel && <span>{getDisplayName()}</span>}
    </button>
  );
};

export default QuickDirectoryButton;
