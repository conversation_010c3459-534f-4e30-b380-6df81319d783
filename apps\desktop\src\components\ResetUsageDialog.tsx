import React, { useState } from 'react';
import { Alert<PERSON><PERSON>gle, RefreshCw, Loader2 } from 'lucide-react';

interface ResetUsageDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  title: string;
  description: string;
  resetType: 'material' | 'project' | 'segments';
  itemCount?: number;
}

/**
 * 重置使用状态确认对话框
 * 用于确认重置素材使用状态的操作
 */
export const ResetUsageDialog: React.FC<ResetUsageDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  resetType,
  itemCount
}) => {
  const [isResetting, setIsResetting] = useState(false);

  if (!isOpen) return null;

  const handleConfirm = async () => {
    setIsResetting(true);
    try {
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('重置使用状态失败:', error);
    } finally {
      setIsResetting(false);
    }
  };

  const getResetTypeText = () => {
    switch (resetType) {
      case 'material':
        return '素材';
      case 'project':
        return '项目';
      case 'segments':
        return '片段';
      default:
        return '项目';
    }
  };

  const getWarningLevel = () => {
    switch (resetType) {
      case 'project':
        return 'high';
      case 'material':
        return 'medium';
      case 'segments':
        return 'low';
      default:
        return 'medium';
    }
  };

  const warningLevel = getWarningLevel();
  const warningColors = {
    high: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      icon: 'text-red-500',
      button: 'bg-red-600 hover:bg-red-700'
    },
    medium: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'text-orange-500',
      button: 'bg-orange-600 hover:bg-orange-700'
    },
    low: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      icon: 'text-yellow-500',
      button: 'bg-yellow-600 hover:bg-yellow-700'
    }
  };

  const colors = warningColors[warningLevel];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        {/* 标题栏 */}
        <div className="flex items-center mb-4">
          <div className={`w-10 h-10 rounded-full ${colors.bg} ${colors.border} border flex items-center justify-center mr-3`}>
            <AlertTriangle className={`w-5 h-5 ${colors.icon}`} />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>

        {/* 描述内容 */}
        <div className="mb-6">
          <p className="text-gray-600 mb-4">{description}</p>
          
          {itemCount !== undefined && (
            <div className={`p-3 rounded-lg ${colors.bg} ${colors.border} border`}>
              <p className="text-sm font-medium text-gray-800">
                将要重置 <span className="font-bold">{itemCount}</span> 个{getResetTypeText()}的使用状态
              </p>
            </div>
          )}

          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-800 mb-2">重置后的影响：</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 所有使用记录将被清除</li>
              <li>• 素材片段将重新变为可用状态</li>
              <li>• 模板匹配时可以重新选择这些素材</li>
              <li>• 此操作无法撤销</li>
            </ul>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            disabled={isResetting}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            disabled={isResetting}
            className={`px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50 flex items-center gap-2 ${colors.button}`}
          >
            {isResetting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                重置中...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4" />
                确认重置
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * 快速重置对话框的便捷函数
 */
export const createResetDialog = {
  material: (materialName: string, onConfirm: () => Promise<void>) => ({
    title: '重置素材使用状态',
    description: `确定要重置素材"${materialName}"的使用状态吗？`,
    resetType: 'material' as const,
    onConfirm
  }),

  project: (projectName: string, materialCount: number, onConfirm: () => Promise<void>) => ({
    title: '重置项目使用状态',
    description: `确定要重置项目"${projectName}"中所有素材的使用状态吗？`,
    resetType: 'project' as const,
    itemCount: materialCount,
    onConfirm
  }),

  segments: (segmentCount: number, onConfirm: () => Promise<void>) => ({
    title: '重置片段使用状态',
    description: '确定要重置选中片段的使用状态吗？',
    resetType: 'segments' as const,
    itemCount: segmentCount,
    onConfirm
  })
};

/**
 * 使用重置对话框的Hook
 */
export const useResetUsageDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [dialogProps, setDialogProps] = useState<Omit<ResetUsageDialogProps, 'isOpen' | 'onClose'> | null>(null);

  const openDialog = (props: Omit<ResetUsageDialogProps, 'isOpen' | 'onClose'>) => {
    setDialogProps(props);
    setIsOpen(true);
  };

  const closeDialog = () => {
    setIsOpen(false);
    setDialogProps(null);
  };

  const ResetDialog = dialogProps ? (
    <ResetUsageDialog
      {...dialogProps}
      isOpen={isOpen}
      onClose={closeDialog}
    />
  ) : null;

  return {
    openDialog,
    closeDialog,
    ResetDialog,
    isOpen
  };
};
