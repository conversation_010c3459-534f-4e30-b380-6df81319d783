import React from 'react';
import { MatchingStatistics } from '../types/templateMatchingResult';

interface TemplateMatchingResultStatsPanelProps {
  statistics: MatchingStatistics;
}

export const TemplateMatchingResultStatsPanel: React.FC<TemplateMatchingResultStatsPanelProps> = ({
  statistics,
}) => {
  // 计算成功率百分比
  const successPercentage = statistics.total_results > 0 
    ? (statistics.successful_results / statistics.total_results) * 100 
    : 0;

  // 计算片段匹配率
  const segmentMatchPercentage = statistics.total_segments > 0
    ? (statistics.matched_segments / statistics.total_segments) * 100
    : 0;

  // 获取成功率颜色
  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 70) return 'text-yellow-600';
    if (rate >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  // 统计卡片组件
  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    color?: string;
    icon?: string;
  }> = ({ title, value, subtitle, color = 'text-gray-900', icon }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
        {icon && (
          <div className="text-2xl opacity-60">{icon}</div>
        )}
      </div>
    </div>
  );

  return (
    <div className="template-matching-result-stats-panel">
      <div className="bg-gradient-to-br from-primary-50 via-white to-primary-50 rounded-xl shadow-sm border border-primary-100/50 p-6 mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <span className="mr-3 text-2xl">📊</span>
          <span className="text-gradient-primary">匹配结果统计</span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* 总结果数 */}
          <StatCard
            title="总匹配结果"
            value={statistics.total_results}
            subtitle="个匹配结果"
            icon="📋"
          />

          {/* 成功结果数 */}
          <StatCard
            title="成功结果"
            value={statistics.successful_results}
            subtitle={`${successPercentage.toFixed(1)}% 成功率`}
            color={getSuccessRateColor(successPercentage)}
            icon="✅"
          />

          {/* 总片段数 */}
          <StatCard
            title="总片段数"
            value={statistics.total_segments}
            subtitle="个模板片段"
            icon="🎬"
          />

          {/* 匹配片段数 */}
          <StatCard
            title="匹配片段"
            value={statistics.matched_segments}
            subtitle={`${segmentMatchPercentage.toFixed(1)}% 匹配率`}
            color={getSuccessRateColor(segmentMatchPercentage)}
            icon="🎯"
          />
        </div>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 素材使用统计 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-base font-semibold text-gray-900 flex items-center">
              <span className="mr-3 text-lg">🎥</span>
              素材使用情况
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600">使用素材总数</span>
                <span className="text-sm font-semibold text-gray-900">
                  {statistics.total_materials}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600">平均每结果</span>
                <span className="text-sm font-semibold text-gray-900">
                  {statistics.total_results > 0
                    ? (statistics.total_materials / statistics.total_results).toFixed(1)
                    : '0'
                  } 个
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 模特使用统计 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-base font-semibold text-gray-900 flex items-center">
              <span className="mr-3 text-lg">👤</span>
              模特使用情况
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600">使用模特总数</span>
                <span className="text-sm font-semibold text-gray-900">
                  {statistics.total_models}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600">平均每结果</span>
                <span className="text-sm font-semibold text-gray-900">
                  {statistics.total_results > 0
                    ? (statistics.total_models / statistics.total_results).toFixed(1)
                    : '0'
                  } 个
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 质量统计 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-base font-semibold text-gray-900 flex items-center">
              <span className="mr-3 text-lg">⭐</span>
              质量统计
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600">平均成功率</span>
                <span className={`text-sm font-semibold ${getSuccessRateColor(Math.min(statistics.average_success_rate * 100, 100))}`}>
                  {Math.min(statistics.average_success_rate * 100, 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600">失败片段</span>
                <span className="text-sm font-semibold text-gray-900">
                  {statistics.total_segments - statistics.matched_segments}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 进度条显示 */}
      <div className="card mt-6">
        <div className="card-header">
          <h3 className="text-base font-semibold text-gray-900">整体匹配进度</h3>
        </div>
        <div className="card-body">
          {/* 结果成功率进度条 */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span className="font-medium">结果成功率</span>
              <span className="font-semibold">{successPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${
                  successPercentage >= 90 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                  successPercentage >= 70 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                  successPercentage >= 50 ? 'bg-gradient-to-r from-orange-500 to-orange-600' : 'bg-gradient-to-r from-red-500 to-red-600'
                }`}
                style={{ width: `${Math.min(successPercentage, 100)}%` }}
              />
            </div>
          </div>

          {/* 片段匹配率进度条 */}
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span className="font-medium">片段匹配率</span>
              <span className="font-semibold">{segmentMatchPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${
                  segmentMatchPercentage >= 90 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                  segmentMatchPercentage >= 70 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                  segmentMatchPercentage >= 50 ? 'bg-gradient-to-r from-orange-500 to-orange-600' : 'bg-gradient-to-r from-red-500 to-red-600'
                }`}
                style={{ width: `${Math.min(segmentMatchPercentage, 100)}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
