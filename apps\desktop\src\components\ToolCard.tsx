import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON>, 
  Spark<PERSON>, 
  Clock, 
  Tag,
  ChevronRight,
  Badge
} from 'lucide-react';
import { Tool, ToolStatus, ToolCategory, ToolCardConfig } from '../types/tool';

interface ToolCardProps {
  /** 工具数据 */
  tool: Tool;
  
  /** 显示配置 */
  config?: ToolCardConfig;
  
  /** 点击回调 */
  onClick?: (tool: Tool) => void;
  
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 工具卡片组件
 * 遵循设计系统规范，提供统一的工具展示界面
 */
const ToolCard: React.FC<ToolCardProps> = ({
  tool,
  config = {},
  onClick,
  className = ''
}) => {
  const navigate = useNavigate();
  
  const {
    showStatus = true,
    showCategory = true,
    showTags = false,
    showVersion = false,
    size = 'medium',
    enableHover = true
  } = config;

  // 处理卡片点击
  const handleClick = () => {
    if (onClick) {
      onClick(tool);
    } else {
      navigate(tool.route);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: ToolStatus) => {
    switch (status) {
      case ToolStatus.STABLE:
        return 'bg-green-100 text-green-800 border-green-200';
      case ToolStatus.BETA:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case ToolStatus.EXPERIMENTAL:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case ToolStatus.DEPRECATED:
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取分类颜色
  const getCategoryColor = (category: ToolCategory) => {
    switch (category) {
      case ToolCategory.DATA_PROCESSING:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case ToolCategory.DEVELOPMENT:
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case ToolCategory.FILE_PROCESSING:
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case ToolCategory.AI_TOOLS:
        return 'bg-pink-100 text-pink-800 border-pink-200';
      case ToolCategory.UTILITIES:
        return 'bg-teal-100 text-teal-800 border-teal-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取分类显示名称
  const getCategoryName = (category: ToolCategory) => {
    switch (category) {
      case ToolCategory.DATA_PROCESSING:
        return '数据处理';
      case ToolCategory.DEVELOPMENT:
        return '开发调试';
      case ToolCategory.FILE_PROCESSING:
        return '文件处理';
      case ToolCategory.AI_TOOLS:
        return 'AI工具';
      case ToolCategory.UTILITIES:
        return '实用工具';
      default:
        return '其他';
    }
  };

  // 获取状态显示名称
  const getStatusName = (status: ToolStatus) => {
    switch (status) {
      case ToolStatus.STABLE:
        return '稳定版';
      case ToolStatus.BETA:
        return '测试版';
      case ToolStatus.EXPERIMENTAL:
        return '实验性';
      case ToolStatus.DEPRECATED:
        return '已废弃';
      default:
        return '未知';
    }
  };

  // 获取卡片尺寸样式
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'p-4';
      case 'large':
        return 'p-8';
      default:
        return 'p-6';
    }
  };

  const IconComponent = tool.icon;

  return (
    <div
      className={`
        card card-interactive group cursor-pointer animate-fade-in-up
        relative overflow-hidden bg-gradient-to-br from-white to-gray-50/50
        hover:from-white hover:to-primary-50/30 transition-all duration-500
        ${enableHover ? 'hover-glow' : ''}
        ${className}
      `}
      onClick={handleClick}
    >
      {/* 装饰性背景 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full -translate-y-16 translate-x-16 opacity-40 group-hover:opacity-60 transition-all duration-500 group-hover:scale-110"></div>

      {/* 光晕效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <div className={`relative ${getSizeClasses()}`}>
        {/* 头部区域 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {/* 工具图标 */}
            <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-300">
              <IconComponent className="w-6 h-6 text-white" />
            </div>
            
            {/* 新功能和热门标识 */}
            <div className="flex flex-col gap-1">
              {tool.isNew && (
                <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                  <Sparkles className="w-3 h-3" />
                  新功能
                </div>
              )}
              {tool.isPopular && (
                <div className="flex items-center gap-1 text-xs font-medium text-yellow-600">
                  <Star className="w-3 h-3" />
                  热门
                </div>
              )}
            </div>
          </div>

          {/* 箭头图标 */}
          <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" />
        </div>

        {/* 工具信息 */}
        <div className="space-y-3">
          {/* 标题 */}
          <h3 className="text-lg font-bold text-gray-900 group-hover:text-primary-700 transition-colors duration-300">
            {tool.name}
          </h3>

          {/* 描述 */}
          <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
            {tool.description}
          </p>

          {/* 标签区域 */}
          <div className="flex flex-wrap gap-2">
            {/* 分类标签 */}
            {showCategory && (
              <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium border ${getCategoryColor(tool.category)}`}>
                <Tag className="w-3 h-3" />
                {getCategoryName(tool.category)}
              </span>
            )}

            {/* 状态标签 */}
            {showStatus && (
              <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium border ${getStatusColor(tool.status)}`}>
                <Badge className="w-3 h-3" />
                {getStatusName(tool.status)}
              </span>
            )}

            {/* 版本信息 */}
            {showVersion && tool.version && (
              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200">
                v{tool.version}
              </span>
            )}
          </div>

          {/* 自定义标签 */}
          {showTags && tool.tags && tool.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {tool.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md"
                >
                  {tag}
                </span>
              ))}
              {tool.tags.length > 3 && (
                <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md">
                  +{tool.tags.length - 3}
                </span>
              )}
            </div>
          )}

          {/* 最后更新时间 */}
          {tool.lastUpdated && (
            <div className="flex items-center gap-1 text-xs text-gray-500 pt-2 border-t border-gray-100">
              <Clock className="w-3 h-3" />
              最后更新: {tool.lastUpdated}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ToolCard;
