import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, test, expect, vi } from 'vitest';
import { EnhancedChatMessageV2 } from '../EnhancedChatMessageV2';

// Mock 数据
const mockUserMessage = {
  id: '1',
  type: 'user' as const,
  content: '请推荐一些夏日穿搭',
  timestamp: new Date('2024-01-01T10:00:00Z'),
  status: 'sent' as const
};

const mockAssistantMessage = {
  id: '2',
  type: 'assistant' as const,
  content: '夏日穿搭建议：选择轻薄透气的面料，如棉麻材质。推荐搭配清爽的色彩，如白色、浅蓝色等。',
  timestamp: new Date('2024-01-01T10:01:00Z'),
  status: 'sent' as const,
  metadata: {
    responseTime: 1500,
    modelUsed: 'gemini-pro',
    sources: [
      {
        title: '夏日穿搭指南',
        uri: 'https://example.com/summer-style.jpg',
        content: {
          type: 'image',
          image_url: 'https://example.com/summer-style.jpg',
          description: '清爽夏日穿搭示例'
        }
      }
    ],
    grounding_metadata: {
      grounding_supports: [
        {
          start_index: 0,
          end_index: 20,
          grounding_chunk_indices: [0]
        }
      ],
      sources: [
        {
          chunk_index: 0,
          title: '夏日穿搭指南',
          uri: 'https://example.com/summer-style.jpg',
          content: {
            type: 'image',
            image_url: 'https://example.com/summer-style.jpg',
            description: '清爽夏日穿搭示例'
          }
        }
      ]
    }
  }
};

describe('EnhancedChatMessageV2', () => {
  test('渲染用户消息', () => {
    render(<EnhancedChatMessageV2 message={mockUserMessage} />);
    
    expect(screen.getByText('请推荐一些夏日穿搭')).toBeInTheDocument();
    expect(screen.getByText('10:00')).toBeInTheDocument();
  });

  test('渲染助手消息', () => {
    render(<EnhancedChatMessageV2 message={mockAssistantMessage} />);
    
    expect(screen.getByText(/夏日穿搭建议/)).toBeInTheDocument();
    expect(screen.getByText('10:01')).toBeInTheDocument();
    expect(screen.getByText('• 响应时间: 1500ms')).toBeInTheDocument();
    expect(screen.getByText('• gemini-pro')).toBeInTheDocument();
  });

  test('显示素材来源', () => {
    render(
      <EnhancedChatMessageV2 
        message={mockAssistantMessage} 
        showSources={true}
        enableMaterialCards={true}
      />
    );
    
    expect(screen.getByText('相关素材 (1)')).toBeInTheDocument();
  });

  test('复制消息功能', async () => {
    // Mock clipboard API
    const mockWriteText = vi.fn().mockImplementation(() => Promise.resolve());
    Object.assign(navigator, {
      clipboard: {
        writeText: mockWriteText,
      },
    });

    render(<EnhancedChatMessageV2 message={mockUserMessage} />);

    const copyButton = screen.getByTitle('复制消息');
    fireEvent.click(copyButton);

    expect(mockWriteText).toHaveBeenCalledWith('请推荐一些夏日穿搭');
  });

  test('展开/收起素材来源', () => {
    render(
      <EnhancedChatMessageV2
        message={mockAssistantMessage}
        showSources={true}
        enableMaterialCards={true}
      />
    );

    // 检查是否显示了素材来源标题
    expect(screen.getByText('相关素材 (1)')).toBeInTheDocument();
  });

  test('角标引用功能', () => {
    render(
      <EnhancedChatMessageV2
        message={mockAssistantMessage}
        enableReferences={true}
      />
    );

    // 检查是否渲染了消息内容
    expect(screen.getByText(/夏日穿搭建议/)).toBeInTheDocument();
  });

  test('发送中状态显示', () => {
    const sendingMessage = {
      ...mockUserMessage,
      status: 'sending' as const
    };

    render(<EnhancedChatMessageV2 message={sendingMessage} />);
    
    expect(screen.getByText('发送中...')).toBeInTheDocument();
  });
});
