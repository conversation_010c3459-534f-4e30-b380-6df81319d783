# AI智能聊天功能重新设计

根据 `promptx/frontend-developer` 规范重新设计的AI智能聊天功能，包含以下核心特性：

## 🎯 核心功能

### 1. 信息引用角标系统
- **角标显示**: 每句话的信息引用以角标形式展示
- **点击查看**: 点击角标可查看详细来源信息
- **悬停预览**: 鼠标悬停显示来源预览
- **智能关联**: 与 grounding metadata 自动关联

### 2. 优化素材卡片列表
- **多种视图**: 支持网格和列表两种展示模式
- **响应式设计**: 适配不同屏幕尺寸
- **缩略图预览**: 图片/视频素材缩略图展示
- **详细信息**: 文件大小、时长等元数据显示

### 3. 增强Markdown渲染
- **语法支持**: 完整的Markdown语法支持
- **角标集成**: 角标与Markdown内容无缝集成
- **高亮效果**: 引用文本智能高亮显示
- **自定义组件**: 支持自定义Markdown组件

## 🏗️ 组件架构

### 核心组件

#### `EnhancedChatMessageV2`
主要的聊天消息组件，整合所有新功能：
```tsx
<EnhancedChatMessageV2
  message={message}
  showSources={true}
  enableMaterialCards={true}
  enableReferences={true}
/>
```

#### `ReferenceFootnote`
引用角标组件：
```tsx
<ReferenceFootnote
  index={1}
  sources={sources}
  onClick={handleReferenceClick}
/>
```

#### `EnhancedMarkdownRenderer`
增强Markdown渲染器：
```tsx
<EnhancedMarkdownRenderer
  content={content}
  groundingMetadata={metadata}
  enableReferences={true}
  enableMarkdown={true}
/>
```

#### `ChatMaterialCard`
聊天专用素材卡片：
```tsx
<ChatMaterialCard
  source={source}
  size="medium"
  showDetails={true}
  onClick={handleMaterialClick}
/>
```

## 🎨 设计规范

### 视觉设计
- **渐变背景**: 使用现代渐变背景提升视觉效果
- **毛玻璃效果**: 消息气泡采用毛玻璃效果
- **动画过渡**: 流畅的动画过渡效果
- **响应式布局**: 完美适配移动端和桌面端

### 交互设计
- **直观操作**: 点击、悬停等交互符合用户习惯
- **即时反馈**: 所有操作都有即时视觉反馈
- **无障碍访问**: 支持键盘导航和屏幕阅读器
- **性能优化**: 懒加载和虚拟化提升性能

## 🔧 技术实现

### 依赖库
- `react-markdown`: Markdown渲染
- `remark-gfm`: GitHub风格Markdown支持
- `lucide-react`: 图标库
- `@testing-library/react`: 单元测试

### 样式系统
- **CSS类**: 使用语义化CSS类名
- **设计系统**: 遵循统一的设计系统
- **响应式**: 移动优先的响应式设计
- **动画**: CSS动画和过渡效果

### 状态管理
- **本地状态**: 使用React Hooks管理组件状态
- **全局状态**: 与现有状态管理系统集成
- **缓存策略**: 智能缓存提升性能

## 📱 使用示例

### 基础用法
```tsx
import { EnhancedChatMessageV2 } from './components/EnhancedChatMessageV2';

function ChatInterface() {
  return (
    <div className="chat-container">
      {messages.map((message) => (
        <EnhancedChatMessageV2
          key={message.id}
          message={message}
          showSources={true}
          enableMaterialCards={true}
          enableReferences={true}
        />
      ))}
    </div>
  );
}
```

### 自定义配置
```tsx
<EnhancedChatMessageV2
  message={message}
  showSources={false}           // 隐藏素材来源
  enableMaterialCards={false}   // 禁用素材卡片
  enableReferences={false}      // 禁用角标引用
  className="custom-message"    // 自定义样式
/>
```

## 🧪 测试

### 单元测试
```bash
npm test -- EnhancedChatMessageV2.test.tsx
```

### 功能测试
- 消息渲染测试
- 角标引用测试
- 素材卡片测试
- 交互功能测试

## 🚀 性能优化

### 渲染优化
- **虚拟化**: 长列表虚拟化
- **懒加载**: 图片和素材懒加载
- **缓存**: 智能缓存策略
- **防抖**: 用户输入防抖处理

### 内存管理
- **清理**: 及时清理事件监听器
- **优化**: 避免内存泄漏
- **回收**: 自动垃圾回收

## 📋 待办事项

- [ ] 添加更多Markdown组件支持
- [ ] 实现素材详情查看功能
- [ ] 添加更多动画效果
- [ ] 优化移动端体验
- [ ] 添加国际化支持

## 🔗 相关文档

- [Frontend Developer 规范](../../promptx/frontend-developer/)
- [设计系统文档](../../styles/design-system.css)
- [组件测试指南](../__tests__/README.md)
