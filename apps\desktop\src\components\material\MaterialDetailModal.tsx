import React, { useState, useCallback } from 'react';
import {
  X,
  Star,
  Tag,
  Palette,
  ExternalLink,
  Download,
  Heart,
  Share2,
  Calendar,
  Package,
  Sparkles,
} from 'lucide-react';

interface MaterialDetailModalProps {
  material: any;
  isVisible: boolean;
  onClose: () => void;
  className?: string;
}

/**
 * 素材详情模态框组件
 * 遵循设计系统规范，提供完整的素材信息展示
 */
export const MaterialDetailModal: React.FC<MaterialDetailModalProps> = ({
  material,
  isVisible,
  onClose,
  className = '',
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);

  // 处理图片加载
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageLoaded(false);
    setImageError(true);
  }, []);

  // 处理收藏
  const handleToggleFavorite = useCallback(() => {
    setIsFavorited(!isFavorited);
    // 这里可以添加实际的收藏逻辑
  }, [isFavorited]);

  // 处理分享
  const handleShare = useCallback(() => {
    if (navigator.share && material.image_url) {
      navigator.share({
        title: material.style_description || '时尚素材',
        url: material.image_url,
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(material.image_url || '');
      alert('链接已复制到剪贴板');
    }
  }, [material]);

  // 处理下载
  const handleDownload = useCallback(() => {
    if (material.image_url) {
      const link = document.createElement('a');
      link.href = material.image_url;
      link.download = `material-${material.id || Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [material]);

  // 处理在新窗口打开
  const handleOpenInNewWindow = useCallback(() => {
    if (material.image_url) {
      window.open(material.image_url, '_blank');
    }
  }, [material]);

  if (!isVisible) return null;

  const primaryProduct = material.products?.[0];

  return (
    <div className={`fixed inset-0 z-50 bg-black/50 backdrop-blur-sm animate-fade-in ${className}`}>
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-fade-in-up">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-primary-50 to-blue-50">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                <Package className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-high-emphasis">
                  素材详情
                </h2>
                <p className="text-sm text-medium-emphasis">
                  {material.style_description || '时尚素材详细信息'}
                </p>
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              aria-label="关闭"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* 主要内容 */}
          <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
              {/* 左侧：图片展示 */}
              <div className="space-y-4">
                <div className="relative aspect-square rounded-xl overflow-hidden bg-gray-100 shadow-lg">
                  {!imageError ? (
                    <img
                      src={material.image_url}
                      alt={material.style_description}
                      className={`w-full h-full object-cover transition-all duration-300 ${
                        imageLoaded ? 'opacity-100' : 'opacity-0'
                      }`}
                      onLoad={handleImageLoad}
                      onError={handleImageError}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-100">
                      <Package className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                  
                  {/* 加载状态 */}
                  {!imageLoaded && !imageError && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                      <div className="w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}

                  {/* 评分标识 */}
                  <div className="absolute top-4 left-4 flex items-center gap-1 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm font-medium shadow-sm">
                    <Star className="w-4 h-4 fill-current" />
                    {material.relevance_score ? (material.relevance_score * 100).toFixed(1) : 'N/A'}%
                  </div>

                  {/* AI推荐标识 */}
                  <div className="absolute top-4 right-4 flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-medium shadow-sm">
                    <Sparkles className="w-4 h-4" />
                    AI推荐
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleToggleFavorite}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors duration-200 ${
                      isFavorited 
                        ? 'bg-red-50 text-red-600 hover:bg-red-100' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
                    {isFavorited ? '已收藏' : '收藏'}
                  </button>
                  
                  <button
                    onClick={handleShare}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  >
                    <Share2 className="w-4 h-4" />
                    分享
                  </button>
                  
                  <button
                    onClick={handleDownload}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors duration-200"
                  >
                    <Download className="w-4 h-4" />
                    下载
                  </button>
                  
                  <button
                    onClick={handleOpenInNewWindow}
                    className="flex items-center gap-2 px-4 py-2 bg-primary-500 text-white hover:bg-primary-600 rounded-lg transition-colors duration-200"
                  >
                    <ExternalLink className="w-4 h-4" />
                    新窗口打开
                  </button>
                </div>
              </div>

              {/* 右侧：详细信息 */}
              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="card p-4">
                  <h3 className="text-lg font-semibold text-high-emphasis mb-3 flex items-center gap-2">
                    <Tag className="w-5 h-5 text-primary-500" />
                    基本信息
                  </h3>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">风格描述</label>
                      <p className="text-gray-900 mt-1">{material.style_description || '暂无描述'}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-700">相关性评分</label>
                      <p className="text-gray-900 mt-1">
                        {material.relevance_score ? `${(material.relevance_score * 100).toFixed(1)}%` : '暂无评分'}
                      </p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-700">创建时间</label>
                      <p className="text-gray-900 mt-1 flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        {new Date(material.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 环境标签 */}
                {material.environment_tags && material.environment_tags.length > 0 && (
                  <div className="card p-4">
                    <h3 className="text-lg font-semibold text-high-emphasis mb-3">环境标签</h3>
                    <div className="flex flex-wrap gap-2">
                      {material.environment_tags.map((tag: string, index: number) => (
                        <div
                          key={index}
                          className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                        >
                          {tag}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 产品信息 */}
                {primaryProduct && (
                  <div className="card p-4">
                    <h3 className="text-lg font-semibold text-high-emphasis mb-3 flex items-center gap-2">
                      <Palette className="w-5 h-5 text-primary-500" />
                      产品信息
                    </h3>
                    
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">类别</label>
                        <p className="text-gray-900 mt-1">{primaryProduct.category}</p>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium text-gray-700">描述</label>
                        <p className="text-gray-900 mt-1">{primaryProduct.description}</p>
                      </div>
                      
                      {primaryProduct.color_pattern && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">主要颜色</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div
                              className="w-6 h-6 rounded-full border border-gray-300 shadow-sm"
                              style={{
                                backgroundColor: `hsl(${primaryProduct.color_pattern.hue * 360}, ${primaryProduct.color_pattern.saturation * 100}%, ${primaryProduct.color_pattern.value * 100}%)`
                              }}
                            />
                            <span className="text-gray-900">
                              HSV({Math.round(primaryProduct.color_pattern.hue * 360)}, {Math.round(primaryProduct.color_pattern.saturation * 100)}%, {Math.round(primaryProduct.color_pattern.value * 100)}%)
                            </span>
                          </div>
                        </div>
                      )}
                      
                      {primaryProduct.design_styles && primaryProduct.design_styles.length > 0 && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">设计风格</label>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {primaryProduct.design_styles.map((style: string, index: number) => (
                              <div
                                key={index}
                                className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-sm"
                              >
                                {style}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 其他产品 */}
                {material.products && material.products.length > 1 && (
                  <div className="card p-4">
                    <h3 className="text-lg font-semibold text-high-emphasis mb-3">
                      其他产品 ({material.products.length - 1} 个)
                    </h3>
                    <div className="space-y-2">
                      {material.products.slice(1).map((product: any, index: number) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                          <div className="text-sm font-medium text-gray-700">{product.category}</div>
                          <div className="text-sm text-gray-600">{product.description}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaterialDetailModal;
