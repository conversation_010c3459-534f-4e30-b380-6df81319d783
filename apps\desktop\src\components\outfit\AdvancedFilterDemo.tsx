import React, { useState } from 'react';
import { SearchConfig, DEFAULT_SEARCH_CONFIG } from '../../types/outfitSearch';
import { AdvancedFilterPanel } from './AdvancedFilterPanel';
import { Setting<PERSON>, Eye } from 'lucide-react';

/**
 * 高级过滤器演示组件
 * 用于展示和测试新的高级过滤器功能
 */

export const AdvancedFilterDemo: React.FC = () => {
  const [config, setConfig] = useState<SearchConfig>(DEFAULT_SEARCH_CONFIG);
  const [showPanel, setShowPanel] = useState(true);
  const [showConfigJson, setShowConfigJson] = useState(false);

  const handleConfigChange = (newConfig: SearchConfig) => {
    setConfig(newConfig);
  };

  const handleTogglePanel = () => {
    setShowPanel(!showPanel);
  };

  // 计算活跃过滤器数量
  const getActiveFiltersCount = () => {
    let count = 0;
    count += config.categories.length;
    count += config.environments.length;
    count += Object.values(config.color_filters).filter(f => f.enabled).length;
    count += Object.values(config.design_styles).reduce((acc, styles) => acc + styles.length, 0);
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">高级过滤器演示</h1>
        <p className="text-gray-600">
          展示新的高级过滤器组件功能，包括类别、环境、设计风格和颜色检测
        </p>
      </div>

      {/* 控制面板 */}
      <div className="card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleTogglePanel}
              className="btn btn-primary"
            >
              <Settings className="w-4 h-4 mr-2" />
              {showPanel ? '隐藏' : '显示'} 高级过滤器
            </button>
            
            <button
              onClick={() => setShowConfigJson(!showConfigJson)}
              className="btn btn-secondary"
            >
              <Eye className="w-4 h-4 mr-2" />
              {showConfigJson ? '隐藏' : '显示'} 配置JSON
            </button>
          </div>

          <div className="text-sm text-gray-600">
            活跃过滤器: <span className="font-semibold text-primary-600">{activeFiltersCount}</span>
          </div>
        </div>
      </div>

      {/* 高级过滤器面板 */}
      <AdvancedFilterPanel
        config={config}
        onConfigChange={handleConfigChange}
        isVisible={showPanel}
        onToggle={handleTogglePanel}
      />

      {/* 配置JSON显示 */}
      {showConfigJson && (
        <div className="card p-4">
          <h3 className="text-lg font-semibold mb-3">当前配置</h3>
          <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>
      )}

      {/* 过滤器摘要 */}
      <div className="card p-4">
        <h3 className="text-lg font-semibold mb-3">过滤器摘要</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 类别摘要 */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-700">类别过滤</h4>
            {config.categories.length > 0 ? (
              <div className="space-y-1">
                {config.categories.map((category) => (
                  <div key={category} className="text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded">
                    {category}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">未设置</p>
            )}
          </div>

          {/* 环境摘要 */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-700">环境标签</h4>
            {config.environments.length > 0 ? (
              <div className="space-y-1">
                {config.environments.map((env) => (
                  <div key={env} className="text-sm bg-green-100 text-green-700 px-2 py-1 rounded">
                    {env}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">未设置</p>
            )}
          </div>

          {/* 设计风格摘要 */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-700">设计风格</h4>
            {Object.keys(config.design_styles).length > 0 ? (
              <div className="space-y-1">
                {Object.entries(config.design_styles).map(([category, styles]) => (
                  <div key={category} className="text-sm">
                    <div className="font-medium text-gray-600">{category}:</div>
                    {styles.map((style) => (
                      <div key={style} className="bg-purple-100 text-purple-700 px-2 py-1 rounded ml-2 mt-1">
                        {style}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">未设置</p>
            )}
          </div>

          {/* 颜色过滤摘要 */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-700">颜色检测</h4>
            {Object.values(config.color_filters).some(f => f.enabled) ? (
              <div className="space-y-1">
                {Object.entries(config.color_filters)
                  .filter(([, filter]) => filter.enabled)
                  .map(([category, filter]) => (
                    <div key={category} className="text-sm">
                      <div className="font-medium text-gray-600">{category}:</div>
                      <div className="flex items-center gap-2 mt-1">
                        <div
                          className="w-4 h-4 rounded border"
                          style={{
                            backgroundColor: `hsl(${filter.color.hue * 360}, ${filter.color.saturation * 100}%, ${filter.color.value * 100}%)`
                          }}
                        />
                        <span className="text-xs text-gray-500">
                          H:{(filter.hue_threshold * 100).toFixed(1)}% 
                          S:{(filter.saturation_threshold * 100).toFixed(1)}% 
                          V:{(filter.value_threshold * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">未设置</p>
            )}
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="card p-4 bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold mb-3 text-blue-800">使用说明</h3>
        <div className="space-y-2 text-sm text-blue-700">
          <p>• <strong>类别过滤</strong>：选择要搜索的服装类别，如上装、下装等</p>
          <p>• <strong>环境标签</strong>：选择适用的环境场景，如室内、室外等</p>
          <p>• <strong>设计风格</strong>：为每个类别设置特定的设计风格偏好</p>
          <p>• <strong>颜色检测</strong>：为每个类别设置颜色过滤条件和阈值</p>
          <p>• 所有设置都会实时更新到搜索配置中，可以在上方的JSON中查看</p>
        </div>
      </div>
    </div>
  );
};
