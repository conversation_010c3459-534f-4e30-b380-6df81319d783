import React, { useCallback } from 'react';
import { X, Sparkles } from 'lucide-react';
import { OutfitRecommendation } from '../../types/outfitRecommendation';
import OutfitRecommendationList from './OutfitRecommendationList';

interface OutfitRecommendationModalProps {
  /** 是否显示模态框 */
  isOpen: boolean;
  /** 关闭模态框回调 */
  onClose: () => void;
  /** 穿搭方案列表 */
  recommendations: OutfitRecommendation[];
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 错误信息 */
  error?: string;
  /** 方案选择回调 */
  onRecommendationSelect?: (recommendation: OutfitRecommendation) => void;
  /** 场景检索回调 */
  onSceneSearch?: (recommendation: OutfitRecommendation) => void;
  /** 重新生成回调 */
  onRegenerate?: () => void;
  /** 生成查询 */
  query?: string;
}

/**
 * 穿搭方案推荐模态框组件
 * 遵循设计系统规范，提供统一的模态框展示界面
 */
export const OutfitRecommendationModal: React.FC<OutfitRecommendationModalProps> = ({
  isOpen,
  onClose,
  recommendations,
  isLoading = false,
  error,
  onRecommendationSelect,
  onSceneSearch,
  onRegenerate,
  query = '',
}) => {
  // 处理背景点击关闭
  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  // 处理ESC键关闭
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm animate-fade-in"
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div className="relative w-full max-w-7xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl animate-scale-in overflow-hidden">
        {/* 模态框头部 */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="icon-container primary w-10 h-10">
                <Sparkles className="w-5 h-5" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-high-emphasis">
                  AI穿搭方案推荐
                </h2>
                <p className="text-sm text-medium-emphasis">
                  {query ? `基于"${query}"的个性化穿搭推荐` : '个性化穿搭推荐'}
                </p>
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              aria-label="关闭"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* 模态框内容 */}
        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          <div className="p-6">
            <OutfitRecommendationList
              recommendations={recommendations}
              isLoading={isLoading}
              error={error}
              onRecommendationSelect={onRecommendationSelect}
              onSceneSearch={onSceneSearch}
              onRegenerate={onRegenerate}
            />
          </div>
        </div>

        {/* 模态框底部 (可选) */}
        {!isLoading && !error && recommendations.length > 0 && (
          <div className="sticky bottom-0 bg-gradient-to-t from-white to-transparent px-6 py-4 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <div className="text-sm text-medium-emphasis">
                共 {recommendations.length} 个推荐方案
              </div>
              
              <div className="flex items-center gap-3">
                {onRegenerate && (
                  <button
                    onClick={onRegenerate}
                    className="btn btn-outline btn-sm"
                  >
                    重新生成
                  </button>
                )}
                
                <button
                  onClick={onClose}
                  className="btn btn-primary btn-sm"
                >
                  完成
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OutfitRecommendationModal;
