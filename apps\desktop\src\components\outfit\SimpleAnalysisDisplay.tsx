import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

// 分析结果类型定义
interface ColorPattern {
  hue: number;
  saturation: number;
  value: number;
}

interface Product {
  category: string;
  description: string;
  color_pattern: ColorPattern;
  design_styles: string[];
  color_pattern_match_dress: number;
  color_pattern_match_environment: number;
}

interface AnalysisResult {
  environment_tags: string[];
  environment_color_pattern: ColorPattern;
  dress_color_pattern: ColorPattern;
  style_description: string;
  products: Product[];
}

interface SimpleAnalysisDisplayProps {
  analysisResult: AnalysisResult;
}

/**
 * 简洁的分析结果展示组件
 * 美观地展示AI图像分析结果
 */
export const SimpleAnalysisDisplay: React.FC<SimpleAnalysisDisplayProps> = ({
  analysisResult
}) => {
  // HSV转十六进制颜色
  const hsvToHex = (color: ColorPattern): string => {
    const { hue, saturation, value } = color;
    
    const c = value * saturation;
    const x = c * (1 - Math.abs(((hue * 6) % 2) - 1));
    const m = value - c;
    
    let r = 0, g = 0, b = 0;
    
    if (hue >= 0 && hue < 1/6) {
      r = c; g = x; b = 0;
    } else if (hue >= 1/6 && hue < 2/6) {
      r = x; g = c; b = 0;
    } else if (hue >= 2/6 && hue < 3/6) {
      r = 0; g = c; b = x;
    } else if (hue >= 3/6 && hue < 4/6) {
      r = 0; g = x; b = c;
    } else if (hue >= 4/6 && hue < 5/6) {
      r = x; g = 0; b = c;
    } else {
      r = c; g = 0; b = x;
    }
    
    const red = Math.round((r + m) * 255);
    const green = Math.round((g + m) * 255);
    const blue = Math.round((b + m) * 255);
    
    return `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
  };

  // 格式化百分比
  const formatPercentage = (value: number) => `${Math.round(value * 100)}%`;

  // 获取类别图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case '上装': return '👕';
      case '下装': return '👖';
      case '鞋子': return '👟';
      case '配饰': return '💍';
      default: return '👔';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg space-y-8">
      {/* 标题 */}
      <div className="text-center border-b pb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          🎨 AI 服装分析结果
        </h2>
        <p className="text-gray-600">智能图像分析与风格解读</p>
      </div>

      {/* 整体风格描述 */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
        <div className="flex items-center mb-4">
          <Eye className="w-5 h-5 text-blue-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">整体风格</h3>
        </div>
        <p className="text-gray-700 leading-relaxed">{analysisResult.style_description}</p>
      </div>

      {/* 环境与色彩分析 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 环境标签 */}
        <div className="bg-green-50 p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <MapPin className="w-5 h-5 text-green-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">拍摄环境</h3>
          </div>
          <div className="flex flex-wrap gap-2">
            {analysisResult.environment_tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>

        {/* 色彩分析 */}
        <div className="bg-purple-50 p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <Palette className="w-5 h-5 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">主色调</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div
                className="w-8 h-8 rounded-full border-2 border-gray-200"
                style={{ backgroundColor: hsvToHex(analysisResult.dress_color_pattern) }}
              />
              <div>
                <p className="text-sm font-medium text-gray-900">服装主色</p>
                <p className="text-xs text-gray-600">{hsvToHex(analysisResult.dress_color_pattern)}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div
                className="w-8 h-8 rounded-full border-2 border-gray-200"
                style={{ backgroundColor: hsvToHex(analysisResult.environment_color_pattern) }}
              />
              <div>
                <p className="text-sm font-medium text-gray-900">环境主色</p>
                <p className="text-xs text-gray-600">{hsvToHex(analysisResult.environment_color_pattern)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 服装单品分析 */}
      <div>
        <div className="flex items-center mb-6">
          <Shirt className="w-5 h-5 text-gray-700 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">服装单品分析</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {analysisResult.products.map((product, index) => (
            <div key={index} className="bg-gray-50 p-6 rounded-lg border border-gray-200">
              {/* 单品标题 */}
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">{getCategoryIcon(product.category)}</span>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">{product.category}</h4>
                  <div
                    className="w-6 h-6 rounded-full border-2 border-gray-300 mt-1"
                    style={{ backgroundColor: hsvToHex(product.color_pattern) }}
                  />
                </div>
              </div>

              {/* 描述 */}
              <p className="text-gray-700 mb-4 leading-relaxed">{product.description}</p>

              {/* 风格标签 */}
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-600 mb-2">设计风格</p>
                <div className="flex flex-wrap gap-2">
                  {product.design_styles.map((style, styleIndex) => (
                    <span
                      key={styleIndex}
                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium"
                    >
                      {style}
                    </span>
                  ))}
                </div>
              </div>

              {/* 匹配度 */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">与整体搭配匹配度</span>
                  <span className="text-sm font-semibold text-green-600">
                    {formatPercentage(product.color_pattern_match_dress)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${product.color_pattern_match_dress * 100}%` }}
                  />
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">与环境匹配度</span>
                  <span className="text-sm font-semibold text-blue-600">
                    {formatPercentage(product.color_pattern_match_environment)}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${product.color_pattern_match_environment * 100}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
