import React, { useState } from 'react';
import { MoreVertical, Eye, Trash2, Calendar, FileText, Image, Video, Music } from 'lucide-react';
import { Template, ImportStatus, TemplateMaterialType } from '../../types/template';

interface TemplateCardProps {
  template: Template;
  onView: () => void;
  onDelete: () => void;
  getStatusIcon: (status: ImportStatus) => React.ReactNode;
  getStatusText: (status: ImportStatus) => string;
}

export const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onView,
  onDelete,
  getStatusIcon,
  getStatusText,
}) => {
  const [showMenu, setShowMenu] = useState(false);

  // 格式化时长
  const formatDuration = (microseconds: number) => {
    const seconds = Math.floor(microseconds / 1000000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // 获取素材统计
  const getMaterialStats = () => {
    const stats = {
      video: 0,
      audio: 0,
      image: 0,
      text: 0,
      other: 0,
    };

    template.materials.forEach((material) => {
      switch (material.material_type) {
        case TemplateMaterialType.Video:
          stats.video++;
          break;
        case TemplateMaterialType.Audio:
          stats.audio++;
          break;
        case TemplateMaterialType.Image:
          stats.image++;
          break;
        case TemplateMaterialType.Text:
          stats.text++;
          break;
        default:
          stats.other++;
          break;
      }
    });

    return stats;
  };

  const materialStats = getMaterialStats();

  return (
    <div className="group bg-gradient-to-br from-white to-gray-50/30 rounded-xl shadow-sm border border-gray-200/50 hover:shadow-lg hover:border-indigo-200 hover:-translate-y-1 transition-all duration-300 overflow-hidden relative">
      {/* 装饰性背景 */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-100/20 to-purple-100/20 rounded-full -translate-y-10 translate-x-10 opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

      {/* 美观的卡片头部 */}
      <div className="p-5 border-b border-gray-100/50 relative z-10">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 truncate" title={template.name}>
                {template.name}
              </h3>
            </div>
            {template.description && (
              <p className="text-sm text-gray-600 mt-1 line-clamp-2 leading-relaxed" title={template.description}>
                {template.description}
              </p>
            )}
          </div>
          
          <div className="relative ml-3 flex-shrink-0">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200 opacity-0 group-hover:opacity-100"
            >
              <MoreVertical className="w-4 h-4 text-gray-500" />
            </button>

            {showMenu && (
              <div className="absolute right-0 top-10 w-36 bg-white border border-gray-200/50 rounded-xl shadow-lg z-20 overflow-hidden">
                <button
                  onClick={() => {
                    onView();
                    setShowMenu(false);
                  }}
                  className="w-full px-4 py-3 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 transition-colors"
                >
                  <Eye className="w-4 h-4" />
                  查看详情
                </button>
                <div className="border-t border-gray-100"></div>
                <button
                  onClick={() => {
                    onDelete();
                    setShowMenu(false);
                  }}
                  className="w-full px-4 py-3 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  删除
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 状态指示器 */}
        <div className="flex items-center mt-3">
          <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 rounded-lg">
            {getStatusIcon(template.import_status)}
            <span className="text-sm font-medium text-gray-700">
              {getStatusText(template.import_status)}
            </span>
          </div>
        </div>
      </div>

      {/* 美观的卡片内容 */}
      <div className="p-5 relative z-10">
        {/* 基本信息 */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg">
            <div className="text-lg font-bold text-gray-900">
              {template.canvas_config.width}×{template.canvas_config.height}
            </div>
            <div className="text-xs text-gray-500 font-medium">分辨率</div>
          </div>
          <div className="text-center p-3 bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg">
            <div className="text-lg font-bold text-primary-700">
              {formatDuration(template.duration)}
            </div>
            <div className="text-xs text-primary-600 font-medium">时长</div>
          </div>
        </div>

        {/* 美观的素材统计 */}
        <div className="mb-4">
          <div className="text-sm font-medium text-gray-700 mb-3">素材统计</div>
          <div className="flex items-center justify-center gap-3 text-xs">
            {materialStats.video > 0 && (
              <div className="flex items-center gap-1 px-2.5 py-1.5 bg-blue-50 text-blue-700 rounded-lg border border-blue-200">
                <Video className="w-3 h-3" />
                <span className="font-medium">{materialStats.video}</span>
              </div>
            )}
            {materialStats.audio > 0 && (
              <div className="flex items-center gap-1 px-2.5 py-1.5 bg-green-50 text-green-700 rounded-lg border border-green-200">
                <Music className="w-3 h-3" />
                <span className="font-medium">{materialStats.audio}</span>
              </div>
            )}
            {materialStats.image > 0 && (
              <div className="flex items-center gap-1 px-2.5 py-1.5 bg-purple-50 text-purple-700 rounded-lg border border-purple-200">
                <Image className="w-3 h-3" />
                <span className="font-medium">{materialStats.image}</span>
              </div>
            )}
            {materialStats.text > 0 && (
              <div className="flex items-center text-orange-600">
                <FileText className="w-3 h-3 mr-1" />
                {materialStats.text}
              </div>
            )}
          </div>
        </div>

        {/* 轨道信息 */}
        <div className="mb-4">
          <div className="text-xs text-gray-500 mb-1">轨道数量</div>
          <div className="text-sm font-medium text-gray-900">
            {template.tracks.length} 个轨道
          </div>
        </div>

        {/* 创建时间 */}
        <div className="flex items-center text-xs text-gray-500">
          <Calendar className="w-3 h-3 mr-1" />
          {formatDate(template.created_at)}
        </div>
      </div>

      {/* 卡片底部操作 */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 rounded-b-lg">
        <button
          onClick={onView}
          className="w-full px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
        >
          查看详情
        </button>
      </div>

      {/* 点击外部关闭菜单 */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
};
