import React from 'react';
import { X, Download, ZoomIn, ZoomOut } from 'lucide-react';

interface ThumbnailPreviewProps {
  imageUrl: string;
  onClose: () => void;
}

/**
 * 缩略图预览组件
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
export const ThumbnailPreview: React.FC<ThumbnailPreviewProps> = ({
  imageUrl,
  onClose,
}) => {
  const [zoom, setZoom] = React.useState(1);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `thumbnail_${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.5));
  };

  const resetZoom = () => {
    setZoom(1);
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">缩略图预览</h3>
        <div className="flex items-center gap-2">
          <button
            onClick={handleZoomOut}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="缩小"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          <button
            onClick={resetZoom}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            title="重置缩放"
          >
            {Math.round(zoom * 100)}%
          </button>
          <button
            onClick={handleZoomIn}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="放大"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          <button
            onClick={handleDownload}
            className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
            title="下载"
          >
            <Download className="w-4 h-4" />
          </button>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="关闭"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="relative overflow-auto max-h-96 bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-center">
          <img
            src={imageUrl}
            alt="缩略图预览"
            className="max-w-full h-auto rounded shadow-lg transition-transform duration-200"
            style={{ transform: `scale(${zoom})` }}
          />
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500 text-center">
        点击工具栏按钮可以缩放、下载或关闭预览
      </div>
    </div>
  );
};
