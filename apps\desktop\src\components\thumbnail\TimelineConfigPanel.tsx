import React from 'react';
import { Grid, Palette } from 'lucide-react';
import { TimelineConfig, TimelineLayout } from '../../types/thumbnail';
import { CustomSelect } from '../CustomSelect';

interface TimelineConfigPanelProps {
  config: TimelineConfig;
  onChange: (config: TimelineConfig) => void;
}

/**
 * 时间轴配置面板组件
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
export const TimelineConfigPanel: React.FC<TimelineConfigPanelProps> = ({
  config,
  onChange,
}) => {
  // 更新配置的辅助函数
  const updateConfig = (updates: Partial<TimelineConfig>) => {
    onChange({ ...config, ...updates });
  };

  // 处理布局变化
  const handleLayoutChange = (layoutType: string) => {
    let layout: TimelineLayout;
    switch (layoutType) {
      case 'Horizontal':
        layout = 'Horizontal';
        break;
      case 'Vertical':
        layout = 'Vertical';
        break;
      case 'Grid':
        layout = { Grid: { columns: 3 } };
        break;
      default:
        layout = 'Horizontal';
    }
    updateConfig({ layout });
  };

  // 获取当前布局类型
  const getCurrentLayoutType = (): string => {
    if (typeof config.layout === 'string') {
      return config.layout;
    } else if (typeof config.layout === 'object' && 'Grid' in config.layout) {
      return 'Grid';
    }
    return 'Horizontal';
  };

  // 获取网格列数
  const getGridColumns = (): number => {
    if (typeof config.layout === 'object' && 'Grid' in config.layout) {
      return config.layout.Grid.columns;
    }
    return 3;
  };

  // 更新网格列数
  const updateGridColumns = (columns: number) => {
    updateConfig({ layout: { Grid: { columns } } });
  };

  return (
    <div className="space-y-4">
      {/* 帧数配置 */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            时间轴帧数
          </label>
          <input
            type="number"
            value={config.frame_count}
            onChange={(e) => updateConfig({ frame_count: parseInt(e.target.value) })}
            min={1}
            max={50}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            帧间距 (px)
          </label>
          <input
            type="number"
            value={config.spacing}
            onChange={(e) => updateConfig({ spacing: parseInt(e.target.value) })}
            min={0}
            max={20}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg"
          />
        </div>
      </div>

      {/* 布局配置 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
          <Grid className="w-4 h-4" />
          布局方式
        </label>

        <CustomSelect
          value={getCurrentLayoutType()}
          onChange={handleLayoutChange}
          options={[
            { value: 'Horizontal', label: '水平排列' },
            { value: 'Vertical', label: '垂直排列' },
            { value: 'Grid', label: '网格排列' },
          ]}
        />

        {getCurrentLayoutType() === 'Grid' && (
          <div>
            <label className="block text-xs text-gray-500 mb-1">网格列数</label>
            <input
              type="number"
              value={getGridColumns()}
              onChange={(e) => updateGridColumns(parseInt(e.target.value))}
              min={1}
              max={10}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
            />
          </div>
        )}
      </div>

      {/* 样式配置 */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
          <Palette className="w-4 h-4" />
          样式设置
        </label>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-xs text-gray-500 mb-1">背景颜色</label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={config.background_color || '#000000'}
                onChange={(e) => updateConfig({ background_color: e.target.value })}
                className="w-10 h-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={config.background_color || '#000000'}
                onChange={(e) => updateConfig({ background_color: e.target.value })}
                placeholder="#000000"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm"
              />
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-500 mb-1">边框宽度 (px)</label>
            <input
              type="number"
              value={config.border_width || 1}
              onChange={(e) => updateConfig({ border_width: parseInt(e.target.value) })}
              min={0}
              max={10}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
            />
          </div>
        </div>

        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={config.show_timestamps}
            onChange={(e) => updateConfig({ show_timestamps: e.target.checked })}
            className="rounded"
          />
          <span className="text-sm">显示时间戳</span>
        </label>
      </div>

      {/* 预览区域 */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-3">布局预览</h4>
        <div className="flex items-center justify-center">
          {getCurrentLayoutType() === 'Horizontal' && (
            <div className="flex gap-1">
              {Array.from({ length: Math.min(config.frame_count, 8) }).map((_, i) => (
                <div
                  key={i}
                  className="w-8 h-6 bg-blue-200 rounded border"
                  style={{ marginRight: i < Math.min(config.frame_count, 8) - 1 ? `${config.spacing}px` : 0 }}
                />
              ))}
              {config.frame_count > 8 && <span className="text-xs text-gray-500 ml-2">...</span>}
            </div>
          )}

          {getCurrentLayoutType() === 'Vertical' && (
            <div className="flex flex-col gap-1">
              {Array.from({ length: Math.min(config.frame_count, 6) }).map((_, i) => (
                <div
                  key={i}
                  className="w-12 h-4 bg-blue-200 rounded border"
                  style={{ marginBottom: i < Math.min(config.frame_count, 6) - 1 ? `${config.spacing}px` : 0 }}
                />
              ))}
              {config.frame_count > 6 && <span className="text-xs text-gray-500 mt-1">...</span>}
            </div>
          )}

          {getCurrentLayoutType() === 'Grid' && (
            <div 
              className="grid gap-1"
              style={{ 
                gridTemplateColumns: `repeat(${getGridColumns()}, 1fr)`,
                gap: `${config.spacing}px`
              }}
            >
              {Array.from({ length: Math.min(config.frame_count, getGridColumns() * 3) }).map((_, i) => (
                <div
                  key={i}
                  className="w-6 h-4 bg-blue-200 rounded border"
                />
              ))}
              {config.frame_count > getGridColumns() * 3 && (
                <div className="col-span-full text-xs text-gray-500 text-center mt-1">...</div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
