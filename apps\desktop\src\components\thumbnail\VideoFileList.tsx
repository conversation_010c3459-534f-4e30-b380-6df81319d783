import React from 'react';
import { FileVideo, Eye, Clock, HardDrive } from 'lucide-react';
import { VideoFile, formatDuration, formatFileSize } from '../../types/thumbnail';

interface VideoFileListProps {
  videos: VideoFile[];
  onPreview: (videoPath: string, timestamp: number) => void;
}

/**
 * 视频文件列表组件
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
export const VideoFileList: React.FC<VideoFileListProps> = ({
  videos,
  onPreview,
}) => {
  if (videos.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileVideo className="w-12 h-12 mx-auto mb-3 text-gray-300" />
        <p>没有找到视频文件</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">
          视频文件列表 ({videos.length} 个)
        </h4>
        <div className="text-xs text-gray-500">
          总大小: {formatFileSize(videos.reduce((sum, v) => sum + v.size, 0))}
        </div>
      </div>

      <div className="max-h-64 overflow-y-auto space-y-2">
        {videos.map((video, index) => (
          <div
            key={index}
            className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            {/* 文件图标 */}
            <div className="flex-shrink-0">
              <FileVideo className={`w-5 h-5 ${video.is_valid ? 'text-blue-500' : 'text-red-500'}`} />
            </div>

            {/* 文件信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h5 className="text-sm font-medium text-gray-900 truncate">
                  {video.name}
                </h5>
                {!video.is_valid && (
                  <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded">
                    无效
                  </span>
                )}
              </div>

              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                {video.duration && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatDuration(video.duration)}
                  </div>
                )}

                {video.resolution && (
                  <div>
                    {video.resolution[0]}×{video.resolution[1]}
                  </div>
                )}

                <div className="flex items-center gap-1">
                  <HardDrive className="w-3 h-3" />
                  {formatFileSize(video.size)}
                </div>

                {video.format && (
                  <div className="uppercase">
                    {video.format}
                  </div>
                )}
              </div>
            </div>

            {/* 预览按钮 */}
            {video.is_valid && video.duration && (
              <button
                onClick={() => onPreview(video.path, video.duration! / 2)}
                className="flex-shrink-0 p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                title="预览缩略图"
              >
                <Eye className="w-4 h-4" />
              </button>
            )}
          </div>
        ))}
      </div>

      {/* 统计信息 */}
      <div className="pt-3 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {videos.filter(v => v.is_valid).length}
            </div>
            <div className="text-xs text-gray-500">有效文件</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {videos.filter(v => !v.is_valid).length}
            </div>
            <div className="text-xs text-gray-500">无效文件</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {formatDuration(
                videos
                  .filter(v => v.duration)
                  .reduce((sum, v) => sum + (v.duration || 0), 0)
              )}
            </div>
            <div className="text-xs text-gray-500">总时长</div>
          </div>
        </div>
      </div>
    </div>
  );
};
