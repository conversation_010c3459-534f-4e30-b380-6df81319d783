import React from 'react';
import {
  CogIcon,
  FilmIcon,
  SpeakerWaveIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { VideoGenerationConfig } from '../../types/videoGeneration';

interface CompactVideoConfigPanelProps {
  config: VideoGenerationConfig;
  onConfigChange: (config: VideoGenerationConfig) => void;
}

/**
 * 紧凑型视频配置面板组件
 * 适用于底部参数设置区域
 */
export const CompactVideoConfigPanel: React.FC<CompactVideoConfigPanelProps> = ({
  config,
  onConfigChange
}) => {
  const handleConfigUpdate = (updates: Partial<VideoGenerationConfig>) => {
    onConfigChange({ ...config, ...updates });
  };

  return (
    <div className="space-y-4">
      {/* 标题 */}
      <div className="flex items-center gap-2">
        <CogIcon className="h-4 w-4 text-primary-600" />
        <h3 className="text-sm font-medium text-gray-900">视频参数</h3>
      </div>

      {/* 参数控制区 */}
      <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {/* 输出格式 */}
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">
            <FilmIcon className="inline h-3 w-3 mr-1" />
            格式
          </label>
          <select
            value={config.output_format}
            onChange={(e) => handleConfigUpdate({ 
              output_format: e.target.value as 'mp4' | 'mov' | 'avi' 
            })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="mp4">MP4</option>
            <option value="mov">MOV</option>
            <option value="avi">AVI</option>
          </select>
        </div>

        {/* 分辨率 */}
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">分辨率</label>
          <select
            value={config.resolution}
            onChange={(e) => handleConfigUpdate({ 
              resolution: e.target.value as '720p' | '1080p' | '4k' 
            })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="720p">720p</option>
            <option value="1080p">1080p</option>
            <option value="4k">4K</option>
          </select>
        </div>

        {/* 帧率 */}
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">帧率</label>
          <select
            value={config.frame_rate}
            onChange={(e) => handleConfigUpdate({ 
              frame_rate: parseInt(e.target.value) as 24 | 30 | 60 
            })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-transparent"
          >
            <option value={24}>24fps</option>
            <option value={30}>30fps</option>
            <option value={60}>60fps</option>
          </select>
        </div>

        {/* 时长 */}
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">
            <ClockIcon className="inline h-3 w-3 mr-1" />
            时长(秒)
          </label>
          <input
            type="number"
            min="5"
            max="300"
            value={config.duration}
            onChange={(e) => handleConfigUpdate({ 
              duration: parseInt(e.target.value) || 30 
            })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        {/* 质量 */}
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">质量</label>
          <select
            value={config.quality}
            onChange={(e) => handleConfigUpdate({ 
              quality: e.target.value as 'low' | 'medium' | 'high' 
            })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="low">低</option>
            <option value="medium">中</option>
            <option value="high">高</option>
          </select>
        </div>

        {/* 音频开关 */}
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">
            <SpeakerWaveIcon className="inline h-3 w-3 mr-1" />
            音频
          </label>
          <div className="flex items-center">
            <button
              onClick={() => handleConfigUpdate({ 
                audio_enabled: !config.audio_enabled 
              })}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ${
                config.audio_enabled ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 ${
                  config.audio_enabled ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
            <span className="ml-2 text-xs text-gray-600">
              {config.audio_enabled ? '开启' : '关闭'}
            </span>
          </div>
        </div>
      </div>

      {/* 预估信息 */}
      <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
        <div className="flex items-center gap-4">
          <span>
            预估大小: {(() => {
              const baseSize = config.duration * 2;
              const qualityMultiplier = config.quality === 'high' ? 2 : config.quality === 'medium' ? 1.5 : 1;
              const resolutionMultiplier = config.resolution === '4k' ? 4 : config.resolution === '1080p' ? 2 : 1;
              const estimatedSize = baseSize * qualityMultiplier * resolutionMultiplier;
              return `${estimatedSize.toFixed(1)} MB`;
            })()}
          </span>
          <span>
            预估时间: {(() => {
              const baseTime = config.duration * 0.5;
              const qualityMultiplier = config.quality === 'high' ? 2 : config.quality === 'medium' ? 1.5 : 1;
              const estimatedTime = baseTime * qualityMultiplier;
              return `${Math.ceil(estimatedTime)} 秒`;
            })()}
          </span>
        </div>
        
        <div className="text-xs text-gray-400">
          {config.resolution} • {config.frame_rate}fps • {config.output_format.toUpperCase()}
        </div>
      </div>

      {/* 高级选项（可展开） */}
      <details className="group">
        <summary className="flex items-center gap-2 text-xs font-medium text-gray-700 cursor-pointer hover:text-gray-900">
          <span>高级选项</span>
          <svg className="h-3 w-3 transition-transform group-open:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </summary>
        
        <div className="mt-3 grid grid-cols-2 lg:grid-cols-4 gap-3">
          {/* 特效选项 */}
          {[
            { id: 'fade', label: '淡入淡出' },
            { id: 'zoom', label: '缩放效果' },
            { id: 'slide', label: '滑动转场' },
            { id: 'blur', label: '模糊背景' }
          ].map((effect) => (
            <label key={effect.id} className="flex items-center text-xs">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500 mr-2"
                onChange={(e) => {
                  const effects = config.effects || [];
                  if (e.target.checked) {
                    handleConfigUpdate({
                      effects: [...effects, { 
                        type: 'transition', 
                        name: effect.id, 
                        parameters: {} 
                      }]
                    });
                  } else {
                    handleConfigUpdate({
                      effects: effects.filter(eff => eff.name !== effect.id)
                    });
                  }
                }}
              />
              <span className="text-gray-700">{effect.label}</span>
            </label>
          ))}
        </div>
      </details>
    </div>
  );
};
