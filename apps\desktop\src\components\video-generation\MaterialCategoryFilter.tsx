import React from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { 
  MaterialCategory, 
  MATERIAL_CATEGORY_CONFIG 
} from '../../types/videoGeneration';

interface MaterialCategoryFilterProps {
  selectedCategory?: MaterialCategory;
  onCategoryChange: (category: MaterialCategory | undefined) => void;
}

/**
 * 素材分类过滤器组件
 * 支持下拉选择和快速筛选
 */
export const MaterialCategoryFilter: React.FC<MaterialCategoryFilterProps> = ({
  selectedCategory,
  onCategoryChange
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const categories = Object.values(MaterialCategory);

  const handleCategorySelect = (category: MaterialCategory | undefined) => {
    onCategoryChange(category);
    setIsOpen(false);
  };

  const selectedConfig = selectedCategory ? MATERIAL_CATEGORY_CONFIG[selectedCategory] : null;

  return (
    <div className="relative">
      {/* 下拉按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-sm border border-gray-200/50 rounded-lg hover:bg-white hover:shadow-medium transition-all duration-200 min-w-[140px] touch-target shadow-subtle"
      >
        {selectedConfig ? (
          <>
            <span className="text-lg">{selectedConfig.icon}</span>
            <span className="text-body-small font-medium text-high-emphasis">
              {selectedConfig.label}
            </span>
          </>
        ) : (
          <span className="text-body-small font-medium text-high-emphasis">
            全部分类
          </span>
        )}
        <ChevronDownIcon 
          className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* 菜单内容 */}
          <div className="absolute top-full left-0 mt-1 w-64 glass-effect border border-gray-200/50 rounded-lg shadow-dramatic z-20 py-2 animate-scale-in">
            {/* 全部分类选项 */}
            <button
              onClick={() => handleCategorySelect(undefined)}
              className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors duration-200 ${
                !selectedCategory ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
              }`}
            >
              <div className="w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-sm">📁</span>
              </div>
              <div className="flex-1">
                <div className="font-medium">全部分类</div>
                <div className="text-xs text-gray-500">显示所有素材</div>
              </div>
            </button>

            {/* 分隔线 */}
            <div className="my-2 border-t border-gray-100" />

            {/* 分类选项 */}
            {categories.map((category) => {
              const config = MATERIAL_CATEGORY_CONFIG[category];
              const isSelected = selectedCategory === category;
              
              return (
                <button
                  key={category}
                  onClick={() => handleCategorySelect(category)}
                  className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors duration-200 ${
                    isSelected ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${config.bgColor}`}>
                    <span className="text-sm">{config.icon}</span>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{config.label}</div>
                    <div className="text-xs text-gray-500">{config.description}</div>
                  </div>
                  {isSelected && (
                    <div className="w-2 h-2 bg-primary-500 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};
