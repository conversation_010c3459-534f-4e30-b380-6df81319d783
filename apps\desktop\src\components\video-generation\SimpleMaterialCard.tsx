import React from 'react';
import {
  PlayIcon,
  MusicalNoteIcon,
  DocumentTextIcon,
  PhotoIcon,
  VideoCameraIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { 
  MaterialAsset, 
  MATERIAL_CATEGORY_CONFIG 
} from '../../types/videoGeneration';

interface SimpleMaterialCardProps {
  asset: MaterialAsset;
  isSelected?: boolean;
  onSelect?: (asset: MaterialAsset) => void;
  onPreview?: (asset: MaterialAsset) => void;
}

/**
 * 简化版素材卡片组件
 * 只显示核心信息：缩略图、名称、类型
 */
export const SimpleMaterialCard: React.FC<SimpleMaterialCardProps> = ({
  asset,
  isSelected = false,
  onSelect,
  onPreview
}) => {
  const categoryConfig = MATERIAL_CATEGORY_CONFIG[asset.category];

  // 获取文件类型图标
  const getTypeIcon = () => {
    switch (asset.type) {
      case 'image':
        return <PhotoIcon className="h-4 w-4" />;
      case 'video':
        return <VideoCameraIcon className="h-4 w-4" />;
      case 'audio':
        return <MusicalNoteIcon className="h-4 w-4" />;
      case 'text':
        return <DocumentTextIcon className="h-4 w-4" />;
      default:
        return <DocumentTextIcon className="h-4 w-4" />;
    }
  };

  return (
    <div
      className={`group relative bg-white rounded-lg border transition-all duration-200 overflow-hidden cursor-pointer hover:shadow-md ${
        isSelected 
          ? 'ring-2 ring-primary-500 border-primary-300 shadow-md' 
          : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={() => onSelect?.(asset)}
    >
      {/* 缩略图区域 */}
      <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
        {asset.thumbnail_path ? (
          <img
            src={asset.thumbnail_path}
            alt={asset.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className={`p-2 rounded-full ${categoryConfig.bgColor}`}>
              <span className="text-lg">{categoryConfig.icon}</span>
            </div>
          </div>
        )}

        {/* 类型标识 */}
        <div className="absolute top-1 left-1">
          <div className={`flex items-center gap-1 px-1.5 py-0.5 rounded text-xs font-medium ${categoryConfig.bgColor} ${categoryConfig.color} backdrop-blur-sm`}>
            {getTypeIcon()}
          </div>
        </div>

        {/* 播放按钮（视频/音频） */}
        {(asset.type === 'video' || asset.type === 'audio') && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onPreview?.(asset);
              }}
              className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors duration-200"
            >
              <PlayIcon className="h-4 w-4" />
            </button>
          </div>
        )}

        {/* 选择状态指示器 */}
        {isSelected && (
          <div className="absolute top-1 right-1 w-5 h-5 bg-primary-600 text-white rounded-full flex items-center justify-center shadow-md">
            <CheckIcon className="h-3 w-3" />
          </div>
        )}
      </div>

      {/* 内容区域 */}
      <div className="p-2">
        <h3 className="font-medium text-gray-900 text-xs truncate mb-1">
          {asset.name}
        </h3>

        {/* 简化的元数据 */}
        <div className="flex items-center justify-between">
          <span className={`px-1.5 py-0.5 rounded text-xs ${categoryConfig.bgColor} ${categoryConfig.color}`}>
            {categoryConfig.label}
          </span>

          {/* 时长或大小信息 */}
          {asset.metadata?.duration && (
            <span className="text-xs text-gray-500">
              {Math.floor(asset.metadata.duration / 60)}:{(asset.metadata.duration % 60).toString().padStart(2, '0')}
            </span>
          )}
          {asset.metadata?.size && !asset.metadata?.duration && (
            <span className="text-xs text-gray-500">
              {(asset.metadata.size / 1024 / 1024).toFixed(1)}MB
            </span>
          )}
        </div>
      </div>

      {/* 悬停效果 */}
      <div className={`absolute inset-0 rounded-lg transition-all duration-200 pointer-events-none ${
        isSelected 
          ? 'bg-primary-500/5' 
          : 'bg-transparent group-hover:bg-gray-500/5'
      }`} />
    </div>
  );
};
