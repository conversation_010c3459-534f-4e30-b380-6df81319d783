import React from 'react';
import {
  CogIcon,
  FilmIcon,
  SpeakerWaveIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { VideoGenerationConfig } from '../../types/videoGeneration';

interface VideoConfigPanelProps {
  config: VideoGenerationConfig;
  onConfigChange: (config: VideoGenerationConfig) => void;
}

/**
 * 视频配置面板组件
 * 支持配置视频生成的各种参数
 */
export const VideoConfigPanel: React.FC<VideoConfigPanelProps> = ({
  config,
  onConfigChange
}) => {
  const handleConfigUpdate = (updates: Partial<VideoGenerationConfig>) => {
    onConfigChange({ ...config, ...updates });
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* 标题 */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
        <div className="flex items-center gap-2">
          <CogIcon className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">视频生成配置</h3>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          配置视频输出格式、质量和其他参数
        </p>
      </div>

      {/* 配置内容 */}
      <div className="p-6 space-y-6">
        {/* 基础设置 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 输出格式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FilmIcon className="inline h-4 w-4 mr-1" />
              输出格式
            </label>
            <select
              value={config.output_format}
              onChange={(e) => handleConfigUpdate({ 
                output_format: e.target.value as 'mp4' | 'mov' | 'avi' 
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="mp4">MP4 (推荐)</option>
              <option value="mov">MOV</option>
              <option value="avi">AVI</option>
            </select>
          </div>

          {/* 分辨率 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              分辨率
            </label>
            <select
              value={config.resolution}
              onChange={(e) => handleConfigUpdate({ 
                resolution: e.target.value as '720p' | '1080p' | '4k' 
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="720p">720p (1280×720)</option>
              <option value="1080p">1080p (1920×1080)</option>
              <option value="4k">4K (3840×2160)</option>
            </select>
          </div>

          {/* 帧率 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              帧率 (FPS)
            </label>
            <select
              value={config.frame_rate}
              onChange={(e) => handleConfigUpdate({ 
                frame_rate: parseInt(e.target.value) as 24 | 30 | 60 
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value={24}>24 FPS (电影级)</option>
              <option value={30}>30 FPS (标准)</option>
              <option value={60}>60 FPS (高流畅度)</option>
            </select>
          </div>

          {/* 视频时长 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <ClockIcon className="inline h-4 w-4 mr-1" />
              视频时长 (秒)
            </label>
            <input
              type="number"
              min="5"
              max="300"
              value={config.duration}
              onChange={(e) => handleConfigUpdate({ 
                duration: parseInt(e.target.value) || 30 
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              建议时长：5-60秒
            </p>
          </div>
        </div>

        {/* 质量设置 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            视频质量
          </label>
          <div className="grid grid-cols-3 gap-3">
            {[
              { value: 'low', label: '低质量', desc: '文件小，加载快' },
              { value: 'medium', label: '中等质量', desc: '平衡质量与大小' },
              { value: 'high', label: '高质量', desc: '最佳画质' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => handleConfigUpdate({ 
                  quality: option.value as 'low' | 'medium' | 'high' 
                })}
                className={`p-4 border rounded-lg text-left transition-all duration-200 ${
                  config.quality === option.value
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium">{option.label}</div>
                <div className="text-xs text-gray-500 mt-1">{option.desc}</div>
              </button>
            ))}
          </div>
        </div>

        {/* 音频设置 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-gray-700">
              <SpeakerWaveIcon className="inline h-4 w-4 mr-1" />
              音频设置
            </label>
            <button
              onClick={() => handleConfigUpdate({ 
                audio_enabled: !config.audio_enabled 
              })}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                config.audio_enabled ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                  config.audio_enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
          
          <div className={`transition-opacity duration-200 ${
            config.audio_enabled ? 'opacity-100' : 'opacity-50'
          }`}>
            <p className="text-sm text-gray-600">
              {config.audio_enabled 
                ? '将包含背景音乐和音效' 
                : '生成的视频将不包含音频'
              }
            </p>
          </div>
        </div>

        {/* 高级设置 */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">高级设置</h4>
          
          <div className="space-y-4">
            {/* 特效设置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                视频特效
              </label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { id: 'fade', label: '淡入淡出' },
                  { id: 'zoom', label: '缩放效果' },
                  { id: 'slide', label: '滑动转场' },
                  { id: 'blur', label: '模糊背景' }
                ].map((effect) => (
                  <label key={effect.id} className="flex items-center">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      onChange={(e) => {
                        const effects = config.effects || [];
                        if (e.target.checked) {
                          handleConfigUpdate({
                            effects: [...effects, { 
                              type: 'transition', 
                              name: effect.id, 
                              parameters: {} 
                            }]
                          });
                        } else {
                          handleConfigUpdate({
                            effects: effects.filter(eff => eff.name !== effect.id)
                          });
                        }
                      }}
                    />
                    <span className="ml-2 text-sm text-gray-700">{effect.label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 预估信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">预估信息</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">预估文件大小：</span>
              <span className="font-medium">
                {(() => {
                  const baseSize = config.duration * 2; // MB per second base
                  const qualityMultiplier = config.quality === 'high' ? 2 : config.quality === 'medium' ? 1.5 : 1;
                  const resolutionMultiplier = config.resolution === '4k' ? 4 : config.resolution === '1080p' ? 2 : 1;
                  const estimatedSize = baseSize * qualityMultiplier * resolutionMultiplier;
                  return `${estimatedSize.toFixed(1)} MB`;
                })()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">预估生成时间：</span>
              <span className="font-medium">
                {(() => {
                  const baseTime = config.duration * 0.5; // seconds per second of video
                  const qualityMultiplier = config.quality === 'high' ? 2 : config.quality === 'medium' ? 1.5 : 1;
                  const estimatedTime = baseTime * qualityMultiplier;
                  return `${Math.ceil(estimatedTime)} 秒`;
                })()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
