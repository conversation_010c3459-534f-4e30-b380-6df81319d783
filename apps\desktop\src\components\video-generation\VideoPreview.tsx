import React, { useState } from 'react';
import {
  PlayIcon,
  PauseIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  ArrowsPointingOutIcon,
  EyeIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import {
  VideoGenerationProject,
  VideoGenerationConfig,
  MaterialCategory,
  MaterialAsset,
  MATERIAL_CATEGORY_CONFIG
} from '../../types/videoGeneration';

interface VideoPreviewProps {
  project: VideoGenerationProject;
  onConfigChange?: (config: VideoGenerationConfig) => void;
}

/**
 * 视频预览组件
 * 显示选中的素材和生成配置的预览
 */
export const VideoPreview: React.FC<VideoPreviewProps> = ({
  project
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showConfigPanel, setShowConfigPanel] = useState(false);

  // 获取所有选中的素材
  const getAllSelectedAssets = () => {
    const allAssets: (MaterialAsset & { category: MaterialCategory })[] = [];
    Object.entries(project.selected_assets).forEach(([category, assets]) => {
      if (assets && assets.length > 0) {
        allAssets.push(...assets.map(asset => ({ ...asset, category: category as MaterialCategory })));
      }
    });
    return allAssets;
  };

  const selectedAssets = getAllSelectedAssets();

  // 模拟播放控制
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleMuteToggle = () => {
    setIsMuted(!isMuted);
  };

  return (
    <div className="space-y-6">
      {/* 预览区域 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <EyeIcon className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">视频预览</h3>
            </div>
            <button
              onClick={() => setShowConfigPanel(!showConfigPanel)}
              className="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <CogIcon className="h-4 w-4" />
              配置
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* 视频预览窗口 */}
          <div className="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg overflow-hidden relative">
            {/* 模拟视频内容 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <PlayIcon className="h-8 w-8" />
                </div>
                <p className="text-lg font-medium">视频预览</p>
                <p className="text-sm text-gray-300 mt-1">
                  {project.generation_config.resolution} • {project.generation_config.frame_rate}fps • {project.generation_config.duration}s
                </p>
              </div>
            </div>

            {/* 播放控制覆盖层 */}
            <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 group">
              <div className="absolute bottom-4 left-4 right-4">
                <div className="flex items-center justify-between text-white">
                  {/* 播放控制 */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handlePlayPause}
                      className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors duration-200"
                    >
                      {isPlaying ? (
                        <PauseIcon className="h-5 w-5" />
                      ) : (
                        <PlayIcon className="h-5 w-5 ml-0.5" />
                      )}
                    </button>
                    
                    <button
                      onClick={handleMuteToggle}
                      className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors duration-200"
                    >
                      {isMuted ? (
                        <SpeakerXMarkIcon className="h-4 w-4" />
                      ) : (
                        <SpeakerWaveIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>

                  {/* 全屏按钮 */}
                  <button className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors duration-200">
                    <ArrowsPointingOutIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* 进度条 */}
                <div className="mt-3">
                  <div className="w-full bg-white/20 rounded-full h-1">
                    <div className="bg-primary-500 h-1 rounded-full w-1/3"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 配置信息 */}
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-gray-600">格式</div>
              <div className="font-medium">{project.generation_config.output_format.toUpperCase()}</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-gray-600">分辨率</div>
              <div className="font-medium">{project.generation_config.resolution}</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-gray-600">帧率</div>
              <div className="font-medium">{project.generation_config.frame_rate} FPS</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-gray-600">时长</div>
              <div className="font-medium">{project.generation_config.duration}s</div>
            </div>
          </div>
        </div>
      </div>

      {/* 选中素材概览 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
          <h3 className="text-lg font-medium text-gray-900">选中素材</h3>
          <p className="text-sm text-gray-600 mt-1">
            共选择了 {selectedAssets.length} 个素材
          </p>
        </div>

        <div className="p-6">
          {selectedAssets.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>尚未选择任何素材</p>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.values(MaterialCategory).map((category) => {
                const categoryAssets = project.selected_assets[category] || [];
                if (categoryAssets.length === 0) return null;

                const config = MATERIAL_CATEGORY_CONFIG[category];
                
                return (
                  <div key={category} className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className={`px-4 py-2 ${config.bgColor} border-b border-gray-200`}>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{config.icon}</span>
                        <span className={`font-medium ${config.color}`}>
                          {config.label}
                        </span>
                        <span className="text-xs text-gray-500">
                          ({categoryAssets.length} 个)
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {categoryAssets.map((asset) => (
                          <div key={asset.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded-lg">
                            {asset.thumbnail_path ? (
                              <img
                                src={asset.thumbnail_path}
                                alt={asset.name}
                                className="w-12 h-12 object-cover rounded-md"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                                <span className="text-lg">{config.icon}</span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {asset.name}
                              </p>
                              <p className="text-xs text-gray-500 truncate">
                                {asset.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* 生成统计 */}
      <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-xl p-6 border border-primary-200">
        <h3 className="text-lg font-medium text-primary-900 mb-4">生成统计</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-700">{selectedAssets.length}</div>
            <div className="text-sm text-primary-600">选中素材</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-700">{project.generation_config.duration}s</div>
            <div className="text-sm text-primary-600">视频时长</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-700">
              {(() => {
                const baseSize = project.generation_config.duration * 2;
                const qualityMultiplier = project.generation_config.quality === 'high' ? 2 : 
                                        project.generation_config.quality === 'medium' ? 1.5 : 1;
                const resolutionMultiplier = project.generation_config.resolution === '4k' ? 4 : 
                                           project.generation_config.resolution === '1080p' ? 2 : 1;
                return `${(baseSize * qualityMultiplier * resolutionMultiplier).toFixed(1)}MB`;
              })()}
            </div>
            <div className="text-sm text-primary-600">预估大小</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-700">
              {project.generation_config.quality === 'high' ? '高' : 
               project.generation_config.quality === 'medium' ? '中' : '低'}
            </div>
            <div className="text-sm text-primary-600">输出质量</div>
          </div>
        </div>
      </div>
    </div>
  );
};
