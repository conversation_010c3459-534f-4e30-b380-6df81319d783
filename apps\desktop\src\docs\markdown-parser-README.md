# Tree-sitter Markdown解析器

基于Tree-sitter的Markdown解析工具，保留原文引用信息，提供精确的语法分析和位置跟踪功能。

## 🚀 功能特性

### 核心功能
- **精确解析**: 基于pulldown-cmark的语法树解析，比传统正则表达式更准确
- **原文引用**: 保留每个节点的精确位置信息（行号、列号、字符偏移）
- **错误恢复**: 即使在语法错误存在时也能提供有用的解析结果
- **多种查询**: 支持标题、链接、代码块等特定节点类型的查询
- **文档验证**: 检查文档结构的一致性和潜在问题
- **实时解析**: 支持实时解析和增量更新

### 技术架构
- **后端**: Rust + pulldown-cmark + Tauri
- **前端**: React + TypeScript
- **通信**: Tauri命令系统
- **位置跟踪**: 自定义位置计算算法

## 📦 项目结构

```
apps/desktop/
├── src-tauri/src/
│   ├── infrastructure/
│   │   └── markdown_parser.rs          # 核心解析器实现
│   └── presentation/commands/
│       └── markdown_commands.rs        # Tauri命令接口
├── src/
│   ├── types/
│   │   └── markdown.ts                 # TypeScript类型定义
│   ├── services/
│   │   └── markdownService.ts          # 前端服务封装
│   ├── components/
│   │   └── MarkdownParserRenderer.tsx  # React渲染组件
│   ├── examples/
│   │   └── MarkdownParserExample.tsx   # 使用示例
│   └── tests/                          # 测试文件
└── docs/
    └── markdown-parser-guide.md        # 详细使用指南
```

## 🛠️ 安装和配置

### 依赖项
在 `Cargo.toml` 中添加：
```toml
pulldown-cmark = "0.9"
```

### 注册命令
在 `lib.rs` 中注册Tauri命令：
```rust
.manage(commands::markdown_commands::MarkdownParserState::new())
.invoke_handler(tauri::generate_handler![
    commands::markdown_commands::parse_markdown,
    commands::markdown_commands::query_markdown_nodes,
    commands::markdown_commands::find_markdown_node_at_position,
    commands::markdown_commands::extract_markdown_outline,
    commands::markdown_commands::extract_markdown_links,
    commands::markdown_commands::validate_markdown,
])
```

## 🎯 快速开始

### 基本解析
```typescript
import { markdownService } from '../services/markdownService';

const markdown = `
# 标题
这是一个**粗体**文本和一个[链接](https://example.com)。
`;

try {
  const result = await markdownService.parseMarkdown(markdown);
  console.log('解析结果:', result);
} catch (error) {
  console.error('解析失败:', error);
}
```

### React组件使用
```tsx
import MarkdownParserRenderer from '../components/MarkdownParserRenderer';

const MyComponent = () => {
  const handleNodeClick = (node) => {
    console.log('点击的节点:', node);
  };

  return (
    <MarkdownParserRenderer
      content={markdown}
      showOutline={true}
      showLinks={true}
      showValidation={true}
      showPositionInfo={true}
      onNodeClick={handleNodeClick}
    />
  );
};
```

## 📋 API参考

### MarkdownService

#### parseMarkdown(text, config?)
解析Markdown文档并返回完整的解析结果。

#### extractOutline(text)
提取文档大纲（标题结构）。

#### extractLinks(text)
提取文档中的所有链接和图片。

#### validateMarkdown(text)
验证Markdown文档结构。

#### queryNodes(text, queryType)
查询特定类型的节点（'headings', 'links', 'code'）。

#### findNodeAtPosition(text, line, column)
根据位置查找节点。

### MarkdownParserRenderer组件

#### 属性
- `content`: Markdown文本内容
- `showOutline`: 是否显示大纲
- `showLinks`: 是否显示链接列表
- `showValidation`: 是否显示验证结果
- `showPositionInfo`: 是否显示位置信息
- `enableRealTimeParsing`: 是否启用实时解析
- `onNodeClick`: 节点点击回调
- `onParseComplete`: 解析完成回调

## 🧪 测试

### 运行测试
```bash
# 运行Rust测试
cargo test markdown_parser --lib

# 运行前端测试
npm test -- MarkdownParserRenderer
```

### 测试覆盖
- ✅ 基本Markdown解析
- ✅ 标题提取和大纲生成
- ✅ 链接和图片提取
- ✅ 代码块查询
- ✅ 位置信息查找
- ✅ 文档结构验证
- ✅ 错误处理和边界情况
- ✅ 大文档性能测试

## 🔧 配置选项

### 解析器配置
```typescript
const config: MarkdownParserConfig = {
  preserve_whitespace: true,    // 是否保留空白节点
  parse_inline_html: false,     // 是否解析内联HTML
  max_depth: 50,                // 最大解析深度
  timeout_ms: 5000,             // 超时时间（毫秒）
};
```

## 📊 性能特性

- **解析速度**: 支持大文档（10MB+）的快速解析
- **内存效率**: 优化的AST结构，减少内存占用
- **实时更新**: 防抖机制支持实时编辑
- **错误恢复**: 部分语法错误不影响整体解析

## 🐛 故障排除

### 常见问题

1. **解析器初始化失败**
   - 检查Tauri后端是否正常运行
   - 确认pulldown-cmark依赖已正确安装

2. **解析超时**
   - 增加timeout_ms配置值
   - 考虑分块处理大文档

3. **位置信息不准确**
   - 确保文本编码一致（UTF-8）
   - 检查换行符格式（LF vs CRLF）

### 调试技巧
```typescript
// 启用详细日志
const result = await markdownService.parseMarkdown(markdown);
console.log('解析统计:', result.statistics);

// 验证文档结构
const validation = await markdownService.validateMarkdown(markdown);
console.log('验证结果:', validation);
```

## 🚧 开发状态

### 已完成
- ✅ 核心解析器实现
- ✅ Tauri命令接口
- ✅ TypeScript类型定义
- ✅ React渲染组件
- ✅ 基础测试套件
- ✅ 使用文档和示例

### 计划中
- 🔄 语法高亮支持
- 🔄 自定义查询语言
- 🔄 插件系统
- 🔄 导出格式支持
- 🔄 性能优化

## 📄 许可证

本项目遵循项目根目录的许可证条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个解析器。

### 开发指南
1. 遵循promptx/tauri-desktop-app-expert的开发规范
2. 添加适当的测试覆盖
3. 更新相关文档
4. 确保代码通过所有检查

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 查看详细使用指南：`docs/markdown-parser-guide.md`
- 参考示例代码：`examples/MarkdownParserExample.tsx`
