import React from 'react';
import { MultiTurnChatTest } from '../components/MultiTurnChatTest';

/**
 * 多轮对话测试页面
 * 遵循前端开发规范的页面组件设计
 */
export const MultiTurnChatTestPage: React.FC = () => {
  return (
    <div className="h-screen flex flex-col">
      {/* 页面头部 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">多轮对话功能测试</h1>
            <p className="text-sm text-gray-600 mt-1">
              测试基于Gemini API的多轮对话功能，支持会话历史管理和上下文保持
            </p>
          </div>
          <div className="text-sm text-gray-500">
            <div>版本: v0.2.1</div>
            <div>模型: Gemini 2.5 Flash</div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-1 overflow-hidden">
        <MultiTurnChatTest />
      </main>

      {/* 页面底部 */}
      <footer className="bg-gray-50 border-t border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div>
            基于 Tauri + React + TypeScript 构建的多轮对话系统
          </div>
          <div className="flex items-center gap-4">
            <span>🤖 AI助手</span>
            <span>💬 多轮对话</span>
            <span>📝 会话管理</span>
            <span>🔄 上下文保持</span>
          </div>
        </div>
      </footer>
    </div>
  );
};
