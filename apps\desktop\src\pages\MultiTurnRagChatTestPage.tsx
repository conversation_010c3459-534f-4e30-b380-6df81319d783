import React from 'react';
import { MultiTurnRagChatTest } from '../components/MultiTurnRagChatTest';

/**
 * 多轮RAG对话测试页面
 * 基于检索增强生成的多轮对话功能测试界面
 */
export const MultiTurnRagChatTestPage: React.FC = () => {
  return (
    <div className="h-screen flex flex-col">
      {/* 页面头部 */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">多轮RAG对话功能测试</h1>
            <p className="text-sm text-gray-600 mt-1">
              基于检索增强生成(RAG)的多轮对话系统，支持会话历史管理和智能检索
            </p>
          </div>
          <div className="text-sm text-gray-500">
            <div>版本: v0.2.2</div>
            <div>模型: Gemini 2.5 Flash</div>
            <div>检索: Vertex AI Search</div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-1 overflow-hidden">
        <MultiTurnRagChatTest />
      </main>

      {/* 页面底部 */}
      <footer className="bg-gray-50 border-t border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div>
            基于 Tauri + React + TypeScript 构建的RAG多轮对话系统
          </div>
          <div className="flex items-center gap-4">
            <span>🔍 智能检索</span>
            <span>💬 多轮对话</span>
            <span>📚 知识增强</span>
            <span>🔄 上下文保持</span>
          </div>
        </div>
      </footer>
    </div>
  );
};
