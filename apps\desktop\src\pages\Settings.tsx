import React, { useState } from 'react';
import { ScreenAdaptationSettings } from '../components/ScreenAdaptationSettings';

/**
 * 设置页面
 * 包含各种应用设置选项
 */
const Settings: React.FC = () => {
  const [showScreenSettings, setShowScreenSettings] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-semibold text-gray-900">应用设置</h1>
            <p className="text-gray-600 mt-1">配置应用的各项参数和偏好设置</p>
          </div>

          <div className="p-6">
            <div className="space-y-6">
              {/* 显示设置 */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h2 className="text-lg font-medium text-gray-900 mb-4">显示设置</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">屏幕适配</h3>
                      <p className="text-sm text-gray-500">根据屏幕尺寸自动调整窗口大小</p>
                    </div>
                    <button
                      onClick={() => setShowScreenSettings(true)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
                    >
                      配置
                    </button>
                  </div>
                </div>
              </div>

              {/* 性能设置 */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h2 className="text-lg font-medium text-gray-900 mb-4">性能设置</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">硬件加速</h3>
                      <p className="text-sm text-gray-500">启用GPU加速以提升性能</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">自动保存</h3>
                      <p className="text-sm text-gray-500">自动保存项目更改</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* 通知设置 */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h2 className="text-lg font-medium text-gray-900 mb-4">通知设置</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">桌面通知</h3>
                      <p className="text-sm text-gray-500">显示系统桌面通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">声音提醒</h3>
                      <p className="text-sm text-gray-500">播放通知声音</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* 数据设置 */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h2 className="text-lg font-medium text-gray-900 mb-4">数据设置</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">数据备份</h3>
                      <p className="text-sm text-gray-500">定期备份应用数据</p>
                    </div>
                    <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm">
                      立即备份
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">清理缓存</h3>
                      <p className="text-sm text-gray-500">清理临时文件和缓存</p>
                    </div>
                    <button className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors text-sm">
                      清理
                    </button>
                  </div>
                </div>
              </div>

              {/* 关于信息 */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h2 className="text-lg font-medium text-gray-900 mb-4">关于</h2>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>应用版本:</strong> 0.2.1</p>
                  <p><strong>构建时间:</strong> {new Date().toLocaleDateString()}</p>
                  <p><strong>技术栈:</strong> Tauri + React + TypeScript</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 屏幕适配设置弹窗 */}
      {showScreenSettings && (
        <ScreenAdaptationSettings
          onClose={() => setShowScreenSettings(false)}
        />
      )}
    </div>
  );
};

export default Settings;
