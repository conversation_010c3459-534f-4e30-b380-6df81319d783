import React from 'react';
import { AdvancedFilterDemo } from '../../components/outfit/AdvancedFilterDemo';

/**
 * 高级过滤器工具页面
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
const AdvancedFilterTool: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* 页面头部 */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-xl font-bold text-gray-900">高级过滤器演示</h1>
                <p className="text-sm text-gray-600">
                  展示和测试高级过滤器组件功能
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500">
                开发工具
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <AdvancedFilterDemo />
      </div>
    </div>
  );
};

export default AdvancedFilterTool;
