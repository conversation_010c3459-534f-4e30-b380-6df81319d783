import React from 'react';
import { 
  Bug, 
  ArrowLeft
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import JsonParserDebugPanel from '../../components/JsonParserDebugPanel';

/**
 * JSON解析器调试面板工具详情页
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
const DebugPanelTool: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      {/* 页面标题和返回按钮 */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/tools')}
          className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center shadow-sm">
            <Bug className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">JSON解析器调试面板</h1>
            <p className="text-gray-600">用于测试后端命令是否正常工作的调试工具</p>
          </div>
        </div>
      </div>

      {/* 工具描述 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
            <Bug className="w-4 h-4 text-orange-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">调试功能</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 测试后端JSON解析命令的可用性</li>
              <li>• 实时查看命令执行结果和错误信息</li>
              <li>• 支持多种测试场景和边界条件</li>
              <li>• 提供详细的性能统计和调试信息</li>
              <li>• 开发者友好的错误诊断工具</li>
            </ul>
          </div>
        </div>
      </div>

      {/* JSON解析器调试面板组件 */}
      <JsonParserDebugPanel />
    </div>
  );
};

export default DebugPanelTool;
