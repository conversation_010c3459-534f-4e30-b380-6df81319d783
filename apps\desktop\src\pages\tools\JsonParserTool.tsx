import React from 'react';
import { 
  Code, 
  ArrowLeft
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import TolerantJsonParser from '../../components/TolerantJsonParser';

/**
 * 容错JSON解析器工具详情页
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
const JsonParserTool: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      {/* 页面标题和返回按钮 */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/tools')}
          className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm">
            <Code className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">容错JSON解析器</h1>
            <p className="text-gray-600">基于Tree-sitter的大模型JSON容错解析器</p>
          </div>
        </div>
      </div>

      {/* 工具描述 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-lg flex items-center justify-center">
            <Code className="w-4 h-4 text-indigo-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">功能特性</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 支持处理大模型返回的不规范JSON数据</li>
              <li>• 基于Tree-sitter解析器，提供高性能解析能力</li>
              <li>• 支持注释、无引号键名、尾随逗号等非标准格式</li>
              <li>• 智能识别并解析JSON字符串字段中的YAML内容</li>
              <li>• 提供多种错误恢复策略和详细的解析统计</li>
              <li>• 实时预览解析结果和错误信息</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 容错JSON解析器组件 */}
      <TolerantJsonParser />
    </div>
  );
};

export default JsonParserTool;
