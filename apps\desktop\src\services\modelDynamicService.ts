import { invoke } from '@tauri-apps/api/core';
import { 
  ModelDynamic, 
  CreateDynamicRequest, 
  UpdateDynamicRequest, 
  ModelDynamicStats,
  DynamicStatus,
  VideoGenerationStatus
} from '../types/model';

// 模特动态服务
export const modelDynamicService = {
  // 创建动态
  async createDynamic(request: CreateDynamicRequest): Promise<ModelDynamic> {
    try {
      return await invoke<ModelDynamic>('create_model_dynamic', { request });
    } catch (error) {
      console.error('创建模特动态失败:', error);
      throw error;
    }
  },

  // 获取动态详情
  async getDynamicById(id: string): Promise<ModelDynamic | null> {
    try {
      return await invoke<ModelDynamic>('get_model_dynamic_by_id', { id });
    } catch (error) {
      console.error('获取模特动态详情失败:', error);
      throw error;
    }
  },

  // 获取模特的所有动态
  async getDynamicsByModelId(modelId: string): Promise<ModelDynamic[]> {
    try {
      return await invoke<ModelDynamic[]>('get_model_dynamics_by_model_id', { modelId });
    } catch (error) {
      console.error('获取模特动态列表失败:', error);
      throw error;
    }
  },

  // 更新动态
  async updateDynamic(id: string, request: UpdateDynamicRequest): Promise<ModelDynamic> {
    try {
      return await invoke<ModelDynamic>('update_model_dynamic', { id, request });
    } catch (error) {
      console.error('更新模特动态失败:', error);
      throw error;
    }
  },

  // 删除动态
  async deleteDynamic(id: string): Promise<void> {
    try {
      await invoke('delete_model_dynamic', { id });
    } catch (error) {
      console.error('删除模特动态失败:', error);
      throw error;
    }
  },

  // 获取模特动态统计
  async getStatsByModelId(modelId: string): Promise<ModelDynamicStats> {
    try {
      return await invoke<ModelDynamicStats>('get_model_dynamic_stats', { modelId });
    } catch (error) {
      console.error('获取模特动态统计失败:', error);
      throw error;
    }
  },

  // 重新生成视频
  async regenerateVideo(dynamicId: string, videoId: string): Promise<void> {
    try {
      await invoke('regenerate_dynamic_video', { dynamicId, videoId });
    } catch (error) {
      console.error('重新生成视频失败:', error);
      throw error;
    }
  },

  // 获取可用的AI模型列表
  async getAvailableAIModels(): Promise<any[]> {
    try {
      return await invoke<any[]>('get_available_ai_models');
    } catch (error) {
      console.error('获取可用AI模型列表失败:', error);
      throw error;
    }
  },

  // 模拟数据 - 仅用于开发测试
  getMockDynamics(modelId: string): ModelDynamic[] {
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    
    return [
      {
        id: '1',
        model_id: modelId,
        title: '夏日写真',
        description: '今天拍了一组夏日写真，用AI生成了一些有趣的视频',
        prompt: '一个年轻女孩在海滩上奔跑，穿着白色连衣裙，阳光明媚，海浪轻拍，微风吹拂她的长发',
        source_image_path: 'https://images.unsplash.com/photo-1503185912284-5271ff81b9a8?q=80&w=1000',
        ai_model: '极梦',
        video_count: 4,
        generated_videos: [
          {
            id: 'v1',
            dynamic_id: '1',
            video_path: '/path/to/video1.mp4',
            thumbnail_path: 'https://images.unsplash.com/photo-1503185912284-5271ff81b9a8?q=80&w=1000',
            file_size: 1024 * 1024 * 5,
            duration: 15,
            status: VideoGenerationStatus.Completed,
            created_at: now.toISOString()
          },
          {
            id: 'v2',
            dynamic_id: '1',
            video_path: '/path/to/video2.mp4',
            thumbnail_path: 'https://images.unsplash.com/photo-1503185912284-5271ff81b9a8?q=80&w=1000',
            file_size: 1024 * 1024 * 4.5,
            duration: 12,
            status: VideoGenerationStatus.Completed,
            created_at: now.toISOString()
          },
          {
            id: 'v3',
            dynamic_id: '1',
            video_path: '/path/to/video3.mp4',
            thumbnail_path: 'https://images.unsplash.com/photo-1503185912284-5271ff81b9a8?q=80&w=1000',
            file_size: 1024 * 1024 * 6,
            duration: 18,
            status: VideoGenerationStatus.Generating,
            generation_progress: 75,
            created_at: now.toISOString()
          },
          {
            id: 'v4',
            dynamic_id: '1',
            video_path: '/path/to/video4.mp4',
            file_size: 0,
            duration: 0,
            status: VideoGenerationStatus.Failed,
            error_message: '生成失败，请重试',
            created_at: now.toISOString()
          }
        ],
        status: DynamicStatus.Published,
        created_at: now.toISOString(),
        updated_at: now.toISOString()
      },
      {
        id: '2',
        model_id: modelId,
        description: '尝试了一些新的风格',
        prompt: '一个女孩站在城市街道上，霓虹灯照亮她的侧脸，赛博朋克风格，未来感十足',
        source_image_path: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?q=80&w=1000',
        ai_model: '极梦',
        video_count: 2,
        generated_videos: [
          {
            id: 'v5',
            dynamic_id: '2',
            video_path: '/path/to/video5.mp4',
            thumbnail_path: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?q=80&w=1000',
            file_size: 1024 * 1024 * 7,
            duration: 20,
            status: VideoGenerationStatus.Completed,
            created_at: yesterday.toISOString()
          },
          {
            id: 'v6',
            dynamic_id: '2',
            video_path: '/path/to/video6.mp4',
            thumbnail_path: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?q=80&w=1000',
            file_size: 1024 * 1024 * 6.5,
            duration: 17,
            status: VideoGenerationStatus.Completed,
            created_at: yesterday.toISOString()
          }
        ],
        status: DynamicStatus.Published,
        created_at: yesterday.toISOString(),
        updated_at: yesterday.toISOString()
      }
    ];
  },

  // 模拟统计数据 - 仅用于开发测试
  getMockStats(_modelId: string): ModelDynamicStats {
    return {
      total_dynamics: 2,
      published_dynamics: 2,
      total_videos: 6,
      completed_videos: 4,
      generating_videos: 1,
      failed_videos: 1
    };
  }
};

export default modelDynamicService;
