import { create } from 'zustand';

/**
 * UI 状态管理
 * 遵循 Tauri 开发规范的 UI 状态管理模式
 */
interface UIState {
  // 主题
  theme: 'light' | 'dark';
  
  // 布局状态
  sidebarOpen: boolean;
  
  // 当前视图
  currentView: string;
  
  // 模态框状态
  showCreateProjectModal: boolean;
  showEditProjectModal: boolean;
  editingProject: any | null;
  
  // 操作
  setTheme: (theme: 'light' | 'dark') => void;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setCurrentView: (view: string) => void;
  
  // 模态框操作
  openCreateProjectModal: () => void;
  closeCreateProjectModal: () => void;
  openEditProjectModal: (project: any) => void;
  closeEditProjectModal: () => void;
}

export const useUIStore = create<UIState>((set) => ({
  // 初始状态
  theme: 'light',
  sidebarOpen: true,
  currentView: 'projects',
  showCreateProjectModal: false,
  showEditProjectModal: false,
  editingProject: null,

  // 主题操作
  setTheme: (theme) => {
    set({ theme });
    // 保存到本地存储
    localStorage.setItem('mixvideo-theme', theme);
  },

  // 侧边栏操作
  toggleSidebar: () => {
    set((state) => ({ sidebarOpen: !state.sidebarOpen }));
  },

  setSidebarOpen: (open) => {
    set({ sidebarOpen: open });
  },

  // 视图操作
  setCurrentView: (view) => {
    set({ currentView: view });
  },

  // 创建项目模态框
  openCreateProjectModal: () => {
    set({ showCreateProjectModal: true });
  },

  closeCreateProjectModal: () => {
    set({ showCreateProjectModal: false });
  },

  // 编辑项目模态框
  openEditProjectModal: (project) => {
    set({ 
      showEditProjectModal: true,
      editingProject: project 
    });
  },

  closeEditProjectModal: () => {
    set({ 
      showEditProjectModal: false,
      editingProject: null 
    });
  },
}));

// 初始化主题
const savedTheme = localStorage.getItem('mixvideo-theme') as 'light' | 'dark' | null;
if (savedTheme) {
  useUIStore.getState().setTheme(savedTheme);
}
