/**
 * 绑定管理功能动画样式
 * 遵循前端开发规范的动画设计原则
 */

/* 增强的淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 优雅的淡入上升动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡出动画 */
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* 滑入动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滑出动画 */
@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(20px);
  }
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 摇摆动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 优雅的悬停效果 */
.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
  transform: translateY(-2px);
}

/* 渐变文字效果 */
.text-gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== 响应式动画优化 ===== */

/* 移动端动画优化 */
@media (max-width: 768px) {
  .animate-fade-in-up {
    animation-duration: 0.3s;
  }

  .hover-glow:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
  }

  /* 移动端卡片悬停效果减弱 */
  .card-interactive:hover {
    transform: translateY(-1px) scale(1.01);
  }

  /* 移动端按钮点击效果 */
  .btn-standard:active,
  .btn-compact:active,
  .btn-large:active {
    transform: scale(0.98);
  }
}

/* 平板端动画优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .hover-glow:hover {
    transform: translateY(-1px);
  }

  .card-interactive:hover {
    transform: translateY(-1px) scale(1.015);
  }
}

/* 大屏幕动画增强 */
@media (min-width: 1920px) {
  .hover-glow:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.15), 0 10px 10px -5px rgba(59, 130, 246, 0.08);
  }

  .card-interactive:hover {
    transform: translateY(-2px) scale(1.03);
  }
}

/* ===== 弹框专用动画 ===== */

/* 弹框淡入动画 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 弹框缩放进入动画 */
@keyframes modalScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 弹框滑入动画 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹框淡出动画 */
@keyframes modalFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 弹框缩放退出动画 */
@keyframes modalScaleOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
}

/* 弹框动画类 */
.animate-modal-fade-in {
  animation: modalFadeIn 0.2s ease-out forwards;
}

.animate-modal-scale-in {
  animation: modalScaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-modal-slide-in {
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-modal-fade-out {
  animation: modalFadeOut 0.2s ease-in forwards;
}

.animate-modal-scale-out {
  animation: modalScaleOut 0.2s ease-in forwards;
}

/* 背景遮罩动画 */
.modal-backdrop {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-backdrop.entering {
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(0px);
}

.modal-backdrop.exiting {
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(0px);
}

/* 弹框容器样式 */
.modal-container {
  position: relative;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-container.large {
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
}

/* ===== 骨架屏动画 ===== */

/* 骨架屏波浪动画 */
@keyframes skeletonWave {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 骨架屏脉冲动画 */
@keyframes skeletonPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 骨架屏闪烁动画 */
@keyframes skeletonShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 骨架屏动画类 */
.animate-skeleton-wave {
  position: relative;
  overflow: hidden;
}

.animate-skeleton-wave::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: skeletonWave 1.6s ease-in-out infinite;
}

.animate-skeleton-pulse {
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.animate-loading-shimmer {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: skeletonShimmer 1.5s infinite;
}

/* 骨架屏容器优化 */
.skeleton-container {
  animation: fadeIn 0.3s ease-out;
}

/* ===== 微交互动画 ===== */

/* 按钮点击反馈 */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* 按钮悬停发光效果 */
@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

/* 成功反馈动画 */
@keyframes successPulse {
  0% {
    transform: scale(1);
    background-color: rgb(34, 197, 94);
  }
  50% {
    transform: scale(1.05);
    background-color: rgb(22, 163, 74);
  }
  100% {
    transform: scale(1);
    background-color: rgb(34, 197, 94);
  }
}

/* 错误震动动画 */
@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* 加载点动画 */
@keyframes loadingDots {
  0%, 20% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* 心跳动画 */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 弹跳进入动画 */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 滑入动画 */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 微交互动画类 */
.animate-button-press {
  animation: buttonPress 0.15s ease-out;
}

.animate-button-glow {
  animation: buttonGlow 2s ease-in-out infinite;
}

.animate-success-pulse {
  animation: successPulse 0.6s ease-out;
}

.animate-error-shake {
  animation: errorShake 0.5s ease-in-out;
}

.animate-loading-dots {
  animation: loadingDots 1.4s ease-in-out infinite;
}

.animate-heartbeat {
  animation: heartbeat 1s ease-in-out infinite;
}

.animate-bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .hover-glow,
  .card-interactive,
  .animate-modal-fade-in,
  .animate-modal-scale-in,
  .animate-modal-slide-in,
  .animate-modal-fade-out,
  .animate-modal-scale-out,
  .animate-skeleton-wave,
  .animate-skeleton-pulse,
  .animate-loading-shimmer {
    animation: none !important;
    transition: none !important;
  }

  .hover-glow:hover,
  .card-interactive:hover {
    transform: none !important;
  }

  .modal-backdrop,
  .modal-container {
    transition: none !important;
  }

  .animate-skeleton-wave::after {
    display: none;
  }
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slide-out {
  animation: slideOut 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out forwards;
}

.animate-bounce {
  animation: bounce 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 1s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 悬停效果 */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮动画 */
.button-press {
  transition: transform 0.1s ease-out;
}

.button-press:active {
  transform: scale(0.98);
}

/* 加载状态 */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 成功状态动画 */
.success-flash {
  animation: successFlash 0.6s ease-out;
}

@keyframes successFlash {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(34, 197, 94, 0.2);
  }
  100% {
    background-color: transparent;
  }
}

/* 错误状态动画 */
.error-flash {
  animation: errorFlash 0.6s ease-out;
}

@keyframes errorFlash {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(239, 68, 68, 0.2);
  }
  100% {
    background-color: transparent;
  }
}

/* 进度条动画 */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式动画 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 焦点动画 */
.focus-ring {
  transition: box-shadow 0.2s ease-out;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* 状态指示器 */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s infinite;
}

.status-indicator.active::before {
  background-color: #10b981;
}

.status-indicator.inactive::before {
  background-color: #6b7280;
}

.status-indicator.error::before {
  background-color: #ef4444;
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 工具提示动画 */
.tooltip {
  opacity: 0;
  transform: translateY(4px);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
  pointer-events: none;
}

.tooltip.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 模态框动画 */
.modal-backdrop {
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.modal-backdrop.show {
  opacity: 1;
}

.modal-content {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.modal-content.show {
  opacity: 1;
  transform: scale(1) translateY(0);
}
