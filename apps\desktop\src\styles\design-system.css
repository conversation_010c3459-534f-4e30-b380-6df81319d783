/* 模特管理设计系统 */

/* 设计令牌 - 颜色系统 */
:root {
  /* 主色调 - 现代蓝色系（与Tailwind配置一致） */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 语义色彩 */
  --success-50: #ecfdf5;
  --success-500: #10b981;
  --success-600: #059669;
  
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* 兼容性变量 - 支持旧的命名格式 */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);

  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);

  --color-green-50: var(--success-50);
  --color-green-100: #d1fae5;
  --color-green-500: var(--success-500);
  --color-green-600: var(--success-600);
  --color-green-800: #065f46;

  --color-yellow-50: var(--warning-50);
  --color-yellow-100: #fef3c7;
  --color-yellow-500: var(--warning-500);
  --color-yellow-600: var(--warning-600);
  --color-yellow-800: #92400e;

  --color-red-50: var(--error-50);
  --color-red-100: #fee2e2;
  --color-red-500: var(--error-500);
  --color-red-600: var(--error-600);
  --color-red-800: #991b1b;

  --color-blue-50: var(--primary-50);
  --color-blue-100: var(--primary-100);
  --color-blue-500: var(--primary-500);
  --color-blue-600: var(--primary-600);
  --color-blue-800: var(--primary-800);

  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* 字体系统 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;

  /* 缓动函数 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 基础动画类 */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-spring);
}

.animate-bounce-in {
  animation: bounceIn var(--duration-slow) var(--ease-spring);
}

/* 关键帧动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% { 
    opacity: 0;
    transform: scale(0.3);
  }
  50% { 
    opacity: 1;
    transform: scale(1.05);
  }
  70% { 
    transform: scale(0.9);
  }
  100% { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 悬停效果 */
.hover-lift {
  transition: transform var(--duration-fast) var(--ease-out),
              box-shadow var(--duration-fast) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 焦点样式 */
.focus-ring {
  transition: box-shadow var(--duration-fast) var(--ease-out);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-200);
}

/* 加载状态 */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--gray-200) 25%,
    var(--gray-100) 50%,
    var(--gray-200) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 响应式断点 */
@media (max-width: 640px) {
  :root {
    --space-4: 0.75rem;
    --space-6: 1rem;
    --space-8: 1.5rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
  }
}

/* 自定义下拉选择框样式 */
.custom-select {
  position: relative;
}

.custom-select select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: none;
  padding-right: 2.5rem; /* 确保右侧有足够空间给图标 */
}

.custom-select select::-ms-expand {
  display: none; /* 隐藏IE的默认箭头 */
}

/* 下拉箭头图标样式 */
.custom-select .dropdown-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  transition: transform var(--duration-fast) var(--ease-out);
}

.custom-select:hover .dropdown-icon {
  transform: translateY(-50%) scale(1.1);
}

.custom-select select:focus + .dropdown-icon {
  color: var(--primary-500);
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== 统一的美观组件样式 ===== */

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, white 0%, rgba(59, 130, 246, 0.05) 50%, white 100%);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(229, 231, 235, 0.5);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8rem;
  height: 8rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  border-radius: 50%;
  transform: translate(4rem, -4rem);
  opacity: 0.5;
}

/* 统计卡片样式 */
.stat-card {
  background: linear-gradient(135deg, white 0%, rgba(249, 250, 251, 0.5) 100%);
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(229, 231, 235, 0.5);
  padding: 1.25rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  transform: translate(2rem, -2rem);
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 0.5;
}

/* 主色调统计卡片 */
.stat-card.primary::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 197, 253, 0.2) 100%);
}

/* 成功色统计卡片 */
.stat-card.success::before {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(134, 239, 172, 0.2) 100%);
}

/* 警告色统计卡片 */
.stat-card.warning::before {
  background: linear-gradient(135deg, rgba(251, 146, 60, 0.2) 0%, rgba(254, 215, 170, 0.2) 100%);
}

/* 紫色统计卡片 */
.stat-card.purple::before {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2) 0%, rgba(196, 181, 253, 0.2) 100%);
}

/* 内容卡片样式 */
.content-card {
  background: linear-gradient(135deg, white 0%, rgba(249, 250, 251, 0.3) 100%);
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(229, 231, 235, 0.5);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

/* 图标容器样式 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.icon-container::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.icon-container:hover::before {
  opacity: 1;
}

/* 主色调图标容器 */
.icon-container.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

/* 成功色图标容器 */
.icon-container.success {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
}

/* 警告色图标容器 */
.icon-container.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

/* 紫色图标容器 */
.icon-container.purple {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
  color: white;
}

/* 粉色图标容器 */
.icon-container.pink {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
  color: white;
}

/* ===== 响应式设计优化 ===== */

/* 移动端优化 */
@media (max-width: 640px) {
  .page-header {
    padding: 1rem;
  }

  .page-header::before {
    width: 4rem;
    height: 4rem;
    transform: translate(2rem, -2rem);
  }

  .stat-card {
    padding: 1rem;
  }

  .content-card {
    margin: 0.5rem 0;
  }

  /* 移动端按钮优化 */
  .btn-standard,
  .btn-compact,
  .btn-large {
    min-height: 44px; /* 符合触摸友好标准 */
  }

  /* 移动端文字大小调整 */
  .page-header h1 {
    font-size: 1.5rem;
  }

  .page-header p {
    font-size: 0.875rem;
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .page-header {
    padding: 1.25rem;
  }

  .stat-card {
    padding: 1.125rem;
  }
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .page-header {
    padding: 2rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .content-card {
    padding: 1.5rem;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .icon-container,
  .stat-card,
  .content-card,
  .page-header {
    border-width: 0.5px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {

  .stat-card,
  .content-card {
    background: linear-gradient(135deg, #1f2937 0%, rgba(31, 41, 55, 0.5) 100%);
    border-color: rgba(75, 85, 99, 0.5);
  }
}

/* ===== 触摸设备优化 ===== */

/* 触摸友好的交互元素 */
@media (pointer: coarse) {
  /* 增大触摸目标 */
  .btn-standard,
  .btn-compact,
  .btn-large {
    min-height: 44px;
    min-width: 44px;
  }

  /* 菜单项触摸优化 */
  .dropdown-item,
  button[role="menuitem"] {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* 卡片交互优化 */
  .card-interactive {
    cursor: pointer;
  }

  /* 移除悬停效果，使用点击效果 */
  .hover-glow:hover {
    transform: none;
    box-shadow: inherit;
  }

  .hover-glow:active {
    transform: scale(0.98);
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
  }
}

/* 精确指针设备优化 */
@media (pointer: fine) {
  /* 保持精细的悬停效果 */
  .hover-glow:hover {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
    transform: translateY(-2px);
  }

  /* 精细的卡片悬停效果 */
  .card-interactive:hover {
    transform: translateY(-2px) scale(1.02);
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .page-header {
    padding: 1rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  /* 紧凑的导航栏 */
  .navigation {
    height: 3rem;
  }
}

/* 竖屏模式优化 */
@media (orientation: portrait) {
  .page-header {
    padding: 1.25rem;
  }

  /* 竖屏下的网格优化 */
  .grid-responsive {
    grid-template-columns: 1fr;
  }

  @media (min-width: 640px) {
    .grid-responsive {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

/* ===== 弹框响应式优化 ===== */

/* 移动端弹框优化 */
@media (max-width: 640px) {
  .modal-container {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    border-radius: 1rem;
  }

  .modal-container.large {
    border-radius: 1rem;
  }

  /* 移动端弹框头部 */
  .modal-header-mobile {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  /* 移动端弹框内容 */
  .modal-content-mobile {
    padding: 1rem;
    max-height: calc(100vh - 8rem);
    overflow-y: auto;
  }

  /* 移动端按钮区域 */
  .modal-actions-mobile {
    padding: 1rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-actions-mobile button {
    width: 100%;
    justify-content: center;
  }
}

/* 平板端弹框优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .modal-container {
    margin: 2rem;
    max-height: calc(100vh - 4rem);
  }

  .modal-content-tablet {
    max-height: calc(100vh - 12rem);
    overflow-y: auto;
  }
}

/* 大屏幕弹框优化 */
@media (min-width: 1920px) {
  .modal-container {
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.35);
  }

  .modal-container.large {
    border-radius: 2rem;
  }
}

/* 弹框内容滚动优化 - 遵循前端开发规范 */
.modal-content-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  scroll-behavior: smooth;
  overscroll-behavior: contain;

  /* 确保内容区域可以正确滚动 */
  min-height: 0;

  /* iOS Safari 优化 */
  -webkit-overflow-scrolling: touch;
}

.modal-content-scroll::-webkit-scrollbar {
  width: 6px;
}

.modal-content-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.modal-content-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.modal-content-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* 兼容旧的 modal-scroll 类 */
.modal-scroll {
  max-height: calc(90vh - 8rem);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

.modal-scroll::-webkit-scrollbar {
  width: 6px;
}

.modal-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.modal-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.modal-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Modal 根容器 - 在 App.tsx 中定义，独立于主布局 */
.modal-root-container {
  /* 完全独立的定位层 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  /* 最高优先级，确保在所有内容之上 */
  z-index: 99999;

  /* 默认不可见，不影响其他元素 */
  pointer-events: none;

  /* 确保不受任何父级影响 */
  isolation: isolate;
  contain: layout style paint;
}

/* 当有 Modal 时，容器变为可交互 */
.modal-root-container:not(:empty) {
  pointer-events: auto;
}

/* 弹框遮罩层优化 - 简化版本，依赖根容器解决定位问题 */
.modal-overlay-container {
  /* 使用 fixed 定位，但现在相对于 modal-root-container */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  /* 在 modal-root 内的层级 */
  z-index: 1;

  /* 布局和动画 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;

  /* 动画效果 */
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 防止内容溢出 */
  overflow-y: auto;
  overflow-x: hidden;

  /* iOS Safari 优化 */
  -webkit-overflow-scrolling: touch;
}

/* 专门处理 h-screen flex flex-col 等复杂容器结构 */
.modal-overlay-container.complex-container-fix {
  /* 强制脱离文档流 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;

  /* 确保完全覆盖视口 */
  width: 100vw !important;
  height: 100vh !important;

  /* 最高优先级 */
  z-index: 999999 !important;

  /* 重置所有可能的父级影响 */
  transform: translateZ(0) !important;
  will-change: auto !important;
  contain: none !important;
  isolation: isolate !important;

  /* 确保不被父级容器限制 */
  clip: auto !important;
  clip-path: none !important;
  overflow: visible !important;

  /* 重置边距和内边距 */
  margin: 0 !important;
  padding: 1rem !important;
}

/* 针对复杂容器结构的修复策略 */
.modal-overlay-container[data-fixed-fallback="true"] {
  /* 强制使用 fixed 定位 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;

  /* 确保在最高层级 */
  z-index: 99999 !important;

  /* 重置可能的父级影响 */
  transform: none !important;
  clip: none !important;
  clip-path: none !important;

  /* 确保完全覆盖 */
  margin: 0 !important;
  padding: 1rem !important;

  /* 防止被父级容器裁剪 */
  overflow: visible !important;
}

/* 复杂容器结构下的背景遮罩修复 */
.modal-overlay-container[data-fixed-fallback="true"] .modal-backdrop-fixed {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: -1 !important;
}

/* 背景遮罩 - 独立层确保完全覆盖 */
.modal-backdrop-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: -1;

  /* 动画效果 */
  animation: backdropFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 弹框容器优化 */
.modal-container {
  position: relative;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0 auto;
  z-index: 1;

  /* 确保在移动设备上正确显示 */
  max-height: calc(100vh - 2rem);

  /* 防止内容被截断 */
  display: flex;
  flex-direction: column;
}

.modal-container.large {
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }
}

/* 弹框层级管理 */
.modal-z-index {
  z-index: 1000;
}

.modal-z-index.high {
  z-index: 1100;
}

.modal-z-index.highest {
  z-index: 1200;
}

/* ===== 全局滚动条优化 ===== */

/* 主内容区域滚动条 */
main {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

main::-webkit-scrollbar {
  width: 8px;
}

main::-webkit-scrollbar-track {
  background: transparent;
}

main::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

main::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* ===== 统一滚动条样式系统 ===== */

/* 通用滚动条样式 - 遵循前端开发规范 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.6);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* 细滚动条 - 用于紧凑空间 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* 粗滚动条 - 用于主要内容区域 */
.scrollbar-thick {
  scrollbar-width: auto;
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(243, 244, 246, 0.5);
}

.scrollbar-thick::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-thick::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 4px;
}

.scrollbar-thick::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.scrollbar-thick::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* 隐藏滚动条但保持功能 */
.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* 滚动行为优化 */
.smooth-scroll {
  scroll-behavior: smooth;
}

.overscroll-behavior-contain {
  overscroll-behavior: contain;
}

/* 主题色滚动条 */
.scrollbar-primary {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) transparent;
}

.scrollbar-primary::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-primary::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-primary::-webkit-scrollbar-thumb {
  background-color: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-primary::-webkit-scrollbar-thumb:hover {
  background-color: rgba(59, 130, 246, 0.7);
}

/* 成功色滚动条 */
.scrollbar-success {
  scrollbar-width: thin;
  scrollbar-color: rgba(34, 197, 94, 0.5) transparent;
}

.scrollbar-success::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-success::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-success::-webkit-scrollbar-thumb {
  background-color: rgba(34, 197, 94, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-success::-webkit-scrollbar-thumb:hover {
  background-color: rgba(34, 197, 94, 0.7);
}

/* ===== 视觉层次系统 ===== */

/* 字体层次 */
.text-display-1 {
  font-size: 3.5rem;
  line-height: 1.1;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.text-display-2 {
  font-size: 3rem;
  line-height: 1.15;
  font-weight: 700;
  letter-spacing: -0.015em;
}

.text-display-3 {
  font-size: 2.5rem;
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.01em;
}

.text-heading-1 {
  font-size: 2rem;
  line-height: 1.25;
  font-weight: 600;
  letter-spacing: -0.005em;
}

.text-heading-2 {
  font-size: 1.75rem;
  line-height: 1.3;
  font-weight: 600;
}

.text-heading-3 {
  font-size: 1.5rem;
  line-height: 1.35;
  font-weight: 600;
}

.text-heading-4 {
  font-size: 1.25rem;
  line-height: 1.4;
  font-weight: 600;
}

.text-heading-5 {
  font-size: 1.125rem;
  line-height: 1.45;
  font-weight: 600;
}

.text-heading-6 {
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 600;
}

.text-body-large {
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 400;
}

.text-body {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
}

.text-body-small {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
}

.text-caption {
  font-size: 0.75rem;
  line-height: 1.4;
  font-weight: 400;
  letter-spacing: 0.01em;
}

.text-overline {
  font-size: 0.75rem;
  line-height: 1.4;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

/* 颜色层次 */
.text-primary {
  color: rgb(var(--color-primary-600));
}

.text-primary-light {
  color: rgb(var(--color-primary-500));
}

.text-primary-dark {
  color: rgb(var(--color-primary-700));
}

.text-emphasis {
  color: rgb(var(--color-gray-900));
  font-weight: 600;
}

.text-high-emphasis {
  color: rgb(var(--color-gray-900));
}

.text-medium-emphasis {
  color: rgb(var(--color-gray-700));
}

.text-low-emphasis {
  color: rgb(var(--color-gray-500));
}

.text-disabled {
  color: rgb(var(--color-gray-400));
}

.text-success {
  color: rgb(var(--color-green-600));
}

.text-warning {
  color: rgb(var(--color-yellow-600));
}

.text-error {
  color: rgb(var(--color-red-600));
}

.text-info {
  color: rgb(var(--color-blue-600));
}

/* 间距层次 */
.space-xs { margin: 0.25rem; }
.space-sm { margin: 0.5rem; }
.space-md { margin: 1rem; }
.space-lg { margin: 1.5rem; }
.space-xl { margin: 2rem; }
.space-2xl { margin: 3rem; }
.space-3xl { margin: 4rem; }

.gap-xs { gap: 0.25rem; }
.gap-sm { gap: 0.5rem; }
.gap-md { gap: 1rem; }
.gap-lg { gap: 1.5rem; }
.gap-xl { gap: 2rem; }
.gap-2xl { gap: 3rem; }
.gap-3xl { gap: 4rem; }

.padding-xs { padding: 0.25rem; }
.padding-sm { padding: 0.5rem; }
.padding-md { padding: 1rem; }
.padding-lg { padding: 1.5rem; }
.padding-xl { padding: 2rem; }
.padding-2xl { padding: 3rem; }
.padding-3xl { padding: 4rem; }

/* 阴影层次 */
.shadow-subtle {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-soft {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-strong {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-intense {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-dramatic {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 边框层次 */
.border-subtle {
  border: 1px solid rgb(var(--color-gray-200));
}

.border-soft {
  border: 1px solid rgb(var(--color-gray-300));
}

.border-medium {
  border: 2px solid rgb(var(--color-gray-300));
}

.border-strong {
  border: 2px solid rgb(var(--color-gray-400));
}

.border-primary {
  border: 2px solid rgb(var(--color-primary-500));
}

.border-success {
  border: 2px solid rgb(var(--color-green-500));
}

.border-warning {
  border: 2px solid rgb(var(--color-yellow-500));
}

.border-error {
  border: 2px solid rgb(var(--color-red-500));
}

/* 圆角层次 */
.rounded-subtle { border-radius: 0.25rem; }
.rounded-soft { border-radius: 0.5rem; }
.rounded-medium { border-radius: 0.75rem; }
.rounded-strong { border-radius: 1rem; }
.rounded-intense { border-radius: 1.5rem; }
.rounded-dramatic { border-radius: 2rem; }

/* ===== 页面布局优化 ===== */

/* 固定导航栏布局 */
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.app-header {
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 50;
}

.app-main {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 内容区域优化 */
.content-container {
  min-height: 100%;
  padding: 1rem 1rem 2rem;
}

@media (min-width: 640px) {
  .content-container {
    padding: 1.5rem 1.5rem 3rem;
  }
}

@media (min-width: 1024px) {
  .content-container {
    padding: 2rem 2rem 4rem;
  }
}

/* 滚动区域阴影效果 */
.scroll-shadow {
  position: relative;
}

.scroll-shadow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.scroll-shadow.scrolled::before {
  opacity: 1;
}

/* 滚动到底部指示器 */
.scroll-indicator {
  position: relative;
}

.scroll-indicator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.scroll-indicator.has-more::after {
  opacity: 1;
}

/* ===== 高级视觉效果 ===== */

/* 渐变背景 */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
}

/* 文本渐变 */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-success {
  background: linear-gradient(135deg, rgb(var(--color-green-500)), rgb(var(--color-green-600)));
}

.bg-gradient-warning {
  background: linear-gradient(135deg, rgb(var(--color-yellow-500)), rgb(var(--color-yellow-600)));
}

.bg-gradient-error {
  background: linear-gradient(135deg, rgb(var(--color-red-500)), rgb(var(--color-red-600)));
}

.bg-gradient-info {
  background: linear-gradient(135deg, rgb(var(--color-blue-500)), rgb(var(--color-blue-600)));
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 发光效果 */
.glow-primary {
  box-shadow: 0 0 20px rgba(var(--color-primary-500), 0.3);
}

.glow-success {
  box-shadow: 0 0 20px rgba(var(--color-green-500), 0.3);
}

.glow-warning {
  box-shadow: 0 0 20px rgba(var(--color-yellow-500), 0.3);
}

.glow-error {
  box-shadow: 0 0 20px rgba(var(--color-red-500), 0.3);
}

/* 状态指示器 */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.status-online::before {
  background-color: rgb(var(--color-green-500));
}

.status-indicator.status-busy::before {
  background-color: rgb(var(--color-red-500));
}

.status-indicator.status-away::before {
  background-color: rgb(var(--color-yellow-500));
}

.status-indicator.status-offline::before {
  background-color: rgb(var(--color-gray-400));
}

/* 进度指示器 */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgb(var(--color-gray-200));
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, rgb(var(--color-primary-500)), rgb(var(--color-primary-600)));
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-bar-animated .progress-bar-fill {
  background-size: 20px 20px;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}

/* 徽章和标签 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 0.375rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background-color: rgb(var(--color-primary-100));
  color: rgb(var(--color-primary-800));
}

.badge-secondary {
  background-color: rgb(var(--color-gray-100));
  color: rgb(var(--color-gray-800));
}

.badge-success {
  background-color: rgb(var(--color-green-100));
  color: rgb(var(--color-green-800));
}

.badge-warning {
  background-color: rgb(var(--color-yellow-100));
  color: rgb(var(--color-yellow-800));
}

.badge-error {
  background-color: rgb(var(--color-red-100));
  color: rgb(var(--color-red-800));
}

.badge-info {
  background-color: rgb(var(--color-blue-100));
  color: rgb(var(--color-blue-800));
}

/* 分割线 */
.divider {
  border: none;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgb(var(--color-gray-300)),
    transparent
  );
  margin: 1rem 0;
}

.divider-vertical {
  width: 1px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent,
    rgb(var(--color-gray-300)),
    transparent
  );
  margin: 0 1rem;
}

/* 卡片变体 */
.card-elevated {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgb(var(--color-gray-100));
}

.card-outlined {
  background: white;
  border-radius: 1rem;
  border: 2px solid rgb(var(--color-gray-200));
}

.card-filled {
  background: rgb(var(--color-gray-50));
  border-radius: 1rem;
  border: 1px solid rgb(var(--color-gray-200));
}

.card-interactive {
  transition: all 0.2s ease;
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 焦点状态 */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid rgb(var(--color-primary-500));
  outline-offset: 2px;
}

.focus-ring-inset:focus {
  outline: 2px solid rgb(var(--color-primary-500));
  outline-offset: -2px;
}

/* ===== 移动端优化 ===== */

/* 触摸友好的交互元素 */
@media (max-width: 768px) {
  /* 最小触摸目标 44px */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* 移动端按钮优化 */
  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .btn-sm {
    min-height: 40px;
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
  }

  .btn-lg {
    min-height: 48px;
    padding: 0.875rem 1.25rem;
    font-size: 1.125rem;
  }

  /* 移动端输入框优化 */
  .form-input,
  .form-textarea,
  .form-select {
    min-height: 44px;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 0.5rem;
  }

  /* 移动端导航优化 */
  .nav-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid rgb(var(--color-gray-200));
    padding: 0.5rem;
    z-index: 50;
  }

  .nav-mobile-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    min-height: 44px;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .nav-mobile-item:hover {
    background-color: rgb(var(--color-gray-100));
  }

  .nav-mobile-item.active {
    background-color: rgb(var(--color-primary-100));
    color: rgb(var(--color-primary-700));
  }

  /* 移动端模态框优化 */
  .modal-mobile {
    position: fixed;
    inset: 0;
    z-index: 50;
    padding: 1rem;
  }

  .modal-mobile .modal-content {
    width: 100%;
    max-height: calc(100vh - 2rem);
    margin: auto;
    border-radius: 1rem;
    overflow: hidden;
  }

  /* 移动端卡片优化 */
  .card-mobile {
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }

  /* 移动端表格优化 */
  .table-mobile {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .table-mobile thead {
    display: none;
  }

  .table-mobile tbody,
  .table-mobile tr,
  .table-mobile td {
    display: block;
  }

  .table-mobile tr {
    border: 1px solid rgb(var(--color-gray-200));
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
  }

  .table-mobile td {
    border: none;
    padding: 0.25rem 0;
    position: relative;
    padding-left: 30%;
  }

  .table-mobile td:before {
    content: attr(data-label);
    position: absolute;
    left: 0;
    width: 25%;
    padding-right: 0.5rem;
    white-space: nowrap;
    font-weight: 600;
    color: rgb(var(--color-gray-700));
  }

  /* 移动端间距调整 */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .section-padding {
    padding: 2rem 0;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* 移动端字体调整 */
  .text-display-1 { font-size: 2.5rem; }
  .text-display-2 { font-size: 2rem; }
  .text-display-3 { font-size: 1.75rem; }
  .text-heading-1 { font-size: 1.5rem; }
  .text-heading-2 { font-size: 1.25rem; }
  .text-heading-3 { font-size: 1.125rem; }

  /* 移动端滚动优化 */
  .scroll-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 移动端手势支持 */
  .swipe-container {
    touch-action: pan-x;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
  }

  .swipe-item {
    scroll-snap-align: start;
    flex-shrink: 0;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .btn {
    min-height: 40px;
  }

  .form-input,
  .form-textarea,
  .form-select {
    min-height: 40px;
  }

  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .text-display-1 { font-size: 4rem; }
  .text-display-2 { font-size: 3.5rem; }
  .text-display-3 { font-size: 3rem; }
}

/* 触摸设备专用样式 */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果 */
  .hover\:bg-gray-50:hover {
    background-color: inherit;
  }

  .hover\:shadow-md:hover {
    box-shadow: inherit;
  }

  /* 增强点击反馈 */
  .btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .card-interactive:active {
    transform: scale(0.99);
    transition: transform 0.1s ease;
  }

  /* 触摸友好的链接 */
  a {
    -webkit-tap-highlight-color: rgba(var(--color-primary-500), 0.3);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .border-subtle {
    border-color: rgb(var(--color-gray-900));
  }

  .text-low-emphasis {
    color: rgb(var(--color-gray-700));
  }

  .bg-gradient-primary {
    background: rgb(var(--color-primary-600));
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .card-interactive:hover {
    transform: none;
  }

  .btn:active {
    transform: none;
  }

  .swipe-container {
    scroll-behavior: auto;
  }
}

/* ===== 可访问性优化 ===== */

/* 键盘导航 */
.keyboard-navigation {
  outline: none;
}

.keyboard-navigation:focus-visible {
  outline: 2px solid rgb(var(--color-primary-500));
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: rgb(var(--color-primary-600));
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 0.25rem;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 高对比度指示器 */
.high-contrast-border {
  border: 2px solid transparent;
}

@media (prefers-contrast: high) {
  .high-contrast-border {
    border-color: currentColor;
  }
}

/* 焦点陷阱 */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* ARIA 状态指示 */
[aria-expanded="true"] .expand-icon {
  transform: rotate(180deg);
}

[aria-selected="true"] {
  background-color: rgb(var(--color-primary-100));
  color: rgb(var(--color-primary-900));
}

[aria-disabled="true"] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

[aria-invalid="true"] {
  border-color: rgb(var(--color-red-500));
  box-shadow: 0 0 0 1px rgb(var(--color-red-500));
}

/* 实时区域 */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* ===== 性能优化 ===== */

/* GPU 加速 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 内容可见性优化 */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* 图片懒加载优化 */
.lazy-image {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image-placeholder {
  background: linear-gradient(
    90deg,
    rgb(var(--color-gray-200)) 25%,
    rgb(var(--color-gray-300)) 50%,
    rgb(var(--color-gray-200)) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

/* 虚拟滚动优化 */
.virtual-scroll-container {
  overflow: auto;
  height: 100%;
}

.virtual-scroll-item {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

/* 防抖动画 */
.debounced-animation {
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

/* 内存优化 */
.memory-efficient {
  contain: layout style paint;
}

/* 渲染优化 */
.render-optimized {
  contain: strict;
  content-visibility: auto;
}

/* 滚动性能优化 */
.scroll-optimized {
  overflow: auto;
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

@supports (scroll-timeline: works) {
  .scroll-optimized {
    scroll-timeline: --scroll-timeline block;
  }
}

/* 字体加载优化 */
.font-display-swap {
  font-display: swap;
}

/* 关键资源预加载 */
.preload-critical {
  font-display: block;
}

/* 非关键资源延迟加载 */
.load-deferred {
  font-display: optional;
}

/* ===== 工具类 ===== */

/* 布局工具 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.grid-center {
  display: grid;
  place-items: center;
}

/* 文本工具 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动态列表优化样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动态卡片样式优化 */
.dynamic-card {
  background: linear-gradient(135deg, white 0%, rgba(249, 250, 251, 0.8) 100%);
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dynamic-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* 缩略图容器优化 */
.thumbnail-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

/* 增强聊天界面样式 */
.enhanced-chat-interface {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.chat-message-bubble {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-message-bubble.user {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.chat-message-bubble.assistant {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 引用角标样式 */
.reference-footnote {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.reference-footnote:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

/* 素材卡片样式 */
.chat-material-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-material-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

/* 引用提示框样式 */
.reference-tooltip {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(229, 231, 235, 0.8);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 动画效果 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* 文本高亮效果 */
.text-highlight {
  background: linear-gradient(120deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  border-bottom: 1px dotted rgba(59, 130, 246, 0.5);
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 1px 2px;
}

.text-highlight:hover {
  background: linear-gradient(120deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.15) 100%);
  border-bottom-color: rgba(59, 130, 246, 0.8);
}

/* 聊天消息动画 */
@keyframes message-slide-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.chat-message-enter {
  animation: message-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 素材网格布局优化 */
.material-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.material-grid.compact {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.75rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.5rem;
  }

  .chat-message-bubble {
    max-width: 85%;
  }

  .reference-footnote {
    width: 1.25rem;
    height: 1.25rem;
    font-size: 0.625rem;
  }
}

/* 滚动条样式优化 */
.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.thumbnail-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.05) 100%);
  pointer-events: none;
}

/* 间距工具 */
.space-y-auto > * + * {
  margin-top: auto;
}

.space-x-auto > * + * {
  margin-left: auto;
}

/* 可见性工具 */
.visible-on-hover {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group:hover .visible-on-hover {
  opacity: 1;
}

.visible-on-focus {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group:focus-within .visible-on-focus {
  opacity: 1;
}

/* 状态工具 */
.loading-state {
  pointer-events: none;
  opacity: 0.7;
  cursor: wait;
}

.error-state {
  border-color: rgb(var(--color-red-500));
  background-color: rgb(var(--color-red-50));
}

.success-state {
  border-color: rgb(var(--color-green-500));
  background-color: rgb(var(--color-green-50));
}

.warning-state {
  border-color: rgb(var(--color-yellow-500));
  background-color: rgb(var(--color-yellow-50));
}

/* 视频生成工作台专用样式 */

/* 滚动条隐藏 */
.scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* 自定义滚动条 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* 缩略图容器 */
.thumbnail-container {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}
