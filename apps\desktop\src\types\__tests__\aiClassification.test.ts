import { describe, it, expect } from 'vitest';
import {
  validateClassificationForm,
  hasFormErrors,
  classificationToFormData,
  formDataToCreateRequest,
  formDataToUpdateRequest,
  AiClassificationFormData,
  AiClassification,
  CLASSIFICATION_VALIDATION,
} from '../aiClassification';

describe('AI Classification Type Functions', () => {
  const validFormData: AiClassificationFormData = {
    name: '全身',
    prompt_text: '头顶到脚底完整入镜，肢体可见度≥90%',
    description: '全身分类描述',
    sort_order: 1,
    weight: 10,
  };

  const mockClassification: AiClassification = {
    id: 'test-id',
    name: '全身',
    prompt_text: '头顶到脚底完整入镜，肢体可见度≥90%',
    description: '全身分类描述',
    is_active: true,
    sort_order: 1,
    weight: 10,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  describe('validateClassificationForm', () => {
    it('should pass validation for valid form data', () => {
      const errors = validateClassificationForm(validFormData);
      expect(Object.keys(errors)).toHaveLength(0);
    });

    it('should fail validation for empty name', () => {
      const formData = { ...validFormData, name: '' };
      const errors = validateClassificationForm(formData);
      expect(errors.name).toBe('分类名称不能为空');
    });

    it('should fail validation for whitespace-only name', () => {
      const formData = { ...validFormData, name: '   ' };
      const errors = validateClassificationForm(formData);
      expect(errors.name).toBe('分类名称不能为空');
    });

    it('should fail validation for name too long', () => {
      const formData = { 
        ...validFormData, 
        name: 'a'.repeat(CLASSIFICATION_VALIDATION.NAME_MAX_LENGTH + 1) 
      };
      const errors = validateClassificationForm(formData);
      expect(errors.name).toBe(`分类名称不能超过${CLASSIFICATION_VALIDATION.NAME_MAX_LENGTH}个字符`);
    });

    it('should fail validation for empty prompt_text', () => {
      const formData = { ...validFormData, prompt_text: '' };
      const errors = validateClassificationForm(formData);
      expect(errors.prompt_text).toBe('提示词不能为空');
    });

    it('should fail validation for whitespace-only prompt_text', () => {
      const formData = { ...validFormData, prompt_text: '   ' };
      const errors = validateClassificationForm(formData);
      expect(errors.prompt_text).toBe('提示词不能为空');
    });

    it('should fail validation for prompt_text too long', () => {
      const formData = { 
        ...validFormData, 
        prompt_text: 'a'.repeat(CLASSIFICATION_VALIDATION.PROMPT_TEXT_MAX_LENGTH + 1) 
      };
      const errors = validateClassificationForm(formData);
      expect(errors.prompt_text).toBe(`提示词不能超过${CLASSIFICATION_VALIDATION.PROMPT_TEXT_MAX_LENGTH}个字符`);
    });

    it('should fail validation for description too long', () => {
      const formData = { 
        ...validFormData, 
        description: 'a'.repeat(CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH + 1) 
      };
      const errors = validateClassificationForm(formData);
      expect(errors.description).toBe(`描述不能超过${CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH}个字符`);
    });

    it('should pass validation for empty description', () => {
      const formData = { ...validFormData, description: '' };
      const errors = validateClassificationForm(formData);
      expect(errors.description).toBeUndefined();
    });

    it('should fail validation for sort_order too small', () => {
      const formData = { 
        ...validFormData, 
        sort_order: CLASSIFICATION_VALIDATION.MIN_SORT_ORDER - 1 
      };
      const errors = validateClassificationForm(formData);
      expect(errors.sort_order).toBe(
        `排序顺序必须在${CLASSIFICATION_VALIDATION.MIN_SORT_ORDER}-${CLASSIFICATION_VALIDATION.MAX_SORT_ORDER}之间`
      );
    });

    it('should fail validation for sort_order too large', () => {
      const formData = { 
        ...validFormData, 
        sort_order: CLASSIFICATION_VALIDATION.MAX_SORT_ORDER + 1 
      };
      const errors = validateClassificationForm(formData);
      expect(errors.sort_order).toBe(
        `排序顺序必须在${CLASSIFICATION_VALIDATION.MIN_SORT_ORDER}-${CLASSIFICATION_VALIDATION.MAX_SORT_ORDER}之间`
      );
    });

    it('should pass validation for boundary sort_order values', () => {
      const minFormData = { 
        ...validFormData, 
        sort_order: CLASSIFICATION_VALIDATION.MIN_SORT_ORDER 
      };
      const maxFormData = { 
        ...validFormData, 
        sort_order: CLASSIFICATION_VALIDATION.MAX_SORT_ORDER 
      };

      const minErrors = validateClassificationForm(minFormData);
      const maxErrors = validateClassificationForm(maxFormData);

      expect(minErrors.sort_order).toBeUndefined();
      expect(maxErrors.sort_order).toBeUndefined();
    });

    it('should return multiple errors for multiple invalid fields', () => {
      const formData: AiClassificationFormData = {
        name: '',
        prompt_text: '',
        description: 'a'.repeat(CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH + 1),
        sort_order: -1,
        weight: 0,
      };

      const errors = validateClassificationForm(formData);

      expect(errors.name).toBe('分类名称不能为空');
      expect(errors.prompt_text).toBe('提示词不能为空');
      expect(errors.description).toBe(`描述不能超过${CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH}个字符`);
      expect(errors.sort_order).toBe(
        `排序顺序必须在${CLASSIFICATION_VALIDATION.MIN_SORT_ORDER}-${CLASSIFICATION_VALIDATION.MAX_SORT_ORDER}之间`
      );
    });
  });

  describe('hasFormErrors', () => {
    it('should return false for empty errors object', () => {
      expect(hasFormErrors({})).toBe(false);
    });

    it('should return true for errors object with errors', () => {
      expect(hasFormErrors({ name: '名称错误' })).toBe(true);
    });

    it('should return true for multiple errors', () => {
      expect(hasFormErrors({ 
        name: '名称错误', 
        prompt_text: '提示词错误' 
      })).toBe(true);
    });
  });

  describe('classificationToFormData', () => {
    it('should convert classification to form data correctly', () => {
      const formData = classificationToFormData(mockClassification);

      expect(formData).toEqual({
        name: mockClassification.name,
        prompt_text: mockClassification.prompt_text,
        description: mockClassification.description,
        sort_order: mockClassification.sort_order,
      });
    });

    it('should handle classification with undefined description', () => {
      const classificationWithoutDescription = {
        ...mockClassification,
        description: undefined,
      };

      const formData = classificationToFormData(classificationWithoutDescription);

      expect(formData.description).toBe('');
    });

    it('should handle classification with null description', () => {
      const classificationWithNullDescription = {
        ...mockClassification,
        description: null as any,
      };

      const formData = classificationToFormData(classificationWithNullDescription);

      expect(formData.description).toBe('');
    });
  });

  describe('formDataToCreateRequest', () => {
    it('should convert form data to create request correctly', () => {
      const request = formDataToCreateRequest(validFormData);

      expect(request).toEqual({
        name: validFormData.name.trim(),
        prompt_text: validFormData.prompt_text.trim(),
        description: validFormData.description.trim(),
        sort_order: validFormData.sort_order,
      });
    });

    it('should trim whitespace from string fields', () => {
      const formDataWithWhitespace = {
        ...validFormData,
        name: '  全身  ',
        prompt_text: '  提示词内容  ',
        description: '  描述内容  ',
      };

      const request = formDataToCreateRequest(formDataWithWhitespace);

      expect(request.name).toBe('全身');
      expect(request.prompt_text).toBe('提示词内容');
      expect(request.description).toBe('描述内容');
    });

    it('should set description to undefined for empty string', () => {
      const formDataWithEmptyDescription = {
        ...validFormData,
        description: '',
      };

      const request = formDataToCreateRequest(formDataWithEmptyDescription);

      expect(request.description).toBeUndefined();
    });

    it('should set description to undefined for whitespace-only string', () => {
      const formDataWithWhitespaceDescription = {
        ...validFormData,
        description: '   ',
      };

      const request = formDataToCreateRequest(formDataWithWhitespaceDescription);

      expect(request.description).toBeUndefined();
    });
  });

  describe('formDataToUpdateRequest', () => {
    it('should convert form data to update request correctly', () => {
      const request = formDataToUpdateRequest(validFormData);

      expect(request).toEqual({
        name: validFormData.name.trim(),
        prompt_text: validFormData.prompt_text.trim(),
        description: validFormData.description.trim(),
        sort_order: validFormData.sort_order,
      });
    });

    it('should trim whitespace from string fields', () => {
      const formDataWithWhitespace = {
        ...validFormData,
        name: '  全身  ',
        prompt_text: '  提示词内容  ',
        description: '  描述内容  ',
      };

      const request = formDataToUpdateRequest(formDataWithWhitespace);

      expect(request.name).toBe('全身');
      expect(request.prompt_text).toBe('提示词内容');
      expect(request.description).toBe('描述内容');
    });

    it('should set description to undefined for empty string', () => {
      const formDataWithEmptyDescription = {
        ...validFormData,
        description: '',
      };

      const request = formDataToUpdateRequest(formDataWithEmptyDescription);

      expect(request.description).toBeUndefined();
    });
  });

  describe('CLASSIFICATION_VALIDATION constants', () => {
    it('should have correct validation constants', () => {
      expect(CLASSIFICATION_VALIDATION.NAME_MAX_LENGTH).toBe(100);
      expect(CLASSIFICATION_VALIDATION.PROMPT_TEXT_MAX_LENGTH).toBe(1000);
      expect(CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH).toBe(500);
      expect(CLASSIFICATION_VALIDATION.MIN_SORT_ORDER).toBe(0);
      expect(CLASSIFICATION_VALIDATION.MAX_SORT_ORDER).toBe(9999);
    });
  });
});
