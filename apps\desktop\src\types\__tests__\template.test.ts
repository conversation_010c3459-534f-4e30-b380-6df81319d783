import { describe, it, expect } from 'vitest';
import { SegmentMatchingRuleHelper, SegmentMatchingRule } from '../template';

describe('SegmentMatchingRuleHelper', () => {
  describe('createFixedMaterial', () => {
    it('should create a fixed material rule', () => {
      const rule = SegmentMatchingRuleHelper.createFixedMaterial();
      expect(rule).toBe('FixedMaterial');
    });
  });

  describe('createAiClassification', () => {
    it('should create an AI classification rule', () => {
      const rule = SegmentMatchingRuleHelper.createAiClassification('test-id', 'Test Category');
      expect(rule).toEqual({
        AiClassification: {
          category_id: 'test-id',
          category_name: 'Test Category'
        }
      });
    });
  });

  describe('createRandomMatch', () => {
    it('should create a random match rule', () => {
      const rule = SegmentMatchingRuleHelper.createRandomMatch();
      expect(rule).toBe('RandomMatch');
    });
  });

  describe('getDisplayName', () => {
    it('should return correct display name for fixed material', () => {
      const rule = SegmentMatchingRuleHelper.createFixedMaterial();
      const displayName = SegmentMatchingRuleHelper.getDisplayName(rule);
      expect(displayName).toBe('固定素材');
    });

    it('should return correct display name for AI classification', () => {
      const rule = SegmentMatchingRuleHelper.createAiClassification('test-id', 'Test Category');
      const displayName = SegmentMatchingRuleHelper.getDisplayName(rule);
      expect(displayName).toBe('AI分类: Test Category');
    });

    it('should return correct display name for random match', () => {
      const rule = SegmentMatchingRuleHelper.createRandomMatch();
      const displayName = SegmentMatchingRuleHelper.getDisplayName(rule);
      expect(displayName).toBe('随机匹配');
    });

    it('should return unknown rule for invalid rule', () => {
      const rule = {} as SegmentMatchingRule;
      const displayName = SegmentMatchingRuleHelper.getDisplayName(rule);
      expect(displayName).toBe('未知规则');
    });
  });

  describe('isFixedMaterial', () => {
    it('should return true for fixed material rule', () => {
      const rule = SegmentMatchingRuleHelper.createFixedMaterial();
      expect(SegmentMatchingRuleHelper.isFixedMaterial(rule)).toBe(true);
    });

    it('should return false for AI classification rule', () => {
      const rule = SegmentMatchingRuleHelper.createAiClassification('test-id', 'Test Category');
      expect(SegmentMatchingRuleHelper.isFixedMaterial(rule)).toBe(false);
    });

    it('should return false for random match rule', () => {
      const rule = SegmentMatchingRuleHelper.createRandomMatch();
      expect(SegmentMatchingRuleHelper.isFixedMaterial(rule)).toBe(false);
    });
  });

  describe('isAiClassification', () => {
    it('should return false for fixed material rule', () => {
      const rule = SegmentMatchingRuleHelper.createFixedMaterial();
      expect(SegmentMatchingRuleHelper.isAiClassification(rule)).toBe(false);
    });

    it('should return true for AI classification rule', () => {
      const rule = SegmentMatchingRuleHelper.createAiClassification('test-id', 'Test Category');
      expect(SegmentMatchingRuleHelper.isAiClassification(rule)).toBe(true);
    });

    it('should return false for random match rule', () => {
      const rule = SegmentMatchingRuleHelper.createRandomMatch();
      expect(SegmentMatchingRuleHelper.isAiClassification(rule)).toBe(false);
    });
  });

  describe('isRandomMatch', () => {
    it('should return false for fixed material rule', () => {
      const rule = SegmentMatchingRuleHelper.createFixedMaterial();
      expect(SegmentMatchingRuleHelper.isRandomMatch(rule)).toBe(false);
    });

    it('should return false for AI classification rule', () => {
      const rule = SegmentMatchingRuleHelper.createAiClassification('test-id', 'Test Category');
      expect(SegmentMatchingRuleHelper.isRandomMatch(rule)).toBe(false);
    });

    it('should return true for random match rule', () => {
      const rule = SegmentMatchingRuleHelper.createRandomMatch();
      expect(SegmentMatchingRuleHelper.isRandomMatch(rule)).toBe(true);
    });
  });

  describe('getAiClassificationInfo', () => {
    it('should return null for fixed material rule', () => {
      const rule = SegmentMatchingRuleHelper.createFixedMaterial();
      const info = SegmentMatchingRuleHelper.getAiClassificationInfo(rule);
      expect(info).toBeNull();
    });

    it('should return classification info for AI classification rule', () => {
      const rule = SegmentMatchingRuleHelper.createAiClassification('test-id', 'Test Category');
      const info = SegmentMatchingRuleHelper.getAiClassificationInfo(rule);
      expect(info).toEqual({
        category_id: 'test-id',
        category_name: 'Test Category'
      });
    });

    it('should return null for random match rule', () => {
      const rule = SegmentMatchingRuleHelper.createRandomMatch();
      const info = SegmentMatchingRuleHelper.getAiClassificationInfo(rule);
      expect(info).toBeNull();
    });
  });
});
