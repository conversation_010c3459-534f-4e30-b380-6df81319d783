/**
 * AI分类相关类型定义
 * 遵循前端开发规范的类型安全设计
 */

/**
 * AI分类数据模型
 */
export interface AiClassification {
  /** 分类唯一标识符 */
  id: string;
  /** 分类名称 */
  name: string;
  /** 分类提示词 */
  prompt_text: string;
  /** 分类描述 */
  description?: string;
  /** 是否激活 */
  is_active: boolean;
  /** 排序顺序 */
  sort_order: number;
  /** 匹配权重（用于按顺序匹配，数值越大优先级越高） */
  weight: number;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

/**
 * 创建AI分类请求
 */
export interface CreateAiClassificationRequest {
  /** 分类名称 */
  name: string;
  /** 分类提示词 */
  prompt_text: string;
  /** 分类描述 */
  description?: string;
  /** 排序顺序 */
  sort_order?: number;
  /** 匹配权重 */
  weight?: number;
}

/**
 * 更新AI分类请求
 */
export interface UpdateAiClassificationRequest {
  /** 分类名称 */
  name?: string;
  /** 分类提示词 */
  prompt_text?: string;
  /** 分类描述 */
  description?: string;
  /** 是否激活 */
  is_active?: boolean;
  /** 排序顺序 */
  sort_order?: number;
  /** 匹配权重 */
  weight?: number;
}

/**
 * AI分类查询参数
 */
export interface AiClassificationQuery {
  /** 是否只查询激活的分类 */
  active_only?: boolean;
  /** 排序字段 */
  sort_by?: string;
  /** 排序方向 */
  sort_order?: string;
  /** 分页大小 */
  limit?: number;
  /** 分页偏移 */
  offset?: number;
}

/**
 * AI分类预览数据
 */
export interface AiClassificationPreview {
  /** 分类列表 */
  classifications: AiClassification[];
  /** 生成的完整提示词 */
  full_prompt: string;
}

/**
 * 分类表单数据
 */
export interface AiClassificationFormData {
  /** 分类名称 */
  name: string;
  /** 分类提示词 */
  prompt_text: string;
  /** 分类描述 */
  description: string;
  /** 排序顺序 */
  sort_order: number;
  /** 匹配权重 */
  weight: number;
}

/**
 * 分类表单验证错误
 */
export interface AiClassificationFormErrors {
  /** 名称错误 */
  name?: string;
  /** 提示词错误 */
  prompt_text?: string;
  /** 描述错误 */
  description?: string;
  /** 排序顺序错误 */
  sort_order?: string;
  /** 权重错误 */
  weight?: string;
  /** 通用错误 */
  general?: string;
}

/**
 * 分类操作状态
 */
export interface AiClassificationState {
  /** 分类列表 */
  classifications: AiClassification[];
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 当前编辑的分类 */
  editingClassification: AiClassification | null;
  /** 是否显示创建对话框 */
  showCreateDialog: boolean;
  /** 是否显示编辑对话框 */
  showEditDialog: boolean;
  /** 是否显示删除确认对话框 */
  showDeleteDialog: boolean;
  /** 待删除的分类ID */
  deletingClassificationId: string | null;
  /** 预览数据 */
  preview: AiClassificationPreview | null;
  /** 预览加载状态 */
  previewLoading: boolean;
}

/**
 * 分类操作类型
 */
export type AiClassificationAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CLASSIFICATIONS'; payload: AiClassification[] }
  | { type: 'ADD_CLASSIFICATION'; payload: AiClassification }
  | { type: 'UPDATE_CLASSIFICATION'; payload: AiClassification }
  | { type: 'DELETE_CLASSIFICATION'; payload: string }
  | { type: 'SET_EDITING_CLASSIFICATION'; payload: AiClassification | null }
  | { type: 'SET_SHOW_CREATE_DIALOG'; payload: boolean }
  | { type: 'SET_SHOW_EDIT_DIALOG'; payload: boolean }
  | { type: 'SET_SHOW_DELETE_DIALOG'; payload: boolean }
  | { type: 'SET_DELETING_CLASSIFICATION_ID'; payload: string | null }
  | { type: 'SET_PREVIEW'; payload: AiClassificationPreview | null }
  | { type: 'SET_PREVIEW_LOADING'; payload: boolean }
  | { type: 'RESET_STATE' };

/**
 * 分类排序更新项
 */
export interface SortOrderUpdate {
  /** 分类ID */
  id: string;
  /** 新的排序顺序 */
  sort_order: number;
}

/**
 * API响应包装器
 */
export interface ApiResponse<T> {
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 是否成功 */
  success: boolean;
}

/**
 * 分类验证规则
 */
export const CLASSIFICATION_VALIDATION = {
  /** 名称最大长度 */
  NAME_MAX_LENGTH: 100,
  /** 提示词最大长度 */
  PROMPT_TEXT_MAX_LENGTH: 1000,
  /** 描述最大长度 */
  DESCRIPTION_MAX_LENGTH: 500,
  /** 最小排序顺序 */
  MIN_SORT_ORDER: 0,
  /** 最大排序顺序 */
  MAX_SORT_ORDER: 9999,
} as const;

/**
 * 默认分类查询参数
 */
export const DEFAULT_CLASSIFICATION_QUERY: AiClassificationQuery = {
  active_only: true,
  sort_by: 'sort_order',
  sort_order: 'ASC',
};

/**
 * 默认表单数据
 */
export const DEFAULT_FORM_DATA: AiClassificationFormData = {
  name: '',
  prompt_text: '',
  description: '',
  sort_order: 0,
  weight: 0,
};

/**
 * 初始状态
 */
export const INITIAL_CLASSIFICATION_STATE: AiClassificationState = {
  classifications: [],
  loading: false,
  error: null,
  editingClassification: null,
  showCreateDialog: false,
  showEditDialog: false,
  showDeleteDialog: false,
  deletingClassificationId: null,
  preview: null,
  previewLoading: false,
};

/**
 * 分类表单验证函数
 */
export const validateClassificationForm = (data: AiClassificationFormData): AiClassificationFormErrors => {
  const errors: AiClassificationFormErrors = {};

  // 验证名称
  if (!data.name.trim()) {
    errors.name = '分类名称不能为空';
  } else if (data.name.length > CLASSIFICATION_VALIDATION.NAME_MAX_LENGTH) {
    errors.name = `分类名称不能超过${CLASSIFICATION_VALIDATION.NAME_MAX_LENGTH}个字符`;
  }

  // 验证提示词
  if (!data.prompt_text.trim()) {
    errors.prompt_text = '提示词不能为空';
  } else if (data.prompt_text.length > CLASSIFICATION_VALIDATION.PROMPT_TEXT_MAX_LENGTH) {
    errors.prompt_text = `提示词不能超过${CLASSIFICATION_VALIDATION.PROMPT_TEXT_MAX_LENGTH}个字符`;
  }

  // 验证描述
  if (data.description && data.description.length > CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH) {
    errors.description = `描述不能超过${CLASSIFICATION_VALIDATION.DESCRIPTION_MAX_LENGTH}个字符`;
  }

  // 验证排序顺序
  if (data.sort_order < CLASSIFICATION_VALIDATION.MIN_SORT_ORDER || 
      data.sort_order > CLASSIFICATION_VALIDATION.MAX_SORT_ORDER) {
    errors.sort_order = `排序顺序必须在${CLASSIFICATION_VALIDATION.MIN_SORT_ORDER}-${CLASSIFICATION_VALIDATION.MAX_SORT_ORDER}之间`;
  }

  return errors;
};

/**
 * 检查表单是否有错误
 */
export const hasFormErrors = (errors: AiClassificationFormErrors): boolean => {
  return Object.keys(errors).length > 0;
};

/**
 * 将分类转换为表单数据
 */
export const classificationToFormData = (classification: AiClassification): AiClassificationFormData => {
  return {
    name: classification.name,
    prompt_text: classification.prompt_text,
    description: classification.description || '',
    sort_order: classification.sort_order,
    weight: classification.weight || 0,
  };
};

/**
 * 将表单数据转换为创建请求
 */
export const formDataToCreateRequest = (data: AiClassificationFormData): CreateAiClassificationRequest => {
  return {
    name: data.name.trim(),
    prompt_text: data.prompt_text.trim(),
    description: data.description.trim() || undefined,
    sort_order: data.sort_order,
    weight: data.weight,
  };
};

/**
 * 将表单数据转换为更新请求
 */
export const formDataToUpdateRequest = (data: AiClassificationFormData): UpdateAiClassificationRequest => {
  return {
    name: data.name.trim(),
    prompt_text: data.prompt_text.trim(),
    description: data.description.trim() || undefined,
    sort_order: data.sort_order,
    weight: data.weight,
  };
};
