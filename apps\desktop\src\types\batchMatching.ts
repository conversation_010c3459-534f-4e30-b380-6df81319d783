/**
 * 一键匹配相关的类型定义
 * 遵循前端开发规范的类型设计原则
 */

// 一键匹配请求
export interface BatchMatchingRequest {
  project_id: string;
  overwrite_existing: boolean;
  result_name_prefix?: string; // 结果名称前缀，默认为"一键匹配"
}

// 一键匹配结果
export interface BatchMatchingResult {
  project_id: string;
  total_bindings: number;
  successful_matches: number;
  failed_matches: number;
  skipped_bindings: number;
  matching_results: BatchMatchingItemResult[];
  total_duration_ms: number;
  summary: BatchMatchingSummary;
  // 新增循环匹配相关字段
  total_rounds: number;           // 总循环轮数
  successful_rounds: number;      // 成功匹配的轮数
  termination_reason: string;     // 循环终止原因
  materials_exhausted: boolean;   // 是否因素材耗尽而终止
}

// 单个绑定的匹配结果
export interface BatchMatchingItemResult {
  binding_id: string;
  template_id: string;
  template_name: string;
  binding_name?: string;
  status: BatchMatchingItemStatus;
  matching_result?: any; // MaterialMatchingResult
  saved_result_id?: string;
  error_message?: string;
  duration_ms: number;
  // 新增循环匹配相关字段
  round_number: number;           // 匹配成功的轮次
  attempts_count: number;         // 尝试匹配的次数
  failure_reason?: string;        // 详细失败原因
}

// 单个绑定匹配状态
export enum BatchMatchingItemStatus {
  Success = 'Success',
  Failed = 'Failed',
  Skipped = 'Skipped',
}

// 一键匹配汇总信息
export interface BatchMatchingSummary {
  total_segments_matched: number;
  total_materials_used: number;
  total_models_used: number;
  average_success_rate: number;
  best_matching_template?: string;
  worst_matching_template?: string;
}

// 一键匹配进度状态
export enum BatchMatchingProgressStatus {
  NotStarted = 'NotStarted',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
}

// 一键匹配进度信息
export interface BatchMatchingProgress {
  status: BatchMatchingProgressStatus;
  current_binding_index: number;
  total_bindings: number;
  current_template_name?: string;
  completed_bindings: number;
  failed_bindings: number;
  elapsed_time_ms: number;
  estimated_remaining_ms?: number;
}

// 获取状态显示文本的辅助函数
export const getBatchMatchingStatusDisplay = (status: BatchMatchingItemStatus): string => {
  switch (status) {
    case BatchMatchingItemStatus.Success:
      return '成功';
    case BatchMatchingItemStatus.Failed:
      return '失败';
    case BatchMatchingItemStatus.Skipped:
      return '跳过';
    default:
      return '未知';
  }
};

// 获取状态颜色的辅助函数
export const getBatchMatchingStatusColor = (status: BatchMatchingItemStatus): string => {
  switch (status) {
    case BatchMatchingItemStatus.Success:
      return 'bg-green-100 text-green-800';
    case BatchMatchingItemStatus.Failed:
      return 'bg-red-100 text-red-800';
    case BatchMatchingItemStatus.Skipped:
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// 获取进度状态显示文本的辅助函数
export const getBatchMatchingProgressStatusDisplay = (status: BatchMatchingProgressStatus): string => {
  switch (status) {
    case BatchMatchingProgressStatus.NotStarted:
      return '未开始';
    case BatchMatchingProgressStatus.InProgress:
      return '进行中';
    case BatchMatchingProgressStatus.Completed:
      return '已完成';
    case BatchMatchingProgressStatus.Failed:
      return '失败';
    case BatchMatchingProgressStatus.Cancelled:
      return '已取消';
    default:
      return '未知';
  }
};

// 格式化时长显示
export const formatDuration = (durationMs: number): string => {
  if (durationMs < 1000) {
    return `${durationMs}ms`;
  } else if (durationMs < 60000) {
    return `${(durationMs / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
};

// 计算成功率百分比
export const calculateSuccessRate = (successful: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((successful / total) * 100);
};
