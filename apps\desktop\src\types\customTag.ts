/**
 * 自定义标签相关类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

export interface CustomTagCategory {
  /** 分类ID */
  id: string;
  /** 分类名称 */
  name: string;
  /** 分类描述 */
  description?: string;
  /** 分类颜色（十六进制） */
  color: string;
  /** 分类图标 */
  icon?: string;
  /** 排序顺序 */
  sort_order: number;
  /** 是否激活 */
  is_active: boolean;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

export interface CustomTag {
  /** 标签ID */
  id: string;
  /** 所属分类ID */
  category_id: string;
  /** 标签名称 */
  name: string;
  /** 标签描述 */
  description?: string;
  /** 标签颜色（可选，继承分类颜色） */
  color?: string;
  /** 排序顺序 */
  sort_order: number;
  /** 使用次数 */
  usage_count: number;
  /** 是否激活 */
  is_active: boolean;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

export interface TagAssociation {
  /** 关联ID */
  id: string;
  /** 标签ID */
  tag_id: string;
  /** 实体类型 */
  entity_type: string;
  /** 实体ID */
  entity_id: string;
  /** 创建时间 */
  created_at: string;
}

export interface CustomTagWithCategory {
  /** 标签信息 */
  tag: CustomTag;
  /** 分类信息 */
  category: CustomTagCategory;
}

export interface CreateCustomTagCategoryRequest {
  /** 分类名称 */
  name: string;
  /** 分类描述 */
  description?: string;
  /** 分类颜色 */
  color?: string;
  /** 分类图标 */
  icon?: string;
}

export interface UpdateCustomTagCategoryRequest {
  /** 分类名称 */
  name?: string;
  /** 分类描述 */
  description?: string;
  /** 分类颜色 */
  color?: string;
  /** 分类图标 */
  icon?: string;
  /** 排序顺序 */
  sort_order?: number;
  /** 是否激活 */
  is_active?: boolean;
}

export interface CreateCustomTagRequest {
  /** 分类ID */
  category_id: string;
  /** 标签名称 */
  name: string;
  /** 标签描述 */
  description?: string;
  /** 标签颜色 */
  color?: string;
}

export interface UpdateCustomTagRequest {
  /** 标签名称 */
  name?: string;
  /** 标签描述 */
  description?: string;
  /** 标签颜色 */
  color?: string;
  /** 排序顺序 */
  sort_order?: number;
  /** 是否激活 */
  is_active?: boolean;
}

export interface TagFilter {
  /** 分类ID列表 */
  category_ids?: string[];
  /** 标签名称搜索 */
  name_search?: string;
  /** 是否只显示激活的标签 */
  active_only?: boolean;
  /** 实体类型过滤 */
  entity_type?: string;
  /** 实体ID过滤 */
  entity_id?: string;
}

export interface TagStatistics {
  /** 总标签数 */
  total_tags: number;
  /** 激活标签数 */
  active_tags: number;
  /** 总分类数 */
  total_categories: number;
  /** 激活分类数 */
  active_categories: number;
  /** 总关联数 */
  total_associations: number;
  /** 按分类统计 */
  by_category: CategoryTagCount[];
}

export interface CategoryTagCount {
  /** 分类信息 */
  category: CustomTagCategory;
  /** 标签数量 */
  tag_count: number;
  /** 关联数量 */
  association_count: number;
}

export enum EntityType {
  Material = 'material',
  Model = 'model',
  Project = 'project',
  Template = 'template',
  MaterialSegment = 'material_segment',
}

export interface TagManagerProps {
  /** 实体类型 */
  entityType?: EntityType;
  /** 实体ID */
  entityId?: string;
  /** 是否显示统计信息 */
  showStatistics?: boolean;
  /** 是否允许创建新标签 */
  allowCreate?: boolean;
  /** 是否允许编辑标签 */
  allowEdit?: boolean;
  /** 是否允许删除标签 */
  allowDelete?: boolean;
  /** 标签变化回调 */
  onTagsChange?: (tags: CustomTagWithCategory[]) => void;
}

export interface TagSelectorProps {
  /** 已选择的标签ID列表 */
  selectedTagIds: string[];
  /** 标签选择变化回调 */
  onSelectionChange: (tagIds: string[]) => void;
  /** 是否允许多选 */
  multiple?: boolean;
  /** 是否显示分类筛选 */
  showCategoryFilter?: boolean;
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 是否允许创建新标签 */
  allowCreate?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否禁用 */
  disabled?: boolean;
}

export interface TagEditorProps {
  /** 标签ID（编辑模式） */
  tagId?: string;
  /** 分类ID（创建模式） */
  categoryId?: string;
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 保存成功回调 */
  onSave: (tag: CustomTag) => void;
}

export interface CategoryEditorProps {
  /** 分类ID（编辑模式） */
  categoryId?: string;
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 保存成功回调 */
  onSave: (category: CustomTagCategory) => void;
}

export interface TagDisplayProps {
  /** 标签信息 */
  tag: CustomTagWithCategory;
  /** 是否显示删除按钮 */
  showRemove?: boolean;
  /** 删除回调 */
  onRemove?: (tagId: string) => void;
  /** 点击回调 */
  onClick?: (tag: CustomTagWithCategory) => void;
  /** 大小 */
  size?: 'small' | 'medium' | 'large';
  /** 是否可点击 */
  clickable?: boolean;
}

export interface TagListProps {
  /** 标签列表 */
  tags: CustomTagWithCategory[];
  /** 是否显示分类分组 */
  groupByCategory?: boolean;
  /** 是否显示删除按钮 */
  showRemove?: boolean;
  /** 删除回调 */
  onRemove?: (tagId: string) => void;
  /** 标签点击回调 */
  onTagClick?: (tag: CustomTagWithCategory) => void;
  /** 是否显示空状态 */
  showEmpty?: boolean;
  /** 空状态文本 */
  emptyText?: string;
}

// 默认的标签分类颜色
export const DEFAULT_CATEGORY_COLORS = [
  '#3b82f6', // 蓝色
  '#ef4444', // 红色
  '#8b5cf6', // 紫色
  '#10b981', // 绿色
  '#f59e0b', // 橙色
  '#6366f1', // 靛蓝
  '#ec4899', // 粉色
  '#14b8a6', // 青色
  '#f97316', // 橙红
  '#84cc16', // 青绿
];

// 默认的标签分类图标
export const DEFAULT_CATEGORY_ICONS = [
  '👕', '🎨', '✨', '🌍', '🧵', '🏷️',
  '💎', '🎯', '🔥', '⭐', '🎪', '🎭',
];

// 实体类型显示名称映射
export const ENTITY_TYPE_NAMES: Record<EntityType, string> = {
  [EntityType.Material]: '素材',
  [EntityType.Model]: '模特',
  [EntityType.Project]: '项目',
  [EntityType.Template]: '模板',
  [EntityType.MaterialSegment]: '素材片段',
};
