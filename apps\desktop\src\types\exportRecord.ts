/**
 * 导出记录相关的类型定义
 * 遵循前端开发规范的类型设计原则
 */

// 导出类型枚举
export enum ExportType {
  JianYingV1 = 'JianYingV1',
  JianYingV2 = 'JianYingV2',
  Json = 'Json',
  Csv = 'Csv',
  Excel = 'Excel'
}

// 导出格式枚举
export enum ExportFormat {
  Json = 'Json',
  Csv = 'Csv',
  Excel = 'Excel',
  Other = 'Other'
}

// 导出状态枚举
export enum ExportStatus {
  Success = 'Success',
  Failed = 'Failed',
  InProgress = 'InProgress',
  Cancelled = 'Cancelled'
}

// 导出记录
export interface ExportRecord {
  id: string;
  matching_result_id: string;
  project_id: string;
  template_id: string;
  export_type: ExportType;
  export_format: ExportFormat;
  file_path: string;
  file_size?: number;
  export_status: ExportStatus;
  export_duration_ms: number;
  error_message?: string;
  metadata?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

// 创建导出记录请求
export interface CreateExportRecordRequest {
  matching_result_id: string;
  export_type: ExportType;
  export_format: ExportFormat;
  file_path: string;
  metadata?: string;
}

// 日期范围
export interface DateRange {
  start_date: string;
  end_date: string;
}

// 导出记录查询选项
export interface ExportRecordQueryOptions {
  project_id?: string;
  matching_result_id?: string;
  template_id?: string;
  export_type?: ExportType;
  export_status?: ExportStatus;
  limit?: number;
  offset?: number;
  search_keyword?: string;
  sort_by?: string; // "created_at", "export_duration_ms", "file_size"
  sort_order?: string; // "asc", "desc"
  date_range?: DateRange;
}

// 导出记录统计信息
export interface ExportRecordStatistics {
  total_exports: number;
  successful_exports: number;
  failed_exports: number;
  total_file_size: number;
  average_export_duration_ms: number;
  export_type_counts: Record<string, number>;
}

// 导出记录过滤选项
export interface ExportRecordFilterOptions {
  project_id?: string;
  template_id?: string;
  export_type?: ExportType;
  export_status?: ExportStatus;
  date_range?: DateRange;
}

// 导出记录排序选项
export interface ExportRecordSortOptions {
  field: 'created_at' | 'updated_at' | 'export_duration_ms' | 'file_size' | 'export_status';
  order: 'asc' | 'desc';
}

// 导出记录分页选项
export interface ExportRecordPaginationOptions {
  page: number;
  page_size: number;
  total?: number;
}

// 导出记录搜索选项
export interface ExportRecordSearchOptions {
  keyword?: string;
  filter?: ExportRecordFilterOptions;
  sort?: ExportRecordSortOptions;
  pagination?: ExportRecordPaginationOptions;
}

// 导出记录列表响应
export interface ExportRecordListResponse {
  records: ExportRecord[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// 导出记录详情响应
export interface ExportRecordDetailResponse {
  record: ExportRecord;
  matching_result?: {
    id: string;
    result_name: string;
    project_id: string;
    template_id: string;
  };
  project?: {
    id: string;
    name: string;
  };
  template?: {
    id: string;
    name: string;
  };
}

// 导出记录操作选项
export interface ExportRecordActionOptions {
  // 重新导出
  re_export?: {
    new_file_path: string;
  };
  // 验证文件
  validate_file?: boolean;
  // 删除记录
  delete?: boolean;
}

// 导出记录批量操作请求
export interface ExportRecordBatchActionRequest {
  record_ids: string[];
  action: 'delete' | 'validate' | 'cleanup';
  options?: {
    force?: boolean;
    backup?: boolean;
  };
}

// 导出记录批量操作响应
export interface ExportRecordBatchActionResponse {
  success_count: number;
  failed_count: number;
  errors: Array<{
    record_id: string;
    error: string;
  }>;
}

// 导出记录清理选项
export interface ExportRecordCleanupOptions {
  days_old: number;
  include_failed?: boolean;
  include_successful?: boolean;
  dry_run?: boolean;
}

// 导出记录清理响应
export interface ExportRecordCleanupResponse {
  cleaned_count: number;
  total_size_freed: number;
  records_cleaned: Array<{
    id: string;
    file_path: string;
    file_size?: number;
  }>;
}

// 导出记录表格列定义
export interface ExportRecordTableColumn {
  key: keyof ExportRecord | 'actions';
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: number;
  render?: (record: ExportRecord) => React.ReactNode;
}

// 导出记录表格配置
export interface ExportRecordTableConfig {
  columns: ExportRecordTableColumn[];
  pagination: ExportRecordPaginationOptions;
  sort: ExportRecordSortOptions;
  filter: ExportRecordFilterOptions;
  selection?: {
    enabled: boolean;
    selected_ids: string[];
  };
}

// 导出记录图表数据
export interface ExportRecordChartData {
  // 按日期统计
  daily_stats: Array<{
    date: string;
    total_exports: number;
    successful_exports: number;
    failed_exports: number;
  }>;
  // 按类型统计
  type_stats: Array<{
    export_type: ExportType;
    count: number;
    percentage: number;
  }>;
  // 按状态统计
  status_stats: Array<{
    export_status: ExportStatus;
    count: number;
    percentage: number;
  }>;
}

// 导出记录仪表板数据
export interface ExportRecordDashboardData {
  statistics: ExportRecordStatistics;
  recent_records: ExportRecord[];
  chart_data: ExportRecordChartData;
  trends: {
    export_count_trend: number; // 相比上周的变化百分比
    success_rate_trend: number;
    average_duration_trend: number;
  };
}

// 导出记录表单数据
export interface ExportRecordFormData {
  matching_result_id: string;
  export_type: ExportType;
  export_format: ExportFormat;
  file_path: string;
  metadata?: string;
}

// 导出记录验证规则
export interface ExportRecordValidationRules {
  matching_result_id: {
    required: boolean;
    message: string;
  };
  export_type: {
    required: boolean;
    message: string;
  };
  export_format: {
    required: boolean;
    message: string;
  };
  file_path: {
    required: boolean;
    pattern?: RegExp;
    message: string;
  };
}

// 导出记录工具函数类型
export type ExportRecordUtils = {
  formatFileSize: (bytes?: number) => string;
  formatDuration: (ms: number) => string;
  getStatusColor: (status: ExportStatus) => string;
  getTypeIcon: (type: ExportType) => string;
  validateFilePath: (path: string) => boolean;
  generateFileName: (record: ExportRecord) => string;
};
