/**
 * 图片生成相关类型定义
 * 遵循 Tauri 开发规范的类型系统设计
 */

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 图片质量选项
export enum ImageQuality {
  STANDARD = 'standard',
  HIGH = 'high',
  ULTRA = 'ultra'
}

// 艺术风格选项
export enum ArtStyle {
  NATURAL = 'natural',
  ARTISTIC = 'artistic',
  ANIME = 'anime',
  REALISTIC = 'realistic',
  ABSTRACT = 'abstract'
}

// 画面比例选项
export enum AspectRatio {
  SQUARE = '1:1',
  LANDSCAPE_4_3 = '4:3',
  PORTRAIT_3_4 = '3:4',
  WIDESCREEN = '16:9',
  MOBILE = '9:16'
}

// 提示词预审请求
export interface PromptCheckRequest {
  prompt: string;
}

// 提示词预审响应
export interface PromptCheckResponse {
  is_valid: boolean;
  message: string;
  suggestions?: string[];
}

// 图片生成请求
export interface ImageGenerationRequest {
  prompt: string;
  reference_image_path?: string;
  aspect_ratio?: string;
  quality?: string;
  style?: string;
}

// 图片生成响应
export interface ImageGenerationResponse {
  task_id: string;
  status: string;
  message: string;
}

// 任务状态查询响应
export interface TaskStatusResponse {
  task_id: string;
  status: TaskStatus;
  progress: number;
  result_url?: string;
  result_urls: string[];  // 新增：支持多张图片
  error_message?: string;
  created_at: string;
  updated_at: string;
}

// 文件上传响应
export interface FileUploadResponse {
  status: boolean;
  msg: string;
  data?: string;
}

// 图片生成配置
export interface ImageGenerationConfig {
  base_url: string;
  bearer_token: string;
  timeout: number;
  max_retries: number;
  retry_delay: number;
}

// 图片生成任务信息
export interface ImageGenerationTask {
  id: string;
  prompt: string;
  reference_image_path?: string;
  aspect_ratio: string;
  quality: string;
  style: string;
  status: TaskStatus;
  progress: number;
  result_url?: string;
  error_message?: string;
  created_at: Date;
  updated_at: Date;
}

// 图片生成历史记录
export interface ImageGenerationHistory {
  tasks: ImageGenerationTask[];
  total_count: number;
  page: number;
  page_size: number;
}

// 图片生成统计信息
export interface ImageGenerationStats {
  total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  pending_tasks: number;
  success_rate: number;
  average_generation_time: number;
}

// 图片生成记录状态
export enum ImageGenerationRecordStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 图片生成记录
export interface ImageGenerationRecord {
  id: string;
  task_id?: string;
  prompt: string;
  reference_image_path?: string;
  reference_image_url?: string;
  status: ImageGenerationRecordStatus;
  progress: number;
  result_urls: string[];
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
}

// 图片生成统计信息（后端返回）
export interface ImageGenerationStatistics {
  total_count: number;
  completed_count: number;
  failed_count: number;
  processing_count: number;
  pending_count: number;
  success_rate: number;
  average_duration_ms: number;
}

// 图片生成服务接口
export interface ImageGenerationService {
  checkPrompt(prompt: string): Promise<PromptCheckResponse>;
  submitGeneration(request: ImageGenerationRequest): Promise<ImageGenerationResponse>;
  queryTaskStatus(taskId: string): Promise<TaskStatusResponse>;
  uploadToCloud(filePath: string): Promise<FileUploadResponse>;
  cancelTask(taskId: string): Promise<void>;
  getTaskHistory(page?: number, pageSize?: number): Promise<ImageGenerationHistory>;
  getStats(): Promise<ImageGenerationStats>;
}

// 图片生成事件类型
export interface ImageGenerationEvents {
  onTaskSubmitted: (task: ImageGenerationTask) => void;
  onTaskUpdated: (task: ImageGenerationTask) => void;
  onTaskCompleted: (task: ImageGenerationTask) => void;
  onTaskFailed: (task: ImageGenerationTask, error: string) => void;
  onTaskCancelled: (taskId: string) => void;
}

// 图片生成错误类型
export class ImageGenerationError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ImageGenerationError';
  }
}

// 图片生成工具状态
export interface ImageGenerationToolState {
  // 输入状态
  prompt: string;
  referenceImagePath: string | null;
  aspectRatio: string;
  quality: string;
  style: string;
  
  // 预审状态
  promptCheckResult: PromptCheckResponse | null;
  isCheckingPrompt: boolean;
  
  // 生成状态
  currentTask: TaskStatusResponse | null;
  isGenerating: boolean;
  generationError: string | null;
  
  // 结果状态
  generatedImageUrl: string | null;
  uploadedImageUrl: string | null;
  
  // 历史记录
  taskHistory: ImageGenerationTask[];
  
  // 配置
  config: ImageGenerationConfig;
}

// 图片生成工具操作
export interface ImageGenerationToolActions {
  setPrompt: (prompt: string) => void;
  setReferenceImage: (path: string | null) => void;
  setAspectRatio: (ratio: string) => void;
  setQuality: (quality: string) => void;
  setStyle: (style: string) => void;
  
  checkPrompt: () => Promise<void>;
  submitGeneration: () => Promise<void>;
  cancelTask: () => void;
  resetAll: () => void;
  
  loadHistory: () => Promise<void>;
  clearHistory: () => void;
}

// 默认配置
export const DEFAULT_IMAGE_GENERATION_CONFIG: ImageGenerationConfig = {
  base_url: 'https://bowongai-test--text-video-agent-fastapi-app.modal.run',
  bearer_token: 'bowong7777',
  timeout: 120,
  max_retries: 3,
  retry_delay: 2
};

// 默认生成参数
export const DEFAULT_GENERATION_PARAMS = {
  aspect_ratio: AspectRatio.MOBILE,
  quality: ImageQuality.STANDARD,
  style: ArtStyle.NATURAL
};

// 支持的图片格式
export const SUPPORTED_IMAGE_FORMATS = [
  'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'
];

// 画面比例选项
export const ASPECT_RATIO_OPTIONS = [
  { value: AspectRatio.SQUARE, label: '正方形 (1:1)' },
  { value: AspectRatio.LANDSCAPE_4_3, label: '横向 (4:3)' },
  { value: AspectRatio.PORTRAIT_3_4, label: '竖向 (3:4)' },
  { value: AspectRatio.WIDESCREEN, label: '宽屏 (16:9)' },
  { value: AspectRatio.MOBILE, label: '手机屏 (9:16)' }
];

// 质量选项
export const QUALITY_OPTIONS = [
  { value: ImageQuality.STANDARD, label: '标准质量' },
  { value: ImageQuality.HIGH, label: '高质量' },
  { value: ImageQuality.ULTRA, label: '超高质量' }
];

// 风格选项
export const STYLE_OPTIONS = [
  { value: ArtStyle.NATURAL, label: '自然风格' },
  { value: ArtStyle.ARTISTIC, label: '艺术风格' },
  { value: ArtStyle.ANIME, label: '动漫风格' },
  { value: ArtStyle.REALISTIC, label: '写实风格' },
  { value: ArtStyle.ABSTRACT, label: '抽象风格' }
];
