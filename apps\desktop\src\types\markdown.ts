/**
 * Markdown解析相关的TypeScript类型定义
 */

/**
 * Markdown节点类型
 */
export enum MarkdownNodeType {
  Document = 'Document',
  Heading = 'Heading',
  Paragraph = 'Paragraph',
  List = 'List',
  ListItem = 'ListItem',
  CodeBlock = 'CodeBlock',
  InlineCode = 'InlineCode',
  Link = 'Link',
  Image = 'Image',
  Emphasis = 'Emphasis',
  Strong = 'Strong',
  Blockquote = 'Blockquote',
  HorizontalRule = 'HorizontalRule',
  Table = 'Table',
  TableRow = 'TableRow',
  TableCell = 'TableCell',
  Text = 'Text',
  LineBreak = 'LineBreak',
  Unknown = 'Unknown',
}

/**
 * 位置信息
 */
export interface Position {
  /** 行号（从0开始） */
  line: number;
  /** 列号（从0开始） */
  column: number;
  /** 字符偏移量（从0开始） */
  offset: number;
  /** 字节偏移量（从0开始） */
  byte_offset: number;
}

/**
 * 范围信息
 */
export interface Range {
  /** 开始位置 */
  start: Position;
  /** 结束位置 */
  end: Position;
}

/**
 * Markdown节点
 */
export interface MarkdownNode {
  /** 节点类型 */
  node_type: MarkdownNodeType;
  /** 节点内容（原始文本） */
  content: string;
  /** 位置范围 */
  range: Range;
  /** 子节点 */
  children: MarkdownNode[];
  /** 节点属性（如标题级别、链接URL等） */
  attributes: Record<string, string>;
}

/**
 * 解析统计信息
 */
export interface ParseStatistics {
  /** 总节点数 */
  total_nodes: number;
  /** 错误节点数 */
  error_nodes: number;
  /** 解析耗时（毫秒） */
  parse_time_ms: number;
  /** 文档长度 */
  document_length: number;
  /** 最大深度 */
  max_depth: number;
}

/**
 * Markdown解析结果
 */
export interface MarkdownParseResult {
  /** 根节点 */
  root: MarkdownNode;
  /** 解析统计信息 */
  statistics: ParseStatistics;
  /** 原始文本 */
  source_text: string;
}

/**
 * Markdown解析器配置
 */
export interface MarkdownParserConfig {
  /** 是否保留空白节点 */
  preserve_whitespace?: boolean;
  /** 是否解析内联HTML */
  parse_inline_html?: boolean;
  /** 最大解析深度 */
  max_depth?: number;
  /** 超时时间（毫秒） */
  timeout_ms?: number;
}

/**
 * 大纲项目
 */
export interface OutlineItem {
  /** 标题文本 */
  title: string;
  /** 标题级别（1-6） */
  level: number;
  /** 位置范围 */
  range: Range;
}

/**
 * 链接类型
 */
export enum LinkType {
  Link = 'Link',
  Image = 'Image',
}

/**
 * 链接信息
 */
export interface LinkInfo {
  /** 链接文本 */
  text: string;
  /** 链接URL */
  url: string;
  /** 链接标题 */
  title?: string;
  /** 位置范围 */
  range: Range;
  /** 链接类型 */
  link_type: LinkType;
}

/**
 * 验证问题类型
 */
export enum ValidationIssueType {
  SkippedHeadingLevel = 'SkippedHeadingLevel',
  EmptyLink = 'EmptyLink',
  RelativeLink = 'RelativeLink',
  InvalidSyntax = 'InvalidSyntax',
}

/**
 * 验证严重程度
 */
export enum ValidationSeverity {
  Error = 'Error',
  Warning = 'Warning',
  Info = 'Info',
}

/**
 * 验证问题
 */
export interface ValidationIssue {
  /** 问题类型 */
  issue_type: ValidationIssueType;
  /** 问题描述 */
  message: string;
  /** 位置范围 */
  range: Range;
  /** 严重程度 */
  severity: ValidationSeverity;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  /** 是否有效 */
  is_valid: boolean;
  /** 问题列表 */
  issues: ValidationIssue[];
  /** 解析统计 */
  statistics: ParseStatistics;
}

// === API请求和响应类型 ===

/**
 * 解析Markdown请求
 */
export interface ParseMarkdownRequest {
  /** 要解析的Markdown文本 */
  text: string;
  /** 解析器配置 */
  config?: MarkdownParserConfig;
}

/**
 * 解析Markdown响应
 */
export interface ParseMarkdownResponse {
  /** 是否成功 */
  success: boolean;
  /** 解析结果 */
  result?: MarkdownParseResult;
  /** 错误信息 */
  error?: string;
}

/**
 * 查询节点请求
 */
export interface QueryNodesRequest {
  /** Markdown文本 */
  text: string;
  /** 查询名称 */
  query_name: string;
}

/**
 * 查询节点响应
 */
export interface QueryNodesResponse {
  /** 是否成功 */
  success: boolean;
  /** 查询结果 */
  nodes?: MarkdownNode[];
  /** 错误信息 */
  error?: string;
}

/**
 * 位置查询请求
 */
export interface FindNodeAtPositionRequest {
  /** Markdown文本 */
  text: string;
  /** 行号（从0开始） */
  line: number;
  /** 列号（从0开始） */
  column: number;
}

/**
 * 位置查询响应
 */
export interface FindNodeAtPositionResponse {
  /** 是否成功 */
  success: boolean;
  /** 找到的节点 */
  node?: MarkdownNode;
  /** 错误信息 */
  error?: string;
}

/**
 * 大纲提取请求
 */
export interface ExtractOutlineRequest {
  /** Markdown文本 */
  text: string;
}

/**
 * 大纲提取响应
 */
export interface ExtractOutlineResponse {
  /** 是否成功 */
  success: boolean;
  /** 大纲项目 */
  outline?: OutlineItem[];
  /** 错误信息 */
  error?: string;
}

/**
 * 链接提取请求
 */
export interface ExtractLinksRequest {
  /** Markdown文本 */
  text: string;
}

/**
 * 链接提取响应
 */
export interface ExtractLinksResponse {
  /** 是否成功 */
  success: boolean;
  /** 链接信息 */
  links?: LinkInfo[];
  /** 错误信息 */
  error?: string;
}

/**
 * 验证请求
 */
export interface ValidateMarkdownRequest {
  /** Markdown文本 */
  text: string;
}

/**
 * 验证响应
 */
export interface ValidateMarkdownResponse {
  /** 是否成功 */
  success: boolean;
  /** 验证结果 */
  validation?: ValidationResult;
  /** 错误信息 */
  error?: string;
}

/**
 * 查询类型枚举
 */
export enum QueryType {
  Headings = 'headings',
  Links = 'links',
  Code = 'code',
}

/**
 * Markdown解析器选项
 */
export interface MarkdownParserOptions {
  /** 是否启用语法高亮 */
  enableSyntaxHighlight?: boolean;
  /** 是否启用实时解析 */
  enableRealTimeParsing?: boolean;
  /** 是否显示位置信息 */
  showPositionInfo?: boolean;
  /** 是否启用验证 */
  enableValidation?: boolean;
  /** 自定义查询 */
  customQueries?: Record<string, string>;
}
