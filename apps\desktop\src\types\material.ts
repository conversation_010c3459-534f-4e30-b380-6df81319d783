/**
 * 素材相关类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

export type MaterialType = 'Video' | 'Audio' | 'Image' | 'Document' | 'Other';

export type ProcessingStatus = 'Pending' | 'Processing' | 'Completed' | 'Failed' | 'Skipped';

export interface VideoMetadata {
  duration: number;
  width: number;
  height: number;
  fps: number;
  bitrate: number;
  codec: string;
  format: string;
  has_audio: boolean;
  audio_codec?: string;
  audio_bitrate?: number;
  audio_sample_rate?: number;
}

export interface AudioMetadata {
  duration: number;
  bitrate: number;
  codec: string;
  sample_rate: number;
  channels: number;
  format: string;
}

export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  color_space?: string;
  has_alpha: boolean;
  dpi?: number;
}

export type MaterialMetadata = 
  | { Video: VideoMetadata }
  | { Audio: AudioMetadata }
  | { Image: ImageMetadata }
  | 'None';

export interface SceneSegment {
  start_time: number;
  end_time: number;
  duration: number;
  scene_id: number;
  confidence: number;
}

export interface SceneDetection {
  scenes: SceneSegment[];
  total_scenes: number;
  detection_method: string;
  threshold: number;
}

export interface MaterialSegment {
  id: string;
  material_id: string;
  segment_index: number;
  start_time: number;
  end_time: number;
  duration: number;
  file_path: string;
  file_size: number;
  thumbnail_path?: string; // 缩略图路径
  usage_count: number;     // 使用次数
  is_used: boolean;        // 是否已使用
  last_used_at?: string;   // 最后使用时间
  created_at: string;
}

export interface Material {
  id: string;
  project_id: string;
  name: string;
  original_path: string;
  file_size: number;
  md5_hash: string;
  material_type: MaterialType;
  processing_status: ProcessingStatus;
  metadata: MaterialMetadata;
  scene_detection?: SceneDetection;
  segments: MaterialSegment[];
  model_id?: string; // 关联的模特ID
  thumbnail_path?: string; // 素材缩略图路径
  created_at: string;
  updated_at: string;
  processed_at?: string;
  error_message?: string;
}

export interface CreateMaterialRequest {
  project_id: string;
  file_paths: string[];
  auto_process: boolean;
  max_segment_duration?: number;
  model_id?: string; // 可选的模特绑定ID
  skip_start_ms?: number; // 跳过视频开头的毫秒数（用于AI生成视频避免相同首帧）
  max_concurrent_imports?: number; // 最大并发导入数量（批量处理优化）
}

export interface MaterialProcessingConfig {
  max_segment_duration: number;
  scene_detection_threshold: number;
  enable_scene_detection: boolean;
  video_quality: string;
  audio_quality: string;
  output_format: string;
  max_concurrent_imports?: number; // 最大并发导入数量（批量处理优化）
}

export interface MaterialImportResult {
  total_files: number;
  processed_files: number;
  skipped_files: number;
  failed_files: number;
  created_materials: Material[];
  errors: string[];
  processing_time: number;
}

export interface MaterialStats {
  total_materials: number;
  video_count: number;
  audio_count: number;
  image_count: number;
  other_count: number;
  total_size: number;
  total_duration: number;
  processing_status_counts: Record<string, number>;
}

// 批量删除相关类型
export interface BatchDeleteFailedItem {
  id: string;
  error_message: string;
}

export interface BatchDeleteResult {
  total_count: number;
  success_count: number;
  failed_count: number;
  successful_ids: string[];
  failed_items: BatchDeleteFailedItem[];
}

export interface FileInfo {
  name: string;
  path: string;
  size: number;
  extension: string;
  material_type: MaterialType;
  is_supported: boolean;
  modified: number;
}

/**
 * 素材导入相关的 Tauri 命令类型定义
 */
export interface MaterialCommands {
  // 素材管理命令
  import_materials(request: CreateMaterialRequest): Promise<MaterialImportResult>;
  get_project_materials(project_id: string): Promise<Material[]>;
  get_material_by_id(id: string): Promise<Material | null>;
  delete_material(id: string): Promise<void>;
  batch_delete_materials(material_ids: string[]): Promise<BatchDeleteResult>;
  get_project_material_stats(project_id: string): Promise<MaterialStats>;
  batch_process_materials(material_ids: string[]): Promise<string[]>;
  update_material_status(id: string, status: string, error_message?: string): Promise<void>;
  cleanup_invalid_materials(project_id: string): Promise<string>;
  
  // 文件操作命令
  is_supported_format(file_path: string): Promise<boolean>;
  get_supported_extensions(): Promise<string[]>;
  select_material_files(): Promise<string[]>;
  validate_material_files(file_paths: string[]): Promise<string[]>;
  get_file_info(file_path: string): Promise<FileInfo>;
  
  // FFmpeg 相关命令
  check_ffmpeg_available(): Promise<boolean>;
  get_ffmpeg_version(): Promise<string>;
  extract_file_metadata(file_path: string): Promise<MaterialMetadata>;
  detect_video_scenes(file_path: string, threshold: number): Promise<number[]>;
  generate_video_thumbnail(
    input_path: string,
    output_path: string,
    timestamp: number,
    width: number,
    height: number
  ): Promise<void>;

  // 音频文件访问命令
  get_audio_file_base64(materialId: string): Promise<string>;
}

/**
 * 素材导入进度状态
 */
export interface ImportProgress {
  current_file: string;
  processed_count: number;
  total_count: number;
  current_status: string;
  errors: string[];
}

/**
 * 素材卡片组件属性
 */
export interface MaterialCardProps {
  material: Material;
  onView: (material: Material) => void;
  onEdit: (material: Material) => void;
  onDelete: (id: string) => void;
  onProcess: (material: Material) => void;
}

/**
 * 素材导入对话框属性
 */
export interface MaterialImportDialogProps {
  isOpen: boolean;
  projectId: string;
  onClose: () => void;
  onImportComplete: (result: MaterialImportResult) => void;
}

/**
 * 素材列表组件属性
 */
export interface MaterialListProps {
  projectId: string;
  materials: Material[];
  isLoading: boolean;
  onRefresh: () => void;
  onImport: () => void;
}

/**
 * 素材统计组件属性
 */
export interface MaterialStatsProps {
  stats: MaterialStats;
  isLoading: boolean;
}

/**
 * 视频预览组件属性
 */
export interface VideoPreviewProps {
  material: Material;
  onSegmentSelect?: (segment: MaterialSegment) => void;
}

/**
 * 场景检测结果组件属性
 */
export interface SceneDetectionProps {
  sceneDetection: SceneDetection;
  onSceneSelect?: (scene: SceneSegment) => void;
}

/**
 * 素材处理配置组件属性
 */
export interface ProcessingConfigProps {
  config: MaterialProcessingConfig;
  onChange: (config: MaterialProcessingConfig) => void;
}

/**
 * 工具函数类型
 */
export interface MaterialUtils {
  formatFileSize(bytes: number): string;
  formatDuration(seconds: number): string;
  getStatusColor(status: ProcessingStatus): string;
  getTypeIcon(type: MaterialType): string;
  isVideoType(type: MaterialType): boolean;
  isAudioType(type: MaterialType): boolean;
  isImageType(type: MaterialType): boolean;
}
