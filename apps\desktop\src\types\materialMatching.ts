/**
 * 素材匹配相关的类型定义
 * 遵循前端开发规范的类型设计原则
 */

import { MaterialSegment } from './material';
import { SegmentMatchingRule } from './template';

// 素材匹配请求
export interface MaterialMatchingRequest {
  project_id: string;
  template_id: string;
  binding_id: string;
  overwrite_existing: boolean;
}

// 素材匹配结果
export interface MaterialMatchingResult {
  binding_id: string;
  template_id: string;
  project_id: string;
  matches: SegmentMatch[];
  statistics: MatchingStatistics;
  failed_segments: FailedSegmentMatch[];
}

// 片段匹配结果
export interface SegmentMatch {
  track_segment_id: string;
  track_segment_name: string;
  material_segment_id: string;
  material_segment: MaterialSegment;
  material_name: string;
  model_name?: string;
  match_score: number;
  match_reason: string;
}

// 匹配失败的片段
export interface FailedSegmentMatch {
  track_segment_id: string;
  track_segment_name: string;
  matching_rule: SegmentMatchingRule;
  failure_reason: string;
}

// 匹配统计信息
export interface MatchingStatistics {
  total_segments: number;
  matched_segments: number;
  failed_segments: number;
  success_rate: number;
  used_materials: number;
  used_models: number;
}

// 项目素材匹配统计信息
export interface ProjectMaterialMatchingStats {
  project_id: string;
  total_materials: number;
  total_segments: number;
  classified_segments: number;
  available_models: number;
  available_categories: string[];
  classification_rate: number;
}

// 模板绑定匹配验证结果
export interface TemplateBindingMatchingValidation {
  binding_id: string;
  is_valid: boolean;
  validation_errors: string[];
  total_segments: number;
  matchable_segments: number;
}

// 匹配状态枚举
export enum MatchingStatus {
  Idle = 'idle',
  Matching = 'matching',
  Completed = 'completed',
  Failed = 'failed'
}

// 匹配结果显示选项
export interface MatchingResultDisplayOptions {
  showSuccessOnly: boolean;
  showFailuresOnly: boolean;
  groupByModel: boolean;
  sortBy: 'score' | 'name' | 'duration';
  sortOrder: 'asc' | 'desc';
}

// 匹配配置选项
export interface MatchingOptions {
  overwrite_existing: boolean;
  prefer_higher_quality: boolean;
  strict_duration_matching: boolean;
  allow_cross_model_matching: boolean;
}

// 匹配进度信息
export interface MatchingProgress {
  current_segment: number;
  total_segments: number;
  current_segment_name: string;
  progress_percentage: number;
  estimated_remaining_time?: number;
}

// 匹配错误类型
export enum MatchingErrorType {
  NoClassifiedSegments = 'no_classified_segments',
  NoMatchingCategory = 'no_matching_category',
  InsufficientDuration = 'insufficient_duration',
  NoAvailableModels = 'no_available_models',
  TemplateNotFound = 'template_not_found',
  ProjectNotFound = 'project_not_found',
  InvalidMatchingRule = 'invalid_matching_rule'
}

// 匹配错误详情
export interface MatchingError {
  type: MatchingErrorType;
  message: string;
  segment_id?: string;
  segment_name?: string;
  details?: Record<string, any>;
}

// 匹配质量评分
export interface MatchingQualityScore {
  overall_score: number;
  duration_score: number;
  category_score: number;
  model_consistency_score: number;
  quality_factors: string[];
}

// 匹配建议
export interface MatchingSuggestion {
  type: 'improve_classification' | 'add_materials' | 'adjust_rules';
  message: string;
  action_required: boolean;
  estimated_improvement: number;
}

// 匹配报告
export interface MatchingReport {
  matching_result: MaterialMatchingResult;
  quality_score: MatchingQualityScore;
  suggestions: MatchingSuggestion[];
  performance_metrics: {
    matching_time_ms: number;
    segments_per_second: number;
    memory_usage_mb?: number;
  };
}
