/**
 * 素材检索相关类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

// 素材检索结果项
export interface MaterialSearchResult {
  /** 素材ID */
  id: string;
  /** 图片URL */
  image_url: string;
  /** 风格描述 */
  style_description: string;
  /** 环境标签 */
  environment_tags?: string[];
  /** 相关度评分 */
  relevance_score?: number;
  /** 素材类型 */
  material_type?: string;
  /** 色彩信息 */
  color_info?: {
    primary_colors: string[];
    color_scheme: string;
  };
  /** 风格标签 */
  style_tags?: string[];
  /** 场合标签 */
  occasion_tags?: string[];
}

// 素材检索响应
export interface MaterialSearchResponse {
  /** 检索结果列表 */
  results: MaterialSearchResult[];
  /** 总结果数量 */
  total_size: number;
  /** 当前页码 */
  current_page: number;
  /** 每页大小 */
  page_size: number;
  /** 总页数 */
  total_pages: number;
  /** 检索耗时(毫秒) */
  search_time_ms?: number;
  /** 检索时间 */
  searched_at: string;
  /** 下一页令牌 */
  next_page_token?: string;
}

// 素材检索请求
export interface MaterialSearchRequest {
  /** 检索查询 */
  query: string;
  /** 推荐方案ID */
  recommendation_id: string;
  /** 检索配置 */
  search_config: MaterialSearchConfig;
  /** 分页信息 */
  pagination: MaterialSearchPagination;
}

// 素材检索配置
export interface MaterialSearchConfig {
  /** 相关度阈值 */
  relevance_threshold: number;
  /** 最大结果数 */
  max_results: number;
  /** 是否包含风格过滤 */
  include_style_filter: boolean;
  /** 是否包含色彩过滤 */
  include_color_filter: boolean;
  /** 是否包含场合过滤 */
  include_occasion_filter: boolean;
}

// 分页信息
export interface MaterialSearchPagination {
  /** 页码 */
  page: number;
  /** 每页大小 */
  page_size: number;
}

// 生成检索查询请求
export interface GenerateSearchQueryRequest {
  /** 穿搭方案 */
  recommendation: any; // OutfitRecommendation类型
  /** 生成选项 */
  options?: GenerateSearchQueryOptions;
}

// 生成检索查询选项
export interface GenerateSearchQueryOptions {
  /** 是否包含风格 */
  include_styles?: boolean;
  /** 是否包含场合 */
  include_occasions?: boolean;
  /** 是否包含季节 */
  include_seasons?: boolean;
  /** 是否包含色彩 */
  include_colors?: boolean;
}

// 生成检索查询响应
export interface GenerateSearchQueryResponse {
  /** 生成的查询字符串 */
  query: string;
  /** 检索配置 */
  search_config: MaterialSearchConfig;
  /** 生成的关键词 */
  generated_keywords: string[];
  /** 生成耗时(毫秒) */
  generation_time_ms: number;
  /** 生成时间 */
  generated_at: string;
}

// 默认检索配置
export const DEFAULT_MATERIAL_SEARCH_CONFIG: MaterialSearchConfig = {
  relevance_threshold: 0.6,
  max_results: 50,
  include_style_filter: true,
  include_color_filter: true,
  include_occasion_filter: true,
};

// 默认生成选项
export const DEFAULT_GENERATE_OPTIONS: GenerateSearchQueryOptions = {
  include_styles: true,
  include_occasions: true,
  include_seasons: false,
  include_colors: true,
};
