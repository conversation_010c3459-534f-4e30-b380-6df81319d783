import { MaterialSegment } from './material';

/**
 * MaterialSegment聚合视图数据类型
 */
export interface MaterialSegmentView {
  project_id: string;
  by_classification: ClassificationGroup[];
  by_model: ModelGroup[];
  stats: MaterialSegmentStats;
}

/**
 * AI分类分组
 */
export interface ClassificationGroup {
  category: string;
  segment_count: number;
  total_duration: number;
  segments: SegmentWithDetails[];
}

/**
 * 模特分组
 */
export interface ModelGroup {
  model_id: string;
  model_name: string;
  segment_count: number;
  total_duration: number;
  segments: SegmentWithDetails[];
}

/**
 * 带详细信息的片段
 */
export interface SegmentWithDetails {
  segment: MaterialSegment;
  material_name: string;
  material_type: string;
  classification?: ClassificationInfo;
  model?: ModelInfo;
}

/**
 * 分类信息
 */
export interface ClassificationInfo {
  category: string;
  confidence: number;
  reasoning: string;
  features: string[];
  product_match: boolean;
  quality_score: number;
}

/**
 * 模特信息
 */
export interface ModelInfo {
  id: string;
  name: string;
  model_type: string;
}

/**
 * 片段统计信息
 */
export interface MaterialSegmentStats {
  total_segments: number;
  classified_segments: number;
  unclassified_segments: number;
  classification_coverage: number;
  classification_counts: Record<string, number>;
  model_counts: Record<string, number>;
  total_duration: number;
}

/**
 * 片段查询参数
 */
export interface MaterialSegmentQuery {
  project_id: string;
  category_filter?: string;
  model_id_filter?: string;
  min_duration?: number;
  max_duration?: number;
  search_term?: string;
  sort_by?: SegmentSortField;
  sort_direction?: SortDirection;
  page_size?: number;
  page?: number;
}

/**
 * 片段排序字段
 */
export enum SegmentSortField {
  CreatedAt = 'CreatedAt',
  Duration = 'Duration',
  Category = 'Category',
  Model = 'Model',
  Confidence = 'Confidence',
}

/**
 * 排序方向
 */
export enum SortDirection {
  Ascending = 'Ascending',
  Descending = 'Descending',
}

/**
 * 视图模式
 */
export enum MaterialSegmentViewMode {
  ByClassification = 'by_classification',
  ByModel = 'by_model',
}

/**
 * 片段操作类型
 */
export enum SegmentActionType {
  Reclassify = 'reclassify',
  EditModel = 'edit_model',
  Delete = 'delete',
  ViewDetails = 'view_details',
}

/**
 * 片段操作请求
 */
export interface SegmentActionRequest {
  segment_id: string;
  action_type: SegmentActionType;
  data?: any;
}
