/**
 * 素材使用状态管理相关类型定义
 * 对应后端的MaterialUsage数据模型
 */

export enum MaterialUsageType {
  TemplateMatching = 'TemplateMatching',
  ManualEdit = 'ManualEdit',
  Other = 'Other'
}

export interface MaterialUsageRecord {
  id: string;
  material_segment_id: string;
  material_id: string;
  project_id: string;
  template_matching_result_id: string;
  template_id: string;
  binding_id: string;
  track_segment_id: string;
  usage_type: MaterialUsageType;
  usage_context?: string;
  created_at: string;
}

export interface MaterialUsageStats {
  material_id: string;
  material_name: string;
  total_segments: number;
  used_segments: number;
  unused_segments: number;
  total_usage_count: number;
  last_used_at?: string;
  usage_rate: number; // 使用率 (used_segments / total_segments)
}

export interface ProjectMaterialUsageOverview {
  project_id: string;
  total_materials: number;
  total_segments: number;
  used_segments: number;
  unused_segments: number;
  total_usage_count: number;
  overall_usage_rate: number;
  materials_stats: MaterialUsageStats[];
}

export interface CreateMaterialUsageRecordRequest {
  material_segment_id: string;
  material_id: string;
  project_id: string;
  template_matching_result_id: string;
  template_id: string;
  binding_id: string;
  track_segment_id: string;
  usage_type: MaterialUsageType;
  usage_context?: string;
}

/**
 * 素材片段使用状态（扩展原有的MaterialSegment类型）
 */
export interface MaterialSegmentWithUsage {
  id: string;
  material_id: string;
  segment_index: number;
  start_time: number;
  end_time: number;
  duration: number;
  file_path: string;
  file_size: number;
  thumbnail_path?: string;
  usage_count: number;
  is_used: boolean;
  last_used_at?: string;
  created_at: string;
}

/**
 * 素材使用状态显示组件的Props
 */
export interface MaterialUsageStatusProps {
  segment: MaterialSegmentWithUsage;
  showDetails?: boolean;
  size?: 'small' | 'medium' | 'large';
}

/**
 * 素材使用统计卡片组件的Props
 */
export interface MaterialUsageStatsCardProps {
  stats: MaterialUsageStats;
  onViewDetails?: (materialId: string) => void;
  onResetUsage?: (materialId: string) => void;
}

/**
 * 项目素材使用概览组件的Props
 */
export interface ProjectUsageOverviewProps {
  overview: ProjectMaterialUsageOverview;
  onRefresh?: () => void;
  onResetAll?: () => void;
}

/**
 * 素材使用历史记录组件的Props
 */
export interface MaterialUsageHistoryProps {
  materialId?: string;
  projectId: string;
  records: MaterialUsageRecord[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

/**
 * 使用状态过滤选项
 */
export interface UsageStatusFilter {
  showAll: boolean;
  showUsed: boolean;
  showUnused: boolean;
  minUsageCount?: number;
  maxUsageCount?: number;
}

/**
 * 素材使用状态管理的Hook返回类型
 */
export interface UseMaterialUsageReturn {
  // 数据
  usageStats: MaterialUsageStats[];
  usageOverview: ProjectMaterialUsageOverview | null;
  usageRecords: MaterialUsageRecord[];
  
  // 状态
  isLoading: boolean;
  error: string | null;
  
  // 操作
  loadUsageStats: (projectId: string) => Promise<void>;
  loadUsageOverview: (projectId: string) => Promise<void>;
  loadUsageRecords: (projectId: string) => Promise<void>;
  createUsageRecord: (request: CreateMaterialUsageRecordRequest) => Promise<void>;
  resetSegmentUsage: (segmentIds: string[]) => Promise<void>;
  resetProjectUsage: (projectId: string) => Promise<void>;
  
  // 工具方法
  getSegmentUsageStatus: (segmentId: string) => 'unused' | 'used' | 'multiple';
  getMaterialUsageRate: (materialId: string) => number;
  clearError: () => void;
}

/**
 * 素材使用状态的颜色主题
 */
export interface UsageStatusTheme {
  unused: {
    bg: string;
    text: string;
    border: string;
    icon: string;
  };
  used: {
    bg: string;
    text: string;
    border: string;
    icon: string;
  };
  multiple: {
    bg: string;
    text: string;
    border: string;
    icon: string;
  };
}

/**
 * 默认的使用状态主题
 */
export const defaultUsageStatusTheme: UsageStatusTheme = {
  unused: {
    bg: 'bg-green-50',
    text: 'text-green-700',
    border: 'border-green-200',
    icon: 'text-green-500'
  },
  used: {
    bg: 'bg-gray-50',
    text: 'text-gray-700',
    border: 'border-gray-200',
    icon: 'text-gray-500'
  },
  multiple: {
    bg: 'bg-orange-50',
    text: 'text-orange-700',
    border: 'border-orange-200',
    icon: 'text-orange-500'
  }
};

/**
 * 使用状态标签的文本映射
 */
export const usageStatusLabels = {
  unused: '未使用',
  used: '已使用',
  multiple: '多次使用'
} as const;

/**
 * 使用类型的中文标签映射
 */
export const usageTypeLabels = {
  [MaterialUsageType.TemplateMatching]: '模板匹配',
  [MaterialUsageType.ManualEdit]: '手动编辑',
  [MaterialUsageType.Other]: '其他'
} as const;
