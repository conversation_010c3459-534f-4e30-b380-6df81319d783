// 模特管理相关类型定义

export interface Model {
  id: string;
  name: string;
  stage_name?: string;
  gender: Gender;
  age?: number;
  height?: number;
  weight?: number;
  measurements?: Measurements;
  description?: string;
  tags: string[];
  avatar_path?: string;
  photos: ModelPhoto[];
  contact_info?: ContactInfo;
  social_media?: SocialMedia;
  status: ModelStatus;
  rating?: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export enum Gender {
  Male = "Male",
  Female = "Female",
  Other = "Other"
}

export interface Measurements {
  bust: number;
  waist: number;
  hips: number;
}

export interface ModelPhoto {
  id: string;
  model_id: string;
  file_path: string;
  file_name: string;
  file_size: number;
  photo_type: PhotoType;
  description?: string;
  tags: string[];
  is_cover: boolean;
  created_at: string;
}

export enum PhotoType {
  Portrait = "Portrait",
  FullBody = "FullBody",
  Headshot = "Headshot",
  Artistic = "Artistic",
  Commercial = "Commercial",
  Casual = "Casual",
  Other = "Other"
}

export interface ContactInfo {
  phone?: string;
  email?: string;
  wechat?: string;
  qq?: string;
  address?: string;
}

export interface SocialMedia {
  weibo?: string;
  instagram?: string;
  tiktok?: string;
  xiaohongshu?: string;
  douyin?: string;
}

export enum ModelStatus {
  Active = "Active",
  Inactive = "Inactive",
  Retired = "Retired",
  Suspended = "Suspended"
}

export interface CreateModelRequest {
  name: string;
  stage_name?: string;
  gender: Gender;
  age?: number;
  height?: number;
  weight?: number;
  measurements?: Measurements;
  description?: string;
  tags?: string[];
  contact_info?: ContactInfo;
  social_media?: SocialMedia;
}

export interface UpdateModelRequest {
  name?: string;
  stage_name?: string;
  age?: number;
  height?: number;
  weight?: number;
  measurements?: Measurements;
  description?: string;
  tags?: string[];
  contact_info?: ContactInfo;
  social_media?: SocialMedia;
  status?: ModelStatus;
  rating?: number;
}

export interface ModelQueryParams {
  name?: string;
  gender?: Gender;
  min_age?: number;
  max_age?: number;
  min_height?: number;
  max_height?: number;
  tags?: string[];
  status?: ModelStatus;
  min_rating?: number;
  limit?: number;
  offset?: number;
}

export interface ModelStatistics {
  total_count: number;
  active_count: number;
  inactive_count: number;
  retired_count: number;
  suspended_count: number;
  male_count: number;
  female_count: number;
  other_count: number;
  average_rating?: number;
}

// 模特管理相关的API函数类型
export interface ModelAPI {
  createModel: (request: CreateModelRequest) => Promise<Model>;
  getModelById: (id: string) => Promise<Model | null>;
  getAllModels: () => Promise<Model[]>;
  searchModels: (params: ModelQueryParams) => Promise<Model[]>;
  updateModel: (id: string, request: UpdateModelRequest) => Promise<Model>;
  deleteModel: (id: string) => Promise<void>;
  deleteModelPermanently: (id: string) => Promise<void>;
  addModelPhoto: (
    modelId: string,
    filePath: string,
    photoType: PhotoType,
    description?: string,
    tags?: string[]
  ) => Promise<ModelPhoto>;
  deleteModelPhoto: (modelId: string, photoId: string) => Promise<void>;
  setCoverPhoto: (modelId: string, photoId: string) => Promise<void>;
  addModelTag: (modelId: string, tag: string) => Promise<void>;
  removeModelTag: (modelId: string, tag: string) => Promise<void>;
  updateModelStatus: (modelId: string, status: ModelStatus) => Promise<void>;
  setModelRating: (modelId: string, rating?: number) => Promise<void>;
  setModelAvatar: (modelId: string, avatarPath?: string) => Promise<void>;
  getModelStatistics: () => Promise<ModelStatistics>;
  selectPhotoFiles: () => Promise<string[]>;
  selectPhotoFile: () => Promise<string | null>;
}

// 表单验证相关
export interface ModelFormErrors {
  name?: string;
  stage_name?: string;
  age?: string;
  height?: string;
  weight?: string;
  measurements?: {
    bust?: string;
    waist?: string;
    hips?: string;
  };
  description?: string;
  contact_info?: {
    phone?: string;
    email?: string;
    wechat?: string;
    qq?: string;
    address?: string;
  };
  social_media?: {
    weibo?: string;
    instagram?: string;
    tiktok?: string;
    xiaohongshu?: string;
    douyin?: string;
  };
  rating?: string;
}

// 模特卡片显示模式
export enum ModelViewMode {
  Grid = "grid",
  List = "list",
  Card = "card"
}

// 模特排序选项
export enum ModelSortBy {
  Name = "name",
  CreatedAt = "created_at",
  UpdatedAt = "updated_at",
  Rating = "rating",
  Age = "age",
  Height = "height"
}

export enum SortOrder {
  Asc = "asc",
  Desc = "desc"
}

export interface ModelSortOptions {
  sortBy: ModelSortBy;
  order: SortOrder;
}

// 模特过滤选项
export interface ModelFilterOptions {
  gender?: Gender[];
  status?: ModelStatus[];
  ageRange?: [number, number];
  heightRange?: [number, number];
  ratingRange?: [number, number];
  tags?: string[];
  hasPhotos?: boolean;
  hasAvatar?: boolean;
}

// 模特搜索结果
export interface ModelSearchResult {
  models: Model[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// 模特照片上传进度
export interface PhotoUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

// 批量操作类型
export enum ModelBatchAction {
  Delete = "delete",
  UpdateStatus = "update_status",
  AddTag = "add_tag",
  RemoveTag = "remove_tag",
  Export = "export"
}

export interface ModelBatchOperation {
  action: ModelBatchAction;
  modelIds: string[];
  params?: any;
}

// 模特动态相关类型
export interface ModelDynamic {
  id: string;
  model_id: string;
  title?: string;
  description: string;
  prompt: string;
  source_image_path: string;
  ai_model: string; // 使用的AI模型，如"极梦"
  video_count: number; // 生成视频个数
  generated_videos: GeneratedVideo[];
  status: DynamicStatus;
  created_at: string;
  updated_at: string;
}

export interface GeneratedVideo {
  id: string;
  dynamic_id: string;
  video_path: string;
  thumbnail_path?: string;
  file_size: number;
  duration: number; // 视频时长（秒）
  status: VideoGenerationStatus;
  generation_progress?: number; // 生成进度 0-100
  error_message?: string;
  created_at: string;
}

export enum DynamicStatus {
  Draft = "Draft",
  Publishing = "Publishing",
  Published = "Published",
  Failed = "Failed"
}

export enum VideoGenerationStatus {
  Pending = "Pending",
  Generating = "Generating",
  Completed = "Completed",
  Failed = "Failed"
}

export interface CreateDynamicRequest {
  model_id: string;
  title?: string;
  description: string;
  prompt: string;
  source_image_path: string;
  ai_model: string;
  video_count: number;
}

export interface UpdateDynamicRequest {
  title?: string;
  description?: string;
  prompt?: string;
  status?: DynamicStatus;
}

// 模特动态统计
export interface ModelDynamicStats {
  total_dynamics: number;
  published_dynamics: number;
  total_videos: number;
  completed_videos: number;
  generating_videos: number;
  failed_videos: number;
}

// AI模型选项
export interface AIModelOption {
  id: string;
  name: string;
  description: string;
  is_available: boolean;
  max_video_count: number;
}
