/**
 * 穿搭方案收藏相关类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

import { OutfitRecommendation } from './outfitRecommendation';
import { MaterialSearchResponse } from './materialSearch';

// 收藏的穿搭方案
export interface OutfitFavorite {
  /** 收藏ID */
  id: string;
  /** 原始方案数据 */
  recommendation_data: OutfitRecommendation;
  /** 用户自定义名称 */
  custom_name?: string;
  /** 创建时间 */
  created_at: string;
}

// 保存方案到收藏请求
export interface SaveOutfitToFavoritesRequest {
  /** 要收藏的方案 */
  recommendation: OutfitRecommendation;
  /** 用户自定义名称 */
  custom_name?: string;
}

// 保存方案到收藏响应
export interface SaveOutfitToFavoritesResponse {
  /** 收藏ID */
  favorite_id: string;
  /** 收藏的方案 */
  favorite: OutfitFavorite;
}

// 获取收藏列表响应
export interface GetFavoriteOutfitsResponse {
  /** 收藏方案列表 */
  favorites: OutfitFavorite[];
  /** 总数量 */
  total_count: number;
}

// 基于收藏方案的素材检索请求
export interface SearchMaterialsByFavoriteRequest {
  /** 收藏方案ID */
  favorite_id: string;
  /** 分页信息 */
  page?: number;
  /** 每页大小 */
  page_size?: number;
}

// 方案对比请求
export interface CompareOutfitFavoritesRequest {
  /** 第一个收藏方案ID */
  favorite_id_1: string;
  /** 第二个收藏方案ID */
  favorite_id_2: string;
  /** 分页信息 */
  page?: number;
  /** 每页大小 */
  page_size?: number;
}

// 方案对比响应
export interface CompareOutfitFavoritesResponse {
  /** 第一个方案信息 */
  favorite_1: OutfitFavorite;
  /** 第一个方案的检索结果 */
  materials_1: MaterialSearchResponse;
  /** 第二个方案信息 */
  favorite_2: OutfitFavorite;
  /** 第二个方案的检索结果 */
  materials_2: MaterialSearchResponse;
  /** 对比生成时间 */
  compared_at: string;
}

// 收藏方案显示模式
export enum FavoriteViewMode {
  Grid = 'grid',
  List = 'list',
}

// 收藏方案排序选项
export interface FavoriteSortOption {
  value: string;
  label: string;
}

export const FAVORITE_SORT_OPTIONS: FavoriteSortOption[] = [
  { value: 'created_at_desc', label: '最新收藏' },
  { value: 'created_at_asc', label: '最早收藏' },
  { value: 'custom_name_asc', label: '名称 A-Z' },
  { value: 'custom_name_desc', label: '名称 Z-A' },
];

// 收藏操作状态
export interface FavoriteOperationState {
  /** 是否正在保存收藏 */
  isSaving: boolean;
  /** 是否正在加载收藏列表 */
  isLoading: boolean;
  /** 是否正在删除收藏 */
  isDeleting: boolean;
  /** 是否正在检索素材 */
  isSearching: boolean;
  /** 是否正在对比 */
  isComparing: boolean;
  /** 错误信息 */
  error?: string;
}
