/**
 * 穿搭方案推荐相关类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

// 分组策略
export interface GroupingStrategy {
  primary_dimension: string;
  reasoning: string;
}

// 方案质量评分
export interface OutfitQualityScore {
  ai_confidence_score: number;
  trend_score: number;
  versatility_score: number;
  difficulty_level: string;
  overall_recommendation_score: number;
}

// 穿搭方案分组
export interface OutfitRecommendationGroup {
  group: string;
  description: string;
  group_id: string;
  style_keywords: string[];
  can_load_more: boolean;
  children: OutfitRecommendation[];
}

// 色彩信息
export interface ColorInfo {
  /** 色彩名称 */
  name: string;
  /** 十六进制色值 */
  hex: string;
  /** HSV值 (0-1范围) */
  hsv: [number, number, number];
}

// 穿搭单品
export interface OutfitItem {
  /** 单品类别 (如: "上装", "下装", "鞋子", "配饰") */
  category: string;
  /** 单品描述 */
  description: string;
  /** 主要颜色 */
  primary_color: ColorInfo;
  /** 次要颜色 (可选) */
  secondary_color?: ColorInfo;
  /** 材质 */
  material: string;
  /** 风格标签 */
  style_tags: string[];
}

// 场景建议
export interface SceneRecommendation {
  /** 场景名称 */
  name: string;
  /** 场景描述 */
  description: string;
  /** 场景类型 ("室内", "室外", "特殊") */
  scene_type: string;
  /** 适合的时间段 */
  time_of_day: string[];
  /** 光线条件 */
  lighting: string;
  /** 拍摄建议 */
  photography_tips: string[];
}

// 穿搭方案
export interface OutfitRecommendation {
  /** 方案ID */
  id: string;
  /** 方案标题 */
  title: string;
  /** 方案描述 */
  description: string;
  /** 整体风格 */
  overall_style: string;
  /** 风格标签 */
  style_tags: string[];
  /** 适合场合 */
  occasions: string[];
  /** 适合季节 */
  seasons: string[];
  /** 穿搭单品列表 */
  items: OutfitItem[];
  /** 色彩搭配主题 */
  color_theme: string;
  /** 主要色彩 */
  primary_colors: ColorInfo[];
  /** 场景建议 */
  scene_recommendations: SceneRecommendation[];
  /** TikTok优化建议 */
  tiktok_tips: string[];
  /** 搭配要点 */
  styling_tips: string[];
  /** 创建时间 */
  created_at: string;
  /** 方案质量评分 */
  quality_score: OutfitQualityScore;
}

// 穿搭方案生成请求
export interface OutfitRecommendationRequest {
  /** 用户输入的关键词或描述 */
  query: string;
  /** 目标风格 (可选) */
  target_style?: string;
  /** 适合场合 (可选) */
  occasions?: string[];
  /** 季节偏好 (可选) */
  season?: string;
  /** 色彩偏好 (可选) */
  color_preferences?: string[];
  /** 生成数量 */
  count: number;
}

// 穿搭方案生成响应
export interface OutfitRecommendationResponse {
  /** 分组策略 */
  grouping_strategy: GroupingStrategy;
  /** 生成的穿搭方案分组列表 */
  groups: OutfitRecommendationGroup[];
  /** 生成时间 (毫秒) */
  generation_time_ms: number;
  /** 生成时间戳 */
  generated_at: string;
  /** 使用的提示词 (调试用) */
  prompt_used?: string;

  // 保持向后兼容性
  /** 生成的穿搭方案列表 (向后兼容) */
  recommendations?: OutfitRecommendation[];
}

// 场景检索请求 (用于方案详情到场景检索的集成)
export interface SceneSearchRequest {
  /** 穿搭方案ID */
  outfit_id: string;
  /** 场景描述 */
  scene_description: string;
  /** 风格标签 */
  style_tags: string[];
  /** 色彩信息 */
  colors: ColorInfo[];
  /** 搜索参数 */
  search_params: SceneSearchParams;
}

// 场景检索参数
export interface SceneSearchParams {
  /** 场景类型过滤 */
  scene_types?: string[];
  /** 时间段过滤 */
  time_filters?: string[];
  /** 光线条件过滤 */
  lighting_filters?: string[];
  /** 结果数量限制 */
  limit?: number;
}

// 穿搭方案卡片组件属性
export interface OutfitRecommendationCardProps {
  /** 穿搭方案数据 */
  recommendation: OutfitRecommendation;
  /** 点击事件处理 */
  onSelect?: (recommendation: OutfitRecommendation) => void;
  /** 场景检索事件处理 */
  onSceneSearch?: (recommendation: OutfitRecommendation) => void;
  /** 素材检索事件处理 */
  onMaterialSearch?: (recommendation: OutfitRecommendation) => void;
  /** 添加到对比事件处理 */
  onAddToComparison?: (recommendation: OutfitRecommendation) => void;
  /** 是否已选择用于对比 */
  isSelectedForComparison?: boolean;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 是否紧凑模式 */
  compact?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

// 穿搭方案列表组件属性
export interface OutfitRecommendationListProps {
  /** 穿搭方案分组列表 */
  groups?: OutfitRecommendationGroup[];
  /** 分组策略 */
  groupingStrategy?: GroupingStrategy;
  /** 穿搭方案列表 (向后兼容) */
  recommendations?: OutfitRecommendation[];
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 错误信息 */
  error?: string;
  /** 卡片选择事件 */
  onRecommendationSelect?: (recommendation: OutfitRecommendation) => void;
  /** 场景检索事件 */
  onSceneSearch?: (recommendation: OutfitRecommendation) => void;
  /** 素材检索事件 */
  onMaterialSearch?: (recommendation: OutfitRecommendation) => void;
  /** 重新生成事件 */
  onRegenerate?: () => void;
  /** 获取更多同类方案事件 */
  onLoadMoreForGroup?: (groupId: string, styleKeywords: string[]) => void;
  /** 添加到对比事件 */
  onAddToComparison?: (recommendation: OutfitRecommendation) => void;
  /** 已选择用于对比的方案 */
  selectedForComparison?: OutfitRecommendation[];
  /** 自定义样式类名 */
  className?: string;
}

// 穿搭方案生成状态
export interface OutfitRecommendationState {
  /** 当前查询 */
  query: string;
  /** 生成的方案列表 */
  recommendations: OutfitRecommendation[];
  /** 是否正在生成 */
  isGenerating: boolean;
  /** 错误信息 */
  error: string | null;
  /** 生成历史 */
  history: OutfitRecommendationResponse[];
}

// 默认请求参数
export const DEFAULT_OUTFIT_RECOMMENDATION_REQUEST: Partial<OutfitRecommendationRequest> = {
  count: 3,
  target_style: undefined,
  occasions: undefined,
  season: undefined,
  color_preferences: undefined,
};

// 常用风格选项
export const STYLE_OPTIONS = [
  '休闲',
  '正式',
  '运动',
  '街头',
  '简约',
  '复古',
  '甜美',
  '酷帅',
  '文艺',
  '优雅',
] as const;

// 常用场合选项
export const OCCASION_OPTIONS = [
  '日常',
  '工作',
  '约会',
  '聚会',
  '旅行',
  '运动',
  '正式场合',
  '休闲娱乐',
] as const;

// 季节选项
export const SEASON_OPTIONS = [
  '春季',
  '夏季',
  '秋季',
  '冬季',
] as const;

// 色彩偏好选项
export const COLOR_PREFERENCE_OPTIONS = [
  '暖色调',
  '冷色调',
  '中性色',
  '亮色',
  '深色',
  '浅色',
  '单色',
  '撞色',
] as const;

// ===== 素材库检索相关类型 =====

/**
 * 素材检索请求
 */
export interface MaterialSearchRequest {
  /** 基于穿搭方案生成的搜索查询 */
  query: string;
  /** 穿搭方案ID */
  recommendation_id: string;
  /** 搜索配置 */
  search_config: MaterialSearchConfig;
  /** 分页信息 */
  pagination: {
    page: number;
    page_size: number;
  };
}

/**
 * 素材检索配置
 */
export interface MaterialSearchConfig {
  /** 相关性阈值 */
  relevance_threshold: 'LOWEST' | 'LOW' | 'MEDIUM' | 'HIGH';
  /** 类别过滤 */
  categories: string[];
  /** 环境标签 */
  environments: string[];
  /** 颜色过滤器 */
  color_filters: Record<string, ColorFilter>;
  /** 设计风格 */
  design_styles: Record<string, string[]>;
  /** 最大结果数量 */
  max_results: number;
}

/**
 * 颜色过滤器
 */
export interface ColorFilter {
  enabled: boolean;
  color: {
    hue: number;
    saturation: number;
    value: number;
  };
  hue_threshold: number;
  saturation_threshold: number;
  value_threshold: number;
}

/**
 * 素材检索结果
 */
export interface MaterialSearchResult {
  /** 结果ID */
  id: string;
  /** 图片URL */
  image_url: string;
  /** 风格描述 */
  style_description: string;
  /** 环境标签 */
  environment_tags: string[];
  /** 产品信息 */
  products: MaterialProduct[];
  /** 相关性评分 */
  relevance_score: number;
  /** 创建时间 */
  created_at: string;
}

/**
 * 素材产品信息
 */
export interface MaterialProduct {
  /** 产品类别 */
  category: string;
  /** 产品描述 */
  description: string;
  /** 主要颜色 */
  color_pattern: {
    hue: number;
    saturation: number;
    value: number;
  };
  /** 设计风格 */
  design_styles: string[];
}

/**
 * 素材检索响应
 */
export interface MaterialSearchResponse {
  /** 搜索结果列表 */
  results: MaterialSearchResult[];
  /** 总结果数量 */
  total_size: number;
  /** 当前页码 */
  current_page: number;
  /** 每页大小 */
  page_size: number;
  /** 总页数 */
  total_pages: number;
  /** 搜索耗时（毫秒） */
  search_time_ms: number;
  /** 搜索时间戳 */
  searched_at: string;
  /** 下一页令牌 */
  next_page_token?: string;
}

/**
 * 智能检索条件生成请求
 */
export interface GenerateSearchQueryRequest {
  /** 穿搭方案 */
  recommendation: OutfitRecommendation;
  /** 生成选项 */
  options?: {
    /** 是否包含颜色信息 */
    include_colors?: boolean;
    /** 是否包含风格信息 */
    include_styles?: boolean;
    /** 是否包含场合信息 */
    include_occasions?: boolean;
    /** 是否包含季节信息 */
    include_seasons?: boolean;
  };
}

/**
 * 智能检索条件生成响应
 */
export interface GenerateSearchQueryResponse {
  /** 生成的搜索查询 */
  query: string;
  /** 生成的搜索配置 */
  search_config: MaterialSearchConfig;
  /** 生成时间（毫秒） */
  generation_time_ms: number;
  /** 生成时间戳 */
  generated_at: string;
}

// ===== 素材检索组件Props =====

/**
 * 素材检索面板组件Props
 */
export interface MaterialSearchPanelProps {
  /** 穿搭方案 */
  recommendation: OutfitRecommendation;
  /** 是否显示 */
  isVisible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 素材选择回调 */
  onMaterialSelect?: (material: MaterialSearchResult) => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 素材检索结果列表组件Props
 */
export interface MaterialSearchResultsProps {
  /** 搜索结果 */
  results: MaterialSearchResult[];
  /** 总结果数量 */
  totalSize: number;
  /** 当前页码 */
  currentPage: number;
  /** 每页大小 */
  pageSize: number;
  /** 是否加载中 */
  isLoading: boolean;
  /** 错误信息 */
  error?: string;
  /** 页面变化回调 */
  onPageChange: (page: number) => void;
  /** 素材选择回调 */
  onMaterialSelect?: (material: MaterialSearchResult) => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 素材卡片组件Props
 */
export interface MaterialCardProps {
  /** 素材结果 */
  material: MaterialSearchResult;
  /** 选择回调 */
  onSelect?: (material: MaterialSearchResult) => void;
  /** 是否显示评分 */
  showScore?: boolean;
  /** 是否紧凑模式 */
  compact?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 素材检索分页组件Props
 */
export interface MaterialSearchPaginationProps {
  /** 当前页码 */
  currentPage: number;
  /** 总页数 */
  totalPages: number;
  /** 每页大小 */
  pageSize: number;
  /** 总结果数量 */
  totalSize: number;
  /** 是否加载中 */
  isLoading?: boolean;
  /** 页面变化回调 */
  onPageChange: (page: number) => void;
  /** 每页大小变化回调 */
  onPageSizeChange?: (pageSize: number) => void;
  /** 自定义类名 */
  className?: string;
}

// 工具函数
export const OutfitRecommendationUtils = {
  /**
   * 生成用于场景检索的描述
   */
  generateSceneSearchDescription(recommendation: OutfitRecommendation): string {
    let description = `${recommendation.title} - ${recommendation.description}`;
    
    if (recommendation.style_tags.length > 0) {
      description += ` 风格: ${recommendation.style_tags.join(', ')}`;
    }
    
    if (recommendation.primary_colors.length > 0) {
      const colorNames = recommendation.primary_colors.map(c => c.name);
      description += ` 主要色彩: ${colorNames.join(', ')}`;
    }
    
    return description;
  },

  /**
   * 获取所有风格标签
   */
  getAllStyleTags(recommendation: OutfitRecommendation): string[] {
    const allTags = [...recommendation.style_tags];
    
    // 添加整体风格
    if (recommendation.overall_style) {
      allTags.push(recommendation.overall_style);
    }
    
    // 添加单品风格标签
    recommendation.items.forEach(item => {
      allTags.push(...item.style_tags);
    });
    
    // 去重并排序
    return [...new Set(allTags)].sort();
  },

  /**
   * 格式化色彩信息为显示文本
   */
  formatColorInfo(color: ColorInfo): string {
    return `${color.name} (${color.hex})`;
  },

  /**
   * 检查方案是否适合指定季节
   */
  isSeasonAppropriate(recommendation: OutfitRecommendation, season: string): boolean {
    return recommendation.seasons.includes(season);
  },

  /**
   * 检查方案是否适合指定场合
   */
  isOccasionAppropriate(recommendation: OutfitRecommendation, occasion: string): boolean {
    return recommendation.occasions.includes(occasion);
  },
};
