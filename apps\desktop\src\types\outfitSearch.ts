/**
 * 服装搭配搜索相关类型定义
 * 遵循 Tauri 开发规范的类型定义模式
 */

// HSV颜色模型
export interface ColorHSV {
  hue: number;        // 色相 (0-1)
  saturation: number; // 饱和度 (0-1)
  value: number;      // 明度 (0-1)
}

// 相关性阈值
export type RelevanceThreshold = 'LOWEST' | 'LOW' | 'MEDIUM' | 'HIGH';

// 颜色过滤器
export interface ColorFilter {
  enabled: boolean;
  color: ColorHSV;
  hue_threshold: number;
  saturation_threshold: number;
  value_threshold: number;
}

// 颜色阈值全局配置
export interface ColorThresholds {
  default_hue_threshold: number;
  default_saturation_threshold: number;
  default_value_threshold: number;
}

// 搜索配置 - 扩展以支持增强功能
export interface SearchConfig {
  relevance_threshold: RelevanceThreshold;
  environments: string[];
  categories: string[];
  color_filters: Record<string, ColorFilter>;
  design_styles: Record<string, string[]>;
  max_keywords: number;
  debug_mode: boolean;
  custom_filters: string[];
  query_enhancement_enabled: boolean;
  color_thresholds: ColorThresholds;
}

// 搜索请求
export interface SearchRequest {
  query: string;
  config: SearchConfig;
  page_size: number;
  page_offset: number;
}

// 产品信息
export interface ProductInfo {
  category: string;
  description: string;
  color_pattern: ColorHSV;
  design_styles: string[];
}

// 搜索结果项
export interface SearchResult {
  id: string;
  image_url: string;
  style_description: string;
  environment_tags: string[];
  products: ProductInfo[];
  relevance_score: number;
}

// 搜索响应
export interface SearchResponse {
  results: SearchResult[];
  total_size: number;
  next_page_token?: string;
  search_time_ms: number;
  searched_at: string;
}

// 产品分析结果
export interface ProductAnalysis {
  category: string;
  description: string;
  color_pattern: ColorHSV;
  design_styles: string[];
  color_pattern_match_dress: number;
  color_pattern_match_environment: number;
}

// Gemini分析结果
export interface OutfitAnalysisResult {
  environment_tags: string[];
  environment_color_pattern: ColorHSV;
  dress_color_pattern: ColorHSV;
  style_description: string;
  products: ProductAnalysis[];
}

// 图像分析请求
export interface AnalyzeImageRequest {
  image_path: string;
  image_name: string;
}

// 图像分析响应
export interface AnalyzeImageResponse {
  result: OutfitAnalysisResult;
  analysis_time_ms: number;
  analyzed_at: string;
}

// LLM问答请求
export interface LLMQueryRequest {
  user_input: string;
  session_id?: string;
}

// LLM问答响应
export interface LLMQueryResponse {
  answer: string;
  related_results: SearchResult[];
  response_time_ms: number;
  responded_at: string;
}

// 搜索历史
export interface SearchHistory {
  id: string;
  query: string;
  config: SearchConfig;
  results_count: number;
  search_time_ms: number;
  created_at: string;
}

// 全局配置信息
export interface OutfitSearchConfigInfo {
  google_project_id: string;
  vertex_ai_app_id: string;
  storage_bucket_name: string;
  data_store_id: string;
}

// 前端状态管理接口
export interface OutfitSearchState {
  // 搜索状态
  searchConfig: SearchConfig;
  searchResults: SearchResult[];
  isSearching: boolean;
  searchError: string | null;
  
  // 分析状态
  analysisResult: OutfitAnalysisResult | null;
  isAnalyzing: boolean;
  analysisError: string | null;
  
  // LLM问答状态
  llmResponse: LLMQueryResponse | null;
  isAsking: boolean;
  llmError: string | null;
  
  // UI状态
  selectedImage: string | null;
  showAdvancedFilters: boolean;
  currentPage: number;
  
  // 历史记录
  searchHistory: SearchHistory[];
  
  // 操作方法
  updateSearchConfig: (config: Partial<SearchConfig>) => void;
  executeSearch: (request: SearchRequest) => Promise<void>;
  analyzeImage: (request: AnalyzeImageRequest) => Promise<void>;
  askLLM: (request: LLMQueryRequest) => Promise<void>;
  clearResults: () => void;
  clearErrors: () => void;
  setSelectedImage: (imagePath: string | null) => void;
  toggleAdvancedFilters: () => void;
  loadSearchHistory: () => Promise<void>;
}

// 组件Props类型
export interface OutfitSearchPanelProps {
  config: SearchConfig;
  onConfigChange: (config: SearchConfig) => void;
  onSearch: (request: SearchRequest) => void;
  isLoading: boolean;
  analysisResult?: OutfitAnalysisResult | null;
  // 图片分析相关
  onImageSelect?: (imagePath: string | null) => void;
  onAnalyzeImage?: (imagePath: string) => void;
  selectedImage?: string | null;
  isAnalyzing?: boolean;
  analysisError?: string | null;
}

export interface ColorPickerProps {
  color: ColorHSV;
  onChange: (color: ColorHSV) => void;
  disabled?: boolean;
}

export interface FilterPanelProps {
  config: SearchConfig;
  onConfigChange: (config: SearchConfig) => void;
  showAdvanced: boolean;
  onToggleAdvanced: () => void;
  analysisResult?: OutfitAnalysisResult | null;
  // 图片分析相关
  onImageSelect?: (imagePath: string | null) => void;
  onAnalyzeImage?: (imagePath: string) => void;
  selectedImage?: string | null;
  isAnalyzing?: boolean;
  analysisError?: string | null;
}

export interface SearchResultsProps {
  results: SearchResult[];
  totalSize: number;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onItemSelect?: (result: SearchResult) => void;
  isLoading: boolean;
}

export interface OutfitCardProps {
  result: SearchResult;
  onSelect?: (result: SearchResult) => void;
  showScore?: boolean;
}

export interface ImageUploaderProps {
  onImageSelect: (imagePath: string | null) => void;
  onAnalysisComplete: (result: OutfitAnalysisResult) => void;
  onAnalyzeImage: (imagePath: string) => void;
  isAnalyzing: boolean;
  selectedImage: string | null;
  analysisError?: string | null;
}

export interface LLMChatProps {
  onAskLLM: (request: LLMQueryRequest) => void;
  onSearch?: (request: SearchRequest) => void;
  response: LLMQueryResponse | null;
  isLoading: boolean;
  error: string | null;
}

// 默认值和常量
export const DEFAULT_COLOR_THRESHOLDS: ColorThresholds = {
  default_hue_threshold: 0.05,
  default_saturation_threshold: 0.05,
  default_value_threshold: 0.20,
};

export const DEFAULT_SEARCH_CONFIG: SearchConfig = {
  relevance_threshold: 'HIGH',
  environments: [],
  categories: [],
  color_filters: {},
  design_styles: {},
  max_keywords: 10,
  debug_mode: false,
  custom_filters: [],
  query_enhancement_enabled: true,
  color_thresholds: DEFAULT_COLOR_THRESHOLDS,
};

export const DEFAULT_COLOR_FILTER: ColorFilter = {
  enabled: false,
  color: { hue: 0.0, saturation: 0.0, value: 0.0 },
  hue_threshold: 0.05,
  saturation_threshold: 0.05,
  value_threshold: 0.20,
};

export const RELEVANCE_THRESHOLD_OPTIONS = [
  { value: 'LOWEST' as const, label: '最低', description: '显示更多相关结果' },
  { value: 'LOW' as const, label: '低', description: '包含较多相关结果' },
  { value: 'MEDIUM' as const, label: '中等', description: '平衡相关性和数量' },
  { value: 'HIGH' as const, label: '高', description: '只显示高度相关结果' },
];

export const SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png', 'webp'];

export const COMMON_CATEGORIES = [
  '上装', '下装', '连衣裙', '外套', '鞋子', '配饰'
];

export const COMMON_ENVIRONMENTS = [
  'Outdoor', 'Indoor', 'City street', 'Building facade', 'Natural setting'
];

export const COMMON_DESIGN_STYLES = [
  '休闲', '正式', '运动', '街头', '简约', '复古', '时尚', '优雅'
];

// 工具函数类型
export interface ColorUtils {
  hsvToHex: (color: ColorHSV) => string;
  hexToHsv: (hex: string) => ColorHSV;
  rgbToHsv: (r: number, g: number, b: number) => ColorHSV;
  hsvToRgb: (color: ColorHSV) => [number, number, number];
  colorDistance: (color1: ColorHSV, color2: ColorHSV) => number;
  colorSimilarity: (color1: ColorHSV, color2: ColorHSV) => number;
}

// 错误类型
export interface OutfitSearchError {
  code: string;
  message: string;
  details?: any;
}

// 分页信息
export interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
