/**
 * 项目相关类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

export interface Project {
  id: string;
  name: string;
  path: string;
  description?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface CreateProjectRequest {
  name: string;
  path: string;
  description?: string;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
}

export interface AppInfo {
  name: string;
  version: string;
  platform: string;
}

/**
 * Tauri 命令类型定义
 * 确保前后端类型一致性
 */
export interface TauriCommands {
  // 项目管理命令
  create_project(request: CreateProjectRequest): Promise<Project>;
  get_all_projects(): Promise<Project[]>;
  get_project_by_id(id: string): Promise<Project | null>;
  update_project(id: string, request: UpdateProjectRequest): Promise<Project>;
  delete_project(id: string): Promise<void>;
  validate_project_path(path: string): Promise<boolean>;
  get_default_project_name(path: string): Promise<string>;
  
  // 系统命令
  select_directory(): Promise<string | null>;
  get_app_info(): Promise<AppInfo>;
  validate_directory(path: string): Promise<boolean>;
  get_directory_name(path: string): Promise<string>;
}

/**
 * 应用状态类型定义
 */
export interface AppState {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
}

export interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  currentView: string;
}

/**
 * 表单状态类型
 */
export interface ProjectFormData {
  name: string;
  path: string;
  description: string;
}

export interface ProjectFormErrors {
  name?: string;
  path?: string;
  description?: string;
}

/**
 * 组件 Props 类型
 */
export interface ProjectCardProps {
  project: Project;
  onEdit: (project: Project) => void;
  onDelete: (id: string) => void;
  onOpen: (project: Project) => void;
}

export interface ProjectFormProps {
  initialData?: Partial<ProjectFormData>;
  onSubmit: (data: ProjectFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export interface ProjectListProps {
  projects: Project[];
  onProjectSelect: (project: Project) => void;
  onProjectEdit: (project: Project) => void;
  onProjectDelete: (id: string) => void;
}
