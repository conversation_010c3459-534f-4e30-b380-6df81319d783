/**
 * RAG Grounding 相关类型定义
 * 基于 Rust 后端的数据结构，提供类型安全的前端接口
 */

/**
 * RAG Grounding 配置
 */
export interface RagGroundingConfig {
  project_id: string;
  location: string;
  data_store_id: string;
  model_id: string;
  temperature: number;
  max_output_tokens: number;
  system_prompt?: string;
  /** 最大检索结果数量 (默认5，最大50) */
  max_retrieval_results?: number;
  /** 相关性阈值 (0.0-1.0，越低检索越多结果) */
  relevance_threshold?: number;
  /** 搜索过滤器 */
  search_filter?: string;
  /** 是否包含摘要 */
  include_summary?: boolean;
}

/**
 * RAG Grounding 请求
 */
export interface RagGroundingRequest {
  user_input: string;
  config?: RagGroundingConfig;
  session_id?: string;
  include_history?: boolean;
  max_history_messages?: number;
  system_prompt?: string;
}

/**
 * RAG Grounding 响应
 */
export interface RagGroundingResponse {
  answer: string;
  grounding_metadata?: GroundingMetadata;
  response_time_ms: number;
  model_used: string;
  session_id?: string;
  message_id?: string;
  conversation_context?: ConversationContext;
}

/**
 * Grounding 元数据
 */
export interface GroundingMetadata {
  sources: GroundingSource[];
  search_queries: string[];
  grounding_supports?: GroundingSupport[];
}

/**
 * Grounding 来源
 */
export interface GroundingSource {
  title: string;
  uri?: string;
  content?: any; // JSON 内容
}

/**
 * Grounding 支持信息
 * 用于关联文字片段与来源信息
 */
export interface GroundingSupport {
  /** 关联的grounding chunk索引数组 */
  groundingChunkIndices: number[];
  /** 文字片段信息 */
  segment: GroundingSegment;
}

/**
 * Grounding 文字片段
 */
export interface GroundingSegment {
  /** 片段开始位置 */
  startIndex: number;
  /** 片段结束位置 */
  endIndex: number;
  /** 片段文字内容 */
  text: string;
}

/**
 * 对话上下文
 */
export interface ConversationContext {
  total_messages: number;
  history_included: boolean;
  context_length: number;
}

/**
 * RAG Grounding 配置信息
 */
export interface RagGroundingConfigInfo {
  base_url: string;
  model_name: string;
  timeout: number;
  max_retries: number;
  retry_delay: number;
  temperature: number;
  max_tokens: number;
  cloudflare_project_id: string;
  cloudflare_gateway_id: string;
  google_project_id: string;
  regions: string[];
}

/**
 * RAG Grounding 默认配置
 */
export const DEFAULT_RAG_GROUNDING_CONFIG: RagGroundingConfig = {
  project_id: "gen-lang-client-0413414134",
  location: "global",
  data_store_id: "searchable-model-images_1752827560253", // 使用存在的数据存储
  model_id: "gemini-2.5-flash",
  temperature: 1.0,
  max_output_tokens: 8192,
  system_prompt: "你是一个短视频情景穿搭分析专家, 根据用户的输入检索RAG，然后参考检索结果，输出符合逻辑的情景和模特穿搭描述，必须依据已知的数据返回可能的方案, 并且给出参照的依据；如果没有匹配的数据支持，返回空结果;",
  // 优化检索参数以获取更多相关数据
  max_retrieval_results: 20, // 增加检索结果数量
  relevance_threshold: 0.3, // 降低相关性阈值
  search_filter: undefined, // 暂不使用过滤器
  include_summary: true, // 包含摘要信息
};

/**
 * RAG Grounding 查询选项
 */
export interface RagGroundingQueryOptions {
  /** 是否包含 grounding 元数据 */
  includeMetadata?: boolean;
  /** 会话ID，用于上下文保持 */
  sessionId?: string;
  /** 自定义配置 */
  customConfig?: Partial<RagGroundingConfig>;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 是否包含历史消息 */
  includeHistory?: boolean;
  /** 最大历史消息数 */
  maxHistoryMessages?: number;
  /** 系统提示词 */
  systemPrompt?: string;
}

/**
 * RAG Grounding 查询结果
 */
export interface RagGroundingQueryResult {
  /** 查询是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: RagGroundingResponse;
  /** 错误信息 */
  error?: string;
  /** 查询开始时间 */
  startTime: Date;
  /** 查询结束时间 */
  endTime: Date;
  /** 总耗时（毫秒） */
  totalTime: number;
}

/**
 * RAG Grounding 服务状态
 */
export interface RagGroundingServiceStatus {
  /** 服务是否可用 */
  available: boolean;
  /** 最后检查时间 */
  lastChecked: Date;
  /** 连接测试结果 */
  connectionTest?: string;
  /** 配置信息 */
  config?: RagGroundingConfigInfo;
}

/**
 * RAG Grounding 错误类型
 */
export enum RagGroundingErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * RAG Grounding 错误
 */
export interface RagGroundingError {
  type: RagGroundingErrorType;
  message: string;
  details?: any;
  timestamp: Date;
}

/**
 * RAG Grounding 统计信息
 */
export interface RagGroundingStats {
  /** 总查询次数 */
  totalQueries: number;
  /** 成功查询次数 */
  successfulQueries: number;
  /** 失败查询次数 */
  failedQueries: number;
  /** 平均响应时间（毫秒） */
  averageResponseTime: number;
  /** 最快响应时间（毫秒） */
  fastestResponseTime: number;
  /** 最慢响应时间（毫秒） */
  slowestResponseTime: number;
  /** 最后查询时间 */
  lastQueryTime?: Date;
}
