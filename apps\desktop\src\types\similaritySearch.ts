/**
 * 相似度检索工具类型定义
 * 遵循 Tauri 开发规范的类型定义模式
 */

// 重用现有的搜索结果类型
import type { SearchResult, SearchResponse, SearchConfig } from './outfitSearch';
export type { SearchResult, SearchResponse, SearchConfig };

// 阈值选项
export interface ThresholdOption {
  value: string;
  label: string;
  description: string;
}

// 相似度检索工具配置
export interface SimilaritySearchConfig {
  available_thresholds: ThresholdOption[];
  default_threshold: string;
  max_results_per_page: number;
  quick_search_tags: string[];
}

// 搜索请求（扩展版，支持高级过滤器）
export interface SimilaritySearchRequest {
  query: string;
  relevance_threshold?: string;
  page_size?: number;
  page_offset?: number;
  config?: SearchConfig; // 添加高级过滤器配置支持
}

// 搜索状态
export interface SimilaritySearchState {
  // 搜索状态
  query: string;
  selectedThreshold: string;
  searchResults: SearchResult[];
  isSearching: boolean;
  searchError: string | null;
  
  // 配置
  config: SimilaritySearchConfig | null;
  isLoadingConfig: boolean;
  
  // 建议
  suggestions: string[];
  showSuggestions: boolean;
  
  // 分页
  currentPage: number;
  totalResults: number;
  
  // 操作方法
  setQuery: (query: string) => void;
  setThreshold: (threshold: string) => void;
  executeSearch: (request: SimilaritySearchRequest) => Promise<void>;
  searchWithPagination: (page: number) => Promise<void>;
  changePageSize: (pageSize: number) => Promise<void>;
  loadConfig: () => Promise<void>;
  loadSuggestions: (query: string) => Promise<void>;
  clearResults: () => void;
  clearError: () => void;
  setShowSuggestions: (show: boolean) => void;
}

// 组件属性接口
export interface SimilaritySearchPanelProps {
  query: string;
  selectedThreshold: string;
  config?: SimilaritySearchConfig | null; // 现在可选，因为阈值选择器已移到顶部
  suggestions: string[];
  showSuggestions: boolean;
  isSearching: boolean;
  onQueryChange: (query: string) => void;
  onThresholdChange?: (threshold: string) => void; // 现在可选，因为阈值选择器已移到顶部
  onSearch: (request: SimilaritySearchRequest) => void;
  onSuggestionSelect: (suggestion: string) => void;
  onSuggestionsToggle: (show: boolean) => void;
  onOutfitRecommendation?: () => void; // 穿搭方案生成回调
  // 高级过滤器控制
  showAdvancedFilters?: boolean;
  onToggleAdvancedFilters?: () => void;
}

export interface SimilaritySearchResultsProps {
  results: SearchResult[];
  totalResults: number;
  currentPage: number;
  maxResultsPerPage: number;
  isLoading: boolean;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onResultSelect?: (result: SearchResult) => void;
  onExternalLinkClick?: (url: string) => void;
}

export interface SimilaritySearchCardProps {
  result: SearchResult;
  onSelect?: (result: SearchResult) => void;
  onExternalLinkClick?: (url: string) => void;
  showScore?: boolean;
  compact?: boolean;
}

// 默认配置
export const DEFAULT_SIMILARITY_SEARCH_CONFIG: Partial<SimilaritySearchConfig> = {
  default_threshold: 'MEDIUM',
  max_results_per_page: 12,
  quick_search_tags: [
    '休闲',
    '正式', 
    '运动',
    '街头',
    '简约',
    '复古'
  ],
};

// 常用搜索关键词
export const COMMON_SEARCH_KEYWORDS = [
  '休闲搭配',
  '正式搭配',
  '运动风格',
  '街头风格',
  '简约风格',
  '复古风格',
  '牛仔裤搭配',
  '连衣裙搭配',
  '外套搭配',
  '夏季搭配',
  '冬季搭配',
  '约会搭配',
  '工作搭配',
  '聚会搭配',
];

// 工具函数类型
export interface SimilaritySearchUtils {
  formatThresholdLabel: (threshold: string) => string;
  getThresholdDescription: (threshold: string) => string;
  validateSearchQuery: (query: string) => boolean;
  generateSearchSuggestions: (query: string, baseList: string[]) => string[];
}
