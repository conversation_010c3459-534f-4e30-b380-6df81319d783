/**
 * 模板匹配结果相关的类型定义
 * 遵循前端开发规范的类型设计原则
 */


// 匹配结果状态枚举
export enum MatchingResultStatus {
  Success = 'Success',
  PartialSuccess = 'PartialSuccess',
  Failed = 'Failed',
  Cancelled = 'Cancelled'
}

// 模板匹配结果
export interface TemplateMatchingResult {
  id: string;
  project_id: string;
  template_id: string;
  binding_id: string;
  result_name: string;
  description?: string;
  total_segments: number;
  matched_segments: number;
  failed_segments: number;
  success_rate: number;
  used_materials: number;
  used_models: number;
  matching_duration_ms: number;
  quality_score?: number;
  status: MatchingResultStatus;
  metadata?: string;
  export_count: number;
  is_exported: boolean;
  last_exported_at?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

// 匹配片段结果
export interface MatchingSegmentResult {
  id: string;
  matching_result_id: string;
  track_segment_id: string;
  track_segment_name: string;
  material_segment_id: string;
  material_id: string;
  material_name: string;
  model_id?: string;
  model_name?: string;
  match_score: number;
  match_reason: string;
  segment_duration: number;
  start_time: number;
  end_time: number;
  properties?: string;
  created_at: string;
  updated_at: string;
}

// 匹配失败片段结果
export interface MatchingFailedSegmentResult {
  id: string;
  matching_result_id: string;
  track_segment_id: string;
  track_segment_name: string;
  matching_rule_type: string;
  matching_rule_data?: string;
  failure_reason: string;
  failure_details?: string;
  segment_duration: number;
  start_time: number;
  end_time: number;
  created_at: string;
  updated_at: string;
}

// 模板匹配结果详情
export interface TemplateMatchingResultDetail {
  matching_result: TemplateMatchingResult;
  segment_results: MatchingSegmentResult[];
  failed_segment_results: MatchingFailedSegmentResult[];
}

// 创建模板匹配结果请求
export interface CreateTemplateMatchingResultRequest {
  project_id: string;
  template_id: string;
  binding_id: string;
  result_name: string;
  description?: string;
}

// 模板匹配结果查询选项
export interface TemplateMatchingResultQueryOptions {
  project_id?: string;
  template_id?: string;
  binding_id?: string;
  status?: MatchingResultStatus;
  limit?: number;
  offset?: number;
  search_keyword?: string;
  sort_by?: string; // "created_at", "success_rate", "result_name"
  sort_order?: string; // "asc", "desc"
}

// 匹配统计信息
export interface MatchingStatistics {
  total_results: number;
  successful_results: number;
  total_segments: number;
  matched_segments: number;
  total_materials: number;
  total_models: number;
  average_success_rate: number;
}

// 保存匹配结果请求
export interface SaveMatchingResultRequest {
  service_result: any; // MaterialMatchingResult from service
  result_name: string;
  description?: string;
  matching_duration_ms: number;
}

// 更新匹配结果信息请求
export interface UpdateMatchingResultInfoRequest {
  result_id: string;
  result_name?: string;
  description?: string;
}

// 设置质量评分请求
export interface SetQualityScoreRequest {
  result_id: string;
  quality_score: number;
}

// 匹配结果状态选项
export interface MatchingResultStatusOption {
  value: string;
  label: string;
}

// 匹配结果列表项（用于列表显示）
export interface MatchingResultListItem {
  id: string;
  result_name: string;
  project_id: string;
  template_id: string;
  status: MatchingResultStatus;
  success_rate: number;
  total_segments: number;
  matched_segments: number;
  failed_segments: number;
  used_materials: number;
  used_models: number;
  matching_duration_ms: number;
  quality_score?: number;
  created_at: string;
  updated_at: string;
}

// 匹配结果卡片信息（用于卡片显示）
export interface MatchingResultCardInfo {
  id: string;
  result_name: string;
  description?: string;
  status: MatchingResultStatus;
  status_display: string;
  success_rate: number;
  total_segments: number;
  matched_segments: number;
  failed_segments: number;
  used_materials: number;
  used_models: number;
  matching_duration_display: string; // 格式化后的时长显示
  quality_score?: number;
  created_at_display: string; // 格式化后的创建时间显示
  updated_at_display: string; // 格式化后的更新时间显示
}

// 匹配结果过滤选项
export interface MatchingResultFilterOptions {
  status?: MatchingResultStatus[];
  success_rate_min?: number;
  success_rate_max?: number;
  date_range?: {
    start_date: string;
    end_date: string;
  };
  has_quality_score?: boolean;
  min_segments?: number;
  max_segments?: number;
}

// 匹配结果排序选项
export interface MatchingResultSortOptions {
  field: 'created_at' | 'updated_at' | 'success_rate' | 'result_name' | 'total_segments' | 'matching_duration_ms';
  order: 'asc' | 'desc';
}

// 匹配结果分页选项
export interface MatchingResultPaginationOptions {
  page: number;
  page_size: number;
  total?: number;
}

// 匹配结果搜索选项
export interface MatchingResultSearchOptions {
  keyword?: string;
  filter?: MatchingResultFilterOptions;
  sort?: MatchingResultSortOptions;
  pagination?: MatchingResultPaginationOptions;
}

// 匹配结果导出选项
export interface MatchingResultExportOptions {
  format: 'json' | 'csv' | 'excel';
  include_segments: boolean;
  include_failed_segments: boolean;
  date_range?: {
    start_date: string;
    end_date: string;
  };
}

// 匹配结果批量操作选项
export interface MatchingResultBatchOperationOptions {
  operation: 'delete' | 'soft_delete' | 'export' | 'set_quality_score';
  result_ids: string[];
  parameters?: Record<string, any>;
}

// 匹配结果分析报告
export interface MatchingResultAnalysisReport {
  total_results: number;
  status_distribution: Record<MatchingResultStatus, number>;
  success_rate_distribution: {
    excellent: number; // 90-100%
    good: number; // 70-89%
    fair: number; // 50-69%
    poor: number; // 0-49%
  };
  duration_statistics: {
    min: number;
    max: number;
    average: number;
    median: number;
  };
  segment_statistics: {
    total_segments: number;
    total_matched: number;
    total_failed: number;
    average_segments_per_result: number;
  };
  material_usage: {
    total_materials_used: number;
    average_materials_per_result: number;
    most_used_materials: Array<{
      material_id: string;
      material_name: string;
      usage_count: number;
    }>;
  };
  model_usage: {
    total_models_used: number;
    average_models_per_result: number;
    most_used_models: Array<{
      model_id: string;
      model_name: string;
      usage_count: number;
    }>;
  };
  quality_score_distribution?: {
    excellent: number; // 4.5-5.0
    good: number; // 3.5-4.4
    fair: number; // 2.5-3.4
    poor: number; // 0-2.4
    unrated: number;
  };
  trends: {
    success_rate_trend: 'improving' | 'declining' | 'stable';
    duration_trend: 'faster' | 'slower' | 'stable';
    quality_trend?: 'improving' | 'declining' | 'stable';
  };
}

// 匹配结果操作历史
export interface MatchingResultOperationHistory {
  id: string;
  result_id: string;
  operation_type: 'create' | 'update' | 'delete' | 'quality_score_set';
  operation_details: string;
  performed_by?: string;
  performed_at: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
}
