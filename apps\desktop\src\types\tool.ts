import { LucideIcon } from 'lucide-react';

/**
 * 工具卡片数据结构
 * 遵循 TypeScript 类型安全原则和 Tauri 开发规范
 */
export interface Tool {
  /** 工具唯一标识符 */
  id: string;
  
  /** 工具名称 */
  name: string;
  
  /** 工具简介描述 */
  description: string;
  
  /** 工具详细说明 */
  longDescription?: string;
  
  /** 工具图标 (Lucide React 图标) */
  icon: LucideIcon;
  
  /** 工具路由路径 */
  route: string;
  
  /** 工具分类 */
  category: ToolCategory;
  
  /** 工具状态 */
  status: ToolStatus;
  
  /** 工具标签 */
  tags?: string[];
  
  /** 是否为新功能 */
  isNew?: boolean;
  
  /** 是否为热门工具 */
  isPopular?: boolean;
  
  /** 工具版本 */
  version?: string;
  
  /** 最后更新时间 */
  lastUpdated?: string;
}

/**
 * 工具分类枚举
 */
export enum ToolCategory {
  /** 数据处理 */
  DATA_PROCESSING = 'data_processing',
  
  /** 开发调试 */
  DEVELOPMENT = 'development',
  
  /** 文件处理 */
  FILE_PROCESSING = 'file_processing',
  
  /** AI工具 */
  AI_TOOLS = 'ai_tools',
  
  /** 实用工具 */
  UTILITIES = 'utilities'
}

/**
 * 工具状态枚举
 */
export enum ToolStatus {
  /** 稳定版本 */
  STABLE = 'stable',
  
  /** 测试版本 */
  BETA = 'beta',
  
  /** 实验性功能 */
  EXPERIMENTAL = 'experimental',
  
  /** 已废弃 */
  DEPRECATED = 'deprecated'
}

/**
 * 工具卡片显示配置
 */
export interface ToolCardConfig {
  /** 是否显示状态标签 */
  showStatus?: boolean;
  
  /** 是否显示分类 */
  showCategory?: boolean;
  
  /** 是否显示标签 */
  showTags?: boolean;
  
  /** 是否显示版本信息 */
  showVersion?: boolean;
  
  /** 卡片尺寸 */
  size?: 'small' | 'medium' | 'large';
  
  /** 是否启用悬停效果 */
  enableHover?: boolean;
}

/**
 * 工具筛选配置
 */
export interface ToolFilterConfig {
  /** 按分类筛选 */
  category?: ToolCategory;
  
  /** 按状态筛选 */
  status?: ToolStatus;
  
  /** 按标签筛选 */
  tags?: string[];
  
  /** 搜索关键词 */
  searchQuery?: string;
  
  /** 是否只显示新功能 */
  showNewOnly?: boolean;
  
  /** 是否只显示热门工具 */
  showPopularOnly?: boolean;
}

/**
 * 工具排序配置
 */
export interface ToolSortConfig {
  /** 排序字段 */
  field: 'name' | 'category' | 'lastUpdated' | 'popularity';
  
  /** 排序方向 */
  direction: 'asc' | 'desc';
}
