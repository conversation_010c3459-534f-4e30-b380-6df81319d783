import { MaterialType } from './material';

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  Pending = 'Pending',
  Uploading = 'Uploading',
  Analyzing = 'Analyzing',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
}

/**
 * 队列状态枚举
 */
export enum QueueStatus {
  Stopped = 'Stopped',
  Running = 'Running',
  Paused = 'Paused',
}

/**
 * 分类状态枚举
 */
export enum ClassificationStatus {
  Classified = 'Classified',
  Failed = 'Failed',
  NeedsReview = 'NeedsReview',
}

/**
 * 批量分类请求
 */
export interface BatchClassificationRequest {
  material_id: string;
  project_id: string;
  overwrite_existing: boolean;
  priority?: number;
}

/**
 * 项目一键分类请求
 */
export interface ProjectBatchClassificationRequest {
  project_id: string;
  overwrite_existing?: boolean;
  material_types?: MaterialType[];
  priority?: number;
}

/**
 * 项目一键分类响应
 */
export interface ProjectBatchClassificationResponse {
  total_materials: number;
  eligible_materials: number;
  created_tasks: number;
  task_ids: string[];
  skipped_materials: string[];
}

/**
 * 任务进度信息
 */
export interface TaskProgress {
  task_id: string;
  status: TaskStatus;
  progress_percentage: number;
  current_step: string;
  error_message?: string;
  started_at?: string;
  estimated_completion?: string;
}

/**
 * 队列统计信息
 */
export interface QueueStats {
  status: QueueStatus;
  total_tasks: number;
  pending_tasks: number;
  processing_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  current_task_id?: string;
  processing_rate: number;
}

/**
 * 视频分类记录
 */
export interface VideoClassificationRecord {
  id: string;
  task_id: string;
  segment_id: string;
  material_id: string;
  project_id: string;
  video_file_path: string;
  classification_result: string;
  confidence_score: number;
  reasoning: string;
  features: string[];
  product_match: boolean;
  quality_score: number;
  gemini_file_uri: string;
  raw_response: string;
  status: ClassificationStatus;
  created_at: string;
  updated_at: string;
}

/**
 * 分类统计信息
 */
export interface ClassificationStats {
  total_tasks: number;
  pending_tasks: number;
  processing_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  total_classifications: number;
  successful_classifications: number;
  failed_classifications: number;
  needs_review_classifications: number;
  average_confidence: number;
  average_quality_score: number;
}

/**
 * 视频分类任务
 */
export interface VideoClassificationTask {
  id: string;
  segment_id: string;
  material_id: string;
  project_id: string;
  video_file_path: string;
  status: TaskStatus;
  priority: number;
  gemini_file_uri?: string;
  prompt_text?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
}
