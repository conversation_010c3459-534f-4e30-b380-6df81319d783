// 视频生成相关类型定义

// 素材中心的素材类型分类
export enum MaterialCategory {
  Model = 'model',        // 模特
  Product = 'product',    // 产品
  Scene = 'scene',        // 场景
  Action = 'action',      // 动作
  Music = 'music',        // 音乐
  PromptTemplate = 'prompt_template'  // 提示词模板
}

// 素材中心的素材项
export interface MaterialAsset {
  id: string;
  name: string;
  category: MaterialCategory;
  type: 'image' | 'video' | 'audio' | 'text';
  file_path?: string;
  thumbnail_path?: string;
  description?: string;
  tags: string[];
  metadata?: {
    duration?: number;
    width?: number;
    height?: number;
    size?: number;
    format?: string;
  };
  created_at: string;
  updated_at: string;
}

// 提示词模板素材
export interface PromptTemplateAsset extends MaterialAsset {
  category: MaterialCategory.PromptTemplate;
  type: 'text';
  template_content: string;
  variables: string[];  // 模板中的变量
  example_output?: string;
}

// 视频生成项目
export interface VideoGenerationProject {
  id: string;
  name: string;
  description?: string;
  selected_assets: {
    [key in MaterialCategory]?: MaterialAsset[];
  };
  generation_config: VideoGenerationConfig;
  status: VideoGenerationStatus;
  result?: VideoGenerationResult;
  created_at: string;
  updated_at: string;
}

// 视频生成配置
export interface VideoGenerationConfig {
  output_format: 'mp4' | 'mov' | 'avi';
  resolution: '720p' | '1080p' | '4k';
  frame_rate: 24 | 30 | 60;
  duration: number;  // 秒
  quality: 'low' | 'medium' | 'high';
  audio_enabled: boolean;
  effects?: VideoEffect[];
}

// 视频特效
export interface VideoEffect {
  type: 'transition' | 'filter' | 'overlay';
  name: string;
  parameters: Record<string, any>;
  start_time?: number;
  duration?: number;
}

export interface VideoGenerationTask {
  id: string;
  project_id: string;
  model_id: string;
  prompt_config: VideoPromptConfig;
  selected_photos: string[];
  status: VideoGenerationStatus;
  result?: VideoGenerationResult;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface VideoPromptConfig {
  product: string;      // 产品描述
  scene: string;        // 场景描述
  model_desc: string;   // 模特描述
  template: string;     // 模板类型
  duplicate: number;    // 生成数量
}

export enum VideoGenerationStatus {
  Pending = "Pending",
  Processing = "Processing", 
  Completed = "Completed",
  Failed = "Failed",
  Cancelled = "Cancelled"
}

export interface VideoGenerationResult {
  video_urls: string[];
  video_paths: string[];
  generation_time: number;
  api_response?: string;
}

export interface CreateVideoGenerationRequest {
  model_id: string;
  prompt_config: VideoPromptConfig;
  selected_photos: string[];
}

export interface VideoGenerationQueryParams {
  model_id?: string;
  status?: VideoGenerationStatus;
  limit?: number;
  offset?: number;
}

export interface DifyApiConfig {
  host: string;
  api_key: string;
}

export interface DifyApiRequest {
  inputs: DifyInputs;
  response_mode: string;
  user: string;
}

export interface DifyInputs {
  product: string;
  scene: string;
  model: string;
  image: string;
  duplicate: number;
  template: string;
}

export interface DifyApiResponse {
  data?: DifyResponseData;
  error?: string;
}

export interface DifyResponseData {
  outputs: DifyOutputs;
}

export interface DifyOutputs {
  output: string[];
}

// 视频生成表单数据
export interface VideoGenerationFormData {
  product: string;
  scene: string;
  model_desc: string;
  template: string;
  duplicate: number;
  selected_photos: string[];
}

// 视频生成表单错误
export interface VideoGenerationFormErrors {
  product?: string;
  scene?: string;
  model_desc?: string;
  template?: string;
  duplicate?: string;
  selected_photos?: string;
}

// 预设模板选项
export interface TemplateOption {
  value: string;
  label: string;
  description?: string;
}

// 常用模板预设
export const TEMPLATE_OPTIONS: TemplateOption[] = [
  { value: "抚媚眼神", label: "抚媚眼神", description: "展现模特魅惑的眼神和表情" },
  { value: "清纯甜美", label: "清纯甜美", description: "展现模特清纯可爱的一面" },
  { value: "性感妩媚", label: "性感妩媚", description: "展现模特性感迷人的魅力" },
  { value: "优雅知性", label: "优雅知性", description: "展现模特优雅大方的气质" },
  { value: "活力青春", label: "活力青春", description: "展现模特青春活力的状态" },
  { value: "神秘冷艳", label: "神秘冷艳", description: "展现模特神秘高冷的气质" },
];

// 常用场景预设
export const SCENE_OPTIONS = [
  "室内可爱简约的女性卧室",
  "现代简约的客厅环境",
  "温馨浪漫的咖啡厅",
  "时尚现代的摄影棚",
  "自然清新的户外花园",
  "优雅精致的酒店套房",
  "艺术感十足的工作室",
  "温暖舒适的家居环境",
];

// 常用产品描述预设
export const PRODUCT_OPTIONS = [
  "超短牛仔裙（白色紧身蕾丝短袖）",
  "黑色修身连衣裙",
  "白色衬衫配黑色短裙",
  "粉色针织毛衣配牛仔裤",
  "红色晚礼服",
  "休闲运动装",
  "职业套装",
  "夏日清新连衣裙",
];

// 素材中心相关接口
export interface MaterialCenterAPI {
  // 素材管理
  getMaterialAssets: (category?: MaterialCategory) => Promise<MaterialAsset[]>;
  createMaterialAsset: (asset: Omit<MaterialAsset, 'id' | 'created_at' | 'updated_at'>) => Promise<MaterialAsset>;
  updateMaterialAsset: (id: string, updates: Partial<MaterialAsset>) => Promise<MaterialAsset>;
  deleteMaterialAsset: (id: string) => Promise<void>;
  searchMaterialAssets: (query: string, category?: MaterialCategory) => Promise<MaterialAsset[]>;

  // 提示词模板管理
  getPromptTemplates: () => Promise<PromptTemplateAsset[]>;
  createPromptTemplate: (template: Omit<PromptTemplateAsset, 'id' | 'created_at' | 'updated_at'>) => Promise<PromptTemplateAsset>;
  updatePromptTemplate: (id: string, updates: Partial<PromptTemplateAsset>) => Promise<PromptTemplateAsset>;
  deletePromptTemplate: (id: string) => Promise<void>;
}

// 视频生成项目相关接口
export interface VideoGenerationProjectAPI {
  // 项目管理
  getVideoGenerationProjects: () => Promise<VideoGenerationProject[]>;
  createVideoGenerationProject: (project: Omit<VideoGenerationProject, 'id' | 'created_at' | 'updated_at'>) => Promise<VideoGenerationProject>;
  updateVideoGenerationProject: (id: string, updates: Partial<VideoGenerationProject>) => Promise<VideoGenerationProject>;
  deleteVideoGenerationProject: (id: string) => Promise<void>;

  // 素材选择
  addAssetToProject: (projectId: string, category: MaterialCategory, asset: MaterialAsset) => Promise<void>;
  removeAssetFromProject: (projectId: string, category: MaterialCategory, assetId: string) => Promise<void>;

  // 视频生成
  generateVideo: (projectId: string) => Promise<VideoGenerationTask>;
  previewVideoGeneration: (projectId: string) => Promise<string>; // 返回预览URL
}

// 视频生成API相关类型
export interface VideoGenerationAPI {
  createVideoGenerationTask: (request: CreateVideoGenerationRequest) => Promise<VideoGenerationTask>;
  getVideoGenerationTask: (taskId: string) => Promise<VideoGenerationTask | null>;
  getVideoGenerationTasks: (params?: VideoGenerationQueryParams) => Promise<VideoGenerationTask[]>;
  cancelVideoGenerationTask: (taskId: string) => Promise<void>;
  deleteVideoGenerationTask: (taskId: string) => Promise<void>;
  retryVideoGenerationTask: (taskId: string) => Promise<VideoGenerationTask>;
}

// 组件相关类型定义

// 素材卡片组件属性
export interface MaterialAssetCardProps {
  asset: MaterialAsset;
  isSelected?: boolean;
  onSelect?: (asset: MaterialAsset) => void;
  onPreview?: (asset: MaterialAsset) => void;
  onEdit?: (asset: MaterialAsset) => void;
  onDelete?: (asset: MaterialAsset) => void;
  showActions?: boolean;
}

// 素材选择器组件属性
export interface MaterialSelectorProps {
  category: MaterialCategory;
  selectedAssets: MaterialAsset[];
  onAssetsChange: (assets: MaterialAsset[]) => void;
  maxSelection?: number;
  allowMultiple?: boolean;
}

// 素材中心页面组件属性
export interface MaterialCenterProps {
  selectedCategory?: MaterialCategory;
  onCategoryChange?: (category: MaterialCategory) => void;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
}

// 视频生成页面组件属性
export interface VideoGenerationPageProps {
  projectId?: string;
}

// 视频预览组件属性
export interface VideoPreviewProps {
  project: VideoGenerationProject;
  onConfigChange?: (config: VideoGenerationConfig) => void;
}

// 素材分类标签配置
export const MATERIAL_CATEGORY_CONFIG = {
  [MaterialCategory.Model]: {
    label: "模特",
    icon: "👤",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200",
    description: "模特照片和视频素材"
  },
  [MaterialCategory.Product]: {
    label: "产品",
    icon: "📦",
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200",
    description: "产品展示素材"
  },
  [MaterialCategory.Scene]: {
    label: "场景",
    icon: "🏞️",
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200",
    description: "背景场景素材"
  },
  [MaterialCategory.Action]: {
    label: "动作",
    icon: "🎭",
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200",
    description: "动作和姿态素材"
  },
  [MaterialCategory.Music]: {
    label: "音乐",
    icon: "🎵",
    color: "text-pink-600",
    bgColor: "bg-pink-50",
    borderColor: "border-pink-200",
    description: "背景音乐和音效"
  },
  [MaterialCategory.PromptTemplate]: {
    label: "提示词模板",
    icon: "📝",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50",
    borderColor: "border-indigo-200",
    description: "AI生成提示词模板"
  },
};

// 视频生成状态显示配置
export const VIDEO_GENERATION_STATUS_CONFIG = {
  [VideoGenerationStatus.Pending]: {
    label: "等待中",
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    borderColor: "border-yellow-200",
  },
  [VideoGenerationStatus.Processing]: {
    label: "处理中",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200",
  },
  [VideoGenerationStatus.Completed]: {
    label: "已完成",
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200",
  },
  [VideoGenerationStatus.Failed]: {
    label: "失败",
    color: "text-red-600",
    bgColor: "bg-red-50",
    borderColor: "border-red-200",
  },
  [VideoGenerationStatus.Cancelled]: {
    label: "已取消",
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    borderColor: "border-gray-200",
  },
};
