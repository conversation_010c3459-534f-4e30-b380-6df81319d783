/**
 * 水印相关类型定义
 * 与Rust后端的数据模型保持一致
 */

export interface WatermarkTemplate {
  id: string;
  name: string;
  file_path: string;
  thumbnail_path?: string;
  category: WatermarkCategory;
  watermark_type: WatermarkType;
  file_size: number;
  width?: number;
  height?: number;
  description?: string;
  tags: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type WatermarkType = 'Image' | 'Vector' | 'Text' | 'Animated';

export type WatermarkCategory = 'Logo' | 'Copyright' | 'Signature' | 'Decoration' | 'Custom';

export interface WatermarkDetectionResult {
  id: string;
  material_id: string;
  detection_method: DetectionMethod;
  detections: WatermarkDetection[];
  confidence_score: number;
  processing_time_ms: number;
  created_at: string;
}

export interface WatermarkDetection {
  region: BoundingBox;
  confidence: number;
  watermark_type?: WatermarkType;
  template_id?: string;
  description?: string;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export type DetectionMethod = 
  | 'TemplateMatching' 
  | 'EdgeDetection' 
  | 'FrequencyAnalysis' 
  | 'TransparencyDetection' 
  | 'Combined';

export interface WatermarkConfig {
  watermark_type: WatermarkType;
  position: WatermarkPosition;
  opacity: number; // 0.0-1.0
  scale: number;
  rotation: number; // 角度
  animation?: WatermarkAnimation;
  blend_mode: BlendMode;
  quality_level: QualityLevel;
}

export type WatermarkPosition = 
  | 'TopLeft' 
  | 'TopCenter' 
  | 'TopRight'
  | 'MiddleLeft' 
  | 'Center' 
  | 'MiddleRight'
  | 'BottomLeft' 
  | 'BottomCenter' 
  | 'BottomRight'
  | { Custom: { x: number; y: number } }
  | { Dynamic: DynamicPositionRule };

export interface DynamicPositionRule {
  avoid_faces: boolean;
  avoid_text: boolean;
  follow_motion: boolean;
  corner_preference: Corner[];
  min_distance_from_edge: number;
}

export type Corner = 'TopLeft' | 'TopRight' | 'BottomLeft' | 'BottomRight';

export interface WatermarkAnimation {
  animation_type: AnimationType;
  duration_ms: number;
  loop_count?: number; // undefined表示无限循环
  easing: EasingFunction;
}

export type AnimationType = 
  | 'FadeIn' 
  | 'FadeOut' 
  | 'SlideIn' 
  | 'SlideOut' 
  | 'Rotate' 
  | 'Scale' 
  | 'Pulse'
  | { Custom: string };

export type EasingFunction = 
  | 'Linear' 
  | 'EaseIn' 
  | 'EaseOut' 
  | 'EaseInOut' 
  | 'Bounce' 
  | 'Elastic';

export type BlendMode = 
  | 'Normal' 
  | 'Multiply' 
  | 'Screen' 
  | 'Overlay' 
  | 'SoftLight' 
  | 'HardLight' 
  | 'ColorDodge' 
  | 'ColorBurn';

export type QualityLevel = 'Low' | 'Medium' | 'High' | 'Lossless';

export interface WatermarkRemovalConfig {
  method: RemovalMethod;
  quality_level: QualityLevel;
  preserve_aspect_ratio: boolean;
  target_regions?: BoundingBox[];
  inpainting_model?: string;
  blur_radius?: number;
  crop_margin?: number;
}

export type RemovalMethod = 
  | 'Inpainting' 
  | 'Blurring' 
  | 'Cropping' 
  | 'Masking' 
  | 'ContentAware' 
  | 'Clone';

export interface WatermarkDetectionConfig {
  similarity_threshold: number; // 0.0-1.0
  min_watermark_size: [number, number];
  max_watermark_size: [number, number];
  detection_regions: DetectionRegion[];
  frame_sample_rate: number;
  methods: DetectionMethod[];
  template_ids?: string[];
}

export type DetectionRegion = 
  | 'FullFrame' 
  | 'Corners' 
  | 'Edges' 
  | 'Center'
  | { Custom: BoundingBox };

export interface BatchWatermarkTask {
  task_id: string;
  operation: WatermarkOperation;
  material_ids: string[];
  config: any; // 动态配置，根据operation类型解析
  status: BatchTaskStatus;
  progress: BatchProgress;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export type WatermarkOperation = 
  | 'Detect' 
  | 'Remove' 
  | 'Add' 
  | 'DetectAndRemove' 
  | 'Replace';

export type BatchTaskStatus = 
  | 'Pending' 
  | 'Running' 
  | 'Completed' 
  | 'Failed' 
  | 'Cancelled' 
  | 'Paused';

export interface BatchProgress {
  total_items: number;
  processed_items: number;
  failed_items: number;
  current_item?: string;
  progress_percentage: number; // 0.0-100.0
  estimated_remaining_ms?: number;
  errors: string[];
  detection_results: WatermarkDetectionResult[];
  processing_results: WatermarkProcessingResult[];
}

export interface WatermarkProcessingResult {
  material_id: string;
  operation: WatermarkOperation;
  success: boolean;
  output_path?: string;
  processing_time_ms: number;
  error_message?: string;
  metadata?: any;
}

// 前端专用的扩展类型
export interface WatermarkToolState {
  activeTab: 'detect' | 'remove' | 'add';
  isLoading: boolean;
  selectedMaterials: string[];
  selectedTemplate?: string;
  detectionConfig: WatermarkDetectionConfig;
  removalConfig: WatermarkRemovalConfig;
  additionConfig: WatermarkConfig;
  batchTask?: BatchWatermarkTask;
}

export interface WatermarkTemplateUpload {
  name: string;
  file: File;
  category: WatermarkCategory;
  watermark_type: WatermarkType;
  description?: string;
  tags: string[];
}

export interface WatermarkDetectionResultDisplay extends WatermarkDetectionResult {
  material_name?: string;
  material_thumbnail?: string;
  detection_preview?: string; // 检测结果预览图
}

// 水印处理事件类型
export interface WatermarkProcessingEvent {
  type: 'task_started' | 'task_progress' | 'task_completed' | 'task_failed' | 'task_cancelled';
  task_id: string;
  data?: any;
}

// 水印模板管理相关
export interface WatermarkTemplateFilter {
  category?: WatermarkCategory;
  watermark_type?: WatermarkType;
  search_text?: string;
  tags?: string[];
  is_active?: boolean;
}

export interface WatermarkTemplateStats {
  total_templates: number;
  by_category: Record<WatermarkCategory, number>;
  by_type: Record<WatermarkType, number>;
  total_size: number; // 总文件大小（字节）
}

// 水印检测统计
export interface WatermarkDetectionStats {
  total_detections: number;
  by_method: Record<DetectionMethod, number>;
  average_confidence: number;
  processing_time_stats: {
    min: number;
    max: number;
    average: number;
  };
}

// 批量处理统计
export interface BatchProcessingStats {
  total_tasks: number;
  by_operation: Record<WatermarkOperation, number>;
  by_status: Record<BatchTaskStatus, number>;
  success_rate: number;
  average_processing_time: number;
}

// 水印处理历史记录
export interface WatermarkProcessingHistory {
  id: string;
  material_id: string;
  operation: WatermarkOperation;
  config: any;
  result: WatermarkProcessingResult;
  created_at: string;
}

// 水印预设配置
export interface WatermarkPreset {
  id: string;
  name: string;
  description?: string;
  operation: WatermarkOperation;
  config: WatermarkConfig | WatermarkRemovalConfig | WatermarkDetectionConfig;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// 导出相关类型
export interface WatermarkExportOptions {
  include_original: boolean;
  include_processed: boolean;
  export_format: 'zip' | 'folder';
  quality_level: QualityLevel;
  naming_pattern: string;
}

export interface WatermarkExportResult {
  export_id: string;
  file_path: string;
  file_size: number;
  exported_count: number;
  created_at: string;
}
