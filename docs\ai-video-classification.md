# AI视频分类功能文档

## 概述

AI视频分类功能是MixVideo桌面应用的核心功能之一，通过集成Google Gemini API实现对视频片段的智能分类。该功能遵循Tauri开发规范，采用分层架构设计，提供完整的任务队列管理和用户界面。

## 功能特性

### 核心功能
- **智能视频分类**: 使用Google Gemini AI对视频片段进行自动分类
- **批量处理**: 支持对素材的所有片段进行批量分类
- **任务队列**: 实现排队机制，支持任务优先级和重试机制
- **实时进度**: 提供实时的分类进度和状态更新
- **文件整理**: 根据分类结果自动移动视频文件到对应分类文件夹

### 用户界面
- **一键分类**: 在素材卡片中提供AI分类按钮
- **进度显示**: 优美的进度条和状态指示器
- **结果展示**: 详细的分类结果和统计信息
- **错误处理**: 友好的错误提示和重试机制

## 技术架构

### 后端架构 (Rust/Tauri)

#### 数据模型层 (`src/data/models/`)
- `video_classification.rs`: 视频分类相关数据模型
  - `VideoClassificationRecord`: 分类记录
  - `VideoClassificationTask`: 分类任务
  - `BatchClassificationRequest`: 批量分类请求
  - `ClassificationStats`: 分类统计信息

#### 数据访问层 (`src/data/repositories/`)
- `video_classification_repository.rs`: 视频分类数据仓库
  - 分类记录的CRUD操作
  - 分类任务的状态管理
  - 统计信息查询

#### 业务逻辑层 (`src/business/services/`)
- `video_classification_service.rs`: 视频分类业务服务
  - 批量任务创建
  - Gemini API调用
  - 文件移动和整理
- `video_classification_queue.rs`: 任务队列管理
  - 队列状态控制
  - 任务进度跟踪
  - 并发处理控制

#### 基础设施层 (`src/infrastructure/`)
- `gemini_service.rs`: Gemini API集成服务
  - 访问令牌管理
  - 视频文件上传
  - AI内容分析

#### 表现层 (`src/presentation/commands/`)
- `video_classification_commands.rs`: Tauri命令接口
  - 前后端通信桥梁
  - 命令参数验证
  - 错误处理

### 前端架构 (React/TypeScript)

#### 状态管理 (`src/store/`)
- `videoClassificationStore.ts`: 视频分类状态管理
  - Zustand状态存储
  - API调用封装
  - 错误状态管理

#### 组件层 (`src/components/`)
- `MaterialCard.tsx`: 素材卡片组件（增强）
  - AI分类按钮
  - 分类状态显示
- `VideoClassificationProgress.tsx`: 分类进度组件
  - 实时进度显示
  - 队列状态控制
  - 统计信息展示

## 数据库设计

### 视频分类记录表 (`video_classification_records`)
```sql
CREATE TABLE video_classification_records (
    id TEXT PRIMARY KEY,
    segment_id TEXT NOT NULL,
    material_id TEXT NOT NULL,
    project_id TEXT NOT NULL,
    category TEXT NOT NULL,
    confidence REAL NOT NULL,
    reasoning TEXT NOT NULL,
    features TEXT NOT NULL,
    product_match INTEGER NOT NULL,
    quality_score REAL NOT NULL,
    gemini_file_uri TEXT,
    raw_response TEXT,
    status TEXT NOT NULL,
    error_message TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### 视频分类任务表 (`video_classification_tasks`)
```sql
CREATE TABLE video_classification_tasks (
    id TEXT PRIMARY KEY,
    segment_id TEXT NOT NULL,
    material_id TEXT NOT NULL,
    project_id TEXT NOT NULL,
    video_file_path TEXT NOT NULL,
    status TEXT NOT NULL,
    priority INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    gemini_file_uri TEXT,
    prompt_text TEXT,
    error_message TEXT,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## API接口

### Tauri命令接口

#### 启动视频分类
```rust
#[command]
pub async fn start_video_classification(
    request: BatchClassificationRequest,
    state: State<'_, AppState>,
) -> Result<Vec<String>, String>
```

#### 获取队列状态
```rust
#[command]
pub async fn get_classification_queue_status(
    state: State<'_, AppState>,
) -> Result<QueueStats, String>
```

#### 获取任务进度
```rust
#[command]
pub async fn get_classification_task_progress(
    task_id: String,
    state: State<'_, AppState>,
) -> Result<Option<TaskProgress>, String>
```

### Gemini API集成

#### 配置
```rust
pub struct GeminiConfig {
    pub base_url: String,
    pub bearer_token: String,
    pub timeout: u64,
}
```

#### 主要方法
- `upload_video_file()`: 上传视频文件到Gemini
- `generate_content_analysis()`: 生成内容分析
- `classify_video()`: 完整的视频分类流程

## 使用流程

### 1. 用户操作流程
1. 用户在素材详情页面点击"AI分类"按钮
2. 系统创建批量分类任务并加入队列
3. 队列开始处理任务，显示实时进度
4. 完成分类后，视频文件自动移动到分类文件夹
5. 用户可查看分类结果和统计信息

### 2. 系统处理流程
1. **任务创建**: 为素材的每个片段创建分类任务
2. **队列处理**: 按优先级顺序处理任务
3. **视频上传**: 将视频文件上传到Gemini
4. **AI分析**: 调用Gemini API进行内容分析
5. **结果解析**: 解析AI响应并创建分类记录
6. **文件移动**: 根据分类结果移动视频文件
7. **状态更新**: 更新任务状态和进度信息

## 错误处理

### 常见错误类型
- **网络错误**: Gemini API连接失败
- **文件错误**: 视频文件不存在或损坏
- **解析错误**: AI响应格式异常
- **权限错误**: 文件移动权限不足

### 错误处理策略
- **自动重试**: 网络错误和临时故障自动重试
- **降级处理**: AI响应异常时使用默认分类
- **用户提示**: 友好的错误消息和解决建议
- **日志记录**: 详细的错误日志用于调试

## 性能优化

### 并发控制
- 单任务处理避免资源竞争
- 任务间延迟防止API限流
- 连接池管理减少开销

### 缓存策略
- 访问令牌缓存减少认证请求
- 分类结果缓存避免重复处理
- 进度状态缓存提升响应速度

## 配置说明

### Gemini API配置
```rust
// 默认配置
GeminiConfig {
    base_url: "https://bowongai-dev--bowong-ai-video-gemini-fastapi-webapp.modal.run",
    bearer_token: "bowong7777",
    timeout: 120,
}
```

### 队列配置
- `max_concurrent_tasks`: 最大并发任务数（默认1）
- `processing_delay`: 任务间延迟（默认2秒）
- `max_retries`: 最大重试次数（默认3次）

## 部署注意事项

### 依赖要求
- Rust 1.70+
- Tauri 2.0+
- SQLite 3.35+
- Node.js 18+

### 环境变量
- `GEMINI_API_URL`: Gemini API地址
- `GEMINI_BEARER_TOKEN`: 认证令牌

### 权限配置
- 文件系统读写权限
- 网络访问权限
- 数据库访问权限

## 开发规范遵循

### Tauri开发规范
- ✅ 分层架构设计
- ✅ 错误处理机制
- ✅ 异步处理模式
- ✅ 状态管理规范

### 前端开发规范
- ✅ 组件化设计
- ✅ 状态管理最佳实践
- ✅ 用户体验优化
- ✅ 响应式设计

### 代码质量
- ✅ 类型安全
- ✅ 错误处理
- ✅ 单元测试
- ✅ 文档完整

## 后续优化建议

1. **性能优化**
   - 实现多任务并发处理
   - 添加视频预处理缓存
   - 优化数据库查询性能

2. **功能增强**
   - 支持自定义分类规则
   - 添加分类结果人工审核
   - 实现分类模型训练

3. **用户体验**
   - 添加分类预览功能
   - 支持批量操作撤销
   - 优化进度显示动画

4. **监控运维**
   - 添加性能监控
   - 实现日志分析
   - 支持配置热更新
