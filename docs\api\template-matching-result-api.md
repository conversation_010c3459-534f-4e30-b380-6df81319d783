# 模板匹配结果 API 文档

## 概述

模板匹配结果 API 提供了完整的模板匹配结果管理功能，包括保存匹配结果、查询历史记录、管理匹配详情等操作。

## 数据模型

### TemplateMatchingResult

模板匹配结果主要实体。

```typescript
interface TemplateMatchingResult {
  id: string;                    // 匹配结果唯一标识
  project_id: string;            // 项目ID
  template_id: string;           // 模板ID
  binding_id: string;            // 绑定ID
  result_name: string;           // 结果名称
  description?: string;          // 结果描述
  total_segments: number;        // 总片段数
  matched_segments: number;      // 匹配成功片段数
  failed_segments: number;       // 匹配失败片段数
  success_rate: number;          // 成功率（百分比）
  used_materials: number;        // 使用的素材数量
  used_models: number;           // 使用的模特数量
  matching_duration_ms: number;  // 匹配耗时（毫秒）
  quality_score?: number;        // 质量评分（0-5）
  status: MatchingResultStatus;  // 匹配状态
  metadata?: string;             // 额外元数据（JSON格式）
  created_at: string;            // 创建时间
  updated_at: string;            // 更新时间
  is_active: boolean;            // 是否激活
}
```

### MatchingResultStatus

匹配结果状态枚举。

```typescript
enum MatchingResultStatus {
  Success = 'Success',           // 匹配成功
  PartialSuccess = 'PartialSuccess', // 部分成功
  Failed = 'Failed',             // 匹配失败
  Cancelled = 'Cancelled'        // 已取消
}
```

### MatchingSegmentResult

成功匹配的片段结果。

```typescript
interface MatchingSegmentResult {
  id: string;                    // 片段结果ID
  matching_result_id: string;    // 关联的匹配结果ID
  track_segment_id: string;      // 模板轨道片段ID
  track_segment_name: string;    // 轨道片段名称
  material_segment_id: string;   // 匹配到的素材片段ID
  material_id: string;           // 素材ID
  material_name: string;         // 素材名称
  model_id?: string;             // 模特ID
  model_name?: string;           // 模特名称
  match_score: number;           // 匹配评分（0-1）
  match_reason: string;          // 匹配原因
  segment_duration: number;      // 片段时长（微秒）
  start_time: number;            // 开始时间（微秒）
  end_time: number;              // 结束时间（微秒）
  properties?: string;           // 片段属性（JSON格式）
  created_at: string;            // 创建时间
  updated_at: string;            // 更新时间
}
```

### MatchingFailedSegmentResult

匹配失败的片段结果。

```typescript
interface MatchingFailedSegmentResult {
  id: string;                    // 失败片段结果ID
  matching_result_id: string;    // 关联的匹配结果ID
  track_segment_id: string;      // 模板轨道片段ID
  track_segment_name: string;    // 轨道片段名称
  matching_rule_type: string;    // 匹配规则类型
  matching_rule_data?: string;   // 匹配规则数据（JSON格式）
  failure_reason: string;        // 失败原因
  failure_details?: string;      // 失败详情（JSON格式）
  segment_duration: number;      // 片段时长（微秒）
  start_time: number;            // 开始时间（微秒）
  end_time: number;              // 结束时间（微秒）
  created_at: string;            // 创建时间
  updated_at: string;            // 更新时间
}
```

## API 接口

### 1. 保存匹配结果

自动保存素材匹配结果到数据库。

```typescript
save_matching_result(
  service_result: MaterialMatchingResult,
  result_name: string,
  description?: string,
  matching_duration_ms: number
): Promise<TemplateMatchingResult>
```

**参数说明：**
- `service_result`: 素材匹配服务返回的匹配结果
- `result_name`: 结果名称
- `description`: 可选的结果描述
- `matching_duration_ms`: 匹配耗时（毫秒）

**返回值：** 保存后的模板匹配结果

### 2. 获取匹配结果详情

获取包含所有片段信息的完整匹配结果详情。

```typescript
get_matching_result_detail(result_id: string): Promise<TemplateMatchingResultDetail | null>
```

**参数说明：**
- `result_id`: 匹配结果ID

**返回值：** 匹配结果详情，包含成功和失败的片段信息

### 3. 获取项目匹配结果列表

获取指定项目的所有匹配结果。

```typescript
get_project_matching_results(project_id: string): Promise<TemplateMatchingResult[]>
```

**参数说明：**
- `project_id`: 项目ID

**返回值：** 匹配结果列表

### 4. 获取模板匹配结果列表

获取指定模板的所有匹配结果。

```typescript
get_template_matching_results(template_id: string): Promise<TemplateMatchingResult[]>
```

**参数说明：**
- `template_id`: 模板ID

**返回值：** 匹配结果列表

### 5. 获取绑定匹配结果列表

获取指定绑定的所有匹配结果。

```typescript
get_binding_matching_results(binding_id: string): Promise<TemplateMatchingResult[]>
```

**参数说明：**
- `binding_id`: 绑定ID

**返回值：** 匹配结果列表

### 6. 查询匹配结果列表

根据查询条件获取匹配结果列表。

```typescript
list_matching_results(options: TemplateMatchingResultQueryOptions): Promise<TemplateMatchingResult[]>
```

**参数说明：**
- `options`: 查询选项

```typescript
interface TemplateMatchingResultQueryOptions {
  project_id?: string;           // 项目ID过滤
  template_id?: string;          // 模板ID过滤
  binding_id?: string;           // 绑定ID过滤
  status?: MatchingResultStatus; // 状态过滤
  limit?: number;                // 限制数量
  offset?: number;               // 偏移量
  search_keyword?: string;       // 搜索关键词
  sort_by?: string;              // 排序字段
  sort_order?: string;           // 排序顺序
}
```

### 7. 删除匹配结果

永久删除匹配结果。

```typescript
delete_matching_result(result_id: string): Promise<boolean>
```

**参数说明：**
- `result_id`: 匹配结果ID

**返回值：** 是否删除成功

### 8. 软删除匹配结果

软删除匹配结果（标记为不活跃）。

```typescript
soft_delete_matching_result(result_id: string): Promise<boolean>
```

**参数说明：**
- `result_id`: 匹配结果ID

**返回值：** 是否删除成功

### 9. 更新匹配结果信息

更新匹配结果的名称和描述。

```typescript
update_matching_result_info(
  result_id: string,
  result_name?: string,
  description?: string
): Promise<TemplateMatchingResult | null>
```

**参数说明：**
- `result_id`: 匹配结果ID
- `result_name`: 新的结果名称（可选）
- `description`: 新的结果描述（可选）

**返回值：** 更新后的匹配结果

### 10. 设置质量评分

为匹配结果设置质量评分。

```typescript
set_matching_result_quality_score(
  result_id: string,
  quality_score: number
): Promise<TemplateMatchingResult | null>
```

**参数说明：**
- `result_id`: 匹配结果ID
- `quality_score`: 质量评分（0-5）

**返回值：** 更新后的匹配结果

### 11. 获取匹配统计信息

获取匹配结果的统计信息。

```typescript
get_matching_statistics(project_id?: string): Promise<MatchingStatistics>
```

**参数说明：**
- `project_id`: 可选的项目ID，如果提供则只统计该项目的数据

**返回值：** 匹配统计信息

```typescript
interface MatchingStatistics {
  total_results: number;         // 总结果数
  successful_results: number;    // 成功结果数
  total_segments: number;        // 总片段数
  matched_segments: number;      // 匹配片段数
  total_materials: number;       // 总素材数
  total_models: number;          // 总模特数
  average_success_rate: number;  // 平均成功率
}
```

### 12. 执行匹配并自动保存

执行素材匹配并自动保存结果到数据库。

```typescript
execute_material_matching_with_save(
  request: MaterialMatchingRequest,
  result_name: string,
  description?: string
): Promise<[MaterialMatchingResult, TemplateMatchingResult | null]>
```

**参数说明：**
- `request`: 素材匹配请求
- `result_name`: 结果名称
- `description`: 可选的结果描述

**返回值：** 包含匹配结果和保存结果的元组

## 错误处理

所有 API 接口都会返回 `Result<T, String>` 类型，其中错误信息为字符串格式。常见错误包括：

- `"模板匹配结果不存在"`: 指定的匹配结果ID不存在
- `"项目不存在"`: 指定的项目ID不存在
- `"模板不存在"`: 指定的模板ID不存在
- `"数据库操作失败"`: 数据库操作出现错误
- `"参数验证失败"`: 输入参数不符合要求

## 使用示例

### 保存匹配结果

```typescript
import { invoke } from '@tauri-apps/api/core';

// 执行匹配并保存结果
const result = await invoke('execute_material_matching_with_save', {
  request: {
    project_id: 'project-1',
    template_id: 'template-1',
    binding_id: 'binding-1',
    overwrite_existing: false,
  },
  resultName: '我的匹配结果',
  description: '这是一个测试匹配结果',
});

console.log('匹配结果:', result[0]);
console.log('保存结果:', result[1]);
```

### 查询匹配结果

```typescript
// 获取项目的所有匹配结果
const results = await invoke('get_project_matching_results', {
  projectId: 'project-1',
});

// 获取匹配结果详情
const detail = await invoke('get_matching_result_detail', {
  resultId: 'result-1',
});

// 查询匹配结果（带过滤条件）
const filteredResults = await invoke('list_matching_results', {
  options: {
    project_id: 'project-1',
    status: 'Success',
    limit: 10,
    offset: 0,
    sort_by: 'created_at',
    sort_order: 'desc',
  },
});
```

### 管理匹配结果

```typescript
// 更新匹配结果信息
await invoke('update_matching_result_info', {
  resultId: 'result-1',
  resultName: '更新后的名称',
  description: '更新后的描述',
});

// 设置质量评分
await invoke('set_matching_result_quality_score', {
  resultId: 'result-1',
  qualityScore: 4.5,
});

// 软删除匹配结果
await invoke('soft_delete_matching_result', {
  resultId: 'result-1',
});
```
