# 模板片段权重配置功能

## 概述

模板片段权重配置功能允许用户为每个模板片段设置不同的AI分类权重，实现更精细的素材匹配控制。该功能将权重配置从全局级别扩展到模板片段级别，提供更灵活的匹配策略。

## 功能特性

### 核心功能

1. **片段级权重配置**
   - 每个模板片段可以有独立的权重配置
   - 支持为不同AI分类设置不同权重值
   - 权重范围：0-100，数值越大优先级越高

2. **权重继承机制**
   - 新片段默认使用全局权重配置
   - 可以选择性地覆盖特定分类的权重
   - 支持重置为全局默认权重

3. **批量操作**
   - 批量复制权重配置到多个片段
   - 批量重置权重配置
   - 批量应用自定义权重配置

4. **可视化管理**
   - 直观的权重指示器
   - 实时权重预览
   - 权重配置编辑器

## 技术架构

### 后端架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  template_segment_weight_commands.rs                       │
│  - create_template_segment_weight                          │
│  - get_segment_weights_with_defaults                       │
│  - batch_update_template_segment_weights                   │
│  - reset_segment_weights_to_global                         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Layer                           │
├─────────────────────────────────────────────────────────────┤
│  TemplateSegmentWeightService                              │
│  - 权重继承逻辑                                              │
│  - 权重验证                                                  │
│  - 批量操作                                                  │
│  - 统计信息                                                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Data Layer                               │
├─────────────────────────────────────────────────────────────┤
│  TemplateSegmentWeightRepository                           │
│  - CRUD操作                                                 │
│  - 权重映射查询                                              │
│  - 批量更新                                                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Database                                 │
├─────────────────────────────────────────────────────────────┤
│  template_segment_weights 表                               │
│  - id, template_id, track_segment_id                       │
│  - ai_classification_id, weight                            │
│  - created_at, updated_at                                  │
└─────────────────────────────────────────────────────────────┘
```

### 前端架构

```
┌─────────────────────────────────────────────────────────────┐
│                    UI Components                            │
├─────────────────────────────────────────────────────────────┤
│  TemplateSegmentWeightEditor                               │
│  - 权重配置编辑界面                                          │
│  - 滑块和数值输入                                            │
│  - 验证和保存                                                │
├─────────────────────────────────────────────────────────────┤
│  SegmentWeightIndicator                                    │
│  - 权重状态指示器                                            │
│  - 快速编辑入口                                              │
├─────────────────────────────────────────────────────────────┤
│  BatchWeightConfigModal                                    │
│  - 批量权重配置                                              │
│  - 复制、重置、自定义                                        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                            │
├─────────────────────────────────────────────────────────────┤
│  TemplateSegmentWeightService                              │
│  - API调用封装                                               │
│  - 数据转换                                                  │
│  - 错误处理                                                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Tauri Commands                           │
├─────────────────────────────────────────────────────────────┤
│  invoke('create_template_segment_weight', ...)             │
│  invoke('get_segment_weights_with_defaults', ...)          │
│  invoke('batch_update_template_segment_weights', ...)      │
└─────────────────────────────────────────────────────────────┘
```

## 数据模型

### 数据库表结构

```sql
CREATE TABLE template_segment_weights (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL,
    track_segment_id TEXT NOT NULL,
    ai_classification_id TEXT NOT NULL,
    weight INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    
    FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE,
    FOREIGN KEY (track_segment_id) REFERENCES track_segments (id) ON DELETE CASCADE,
    FOREIGN KEY (ai_classification_id) REFERENCES ai_classifications (id) ON DELETE CASCADE,
    
    UNIQUE(template_id, track_segment_id, ai_classification_id)
);
```

### TypeScript 类型定义

```typescript
interface TemplateSegmentWeight {
  id: string;
  template_id: string;
  track_segment_id: string;
  ai_classification_id: string;
  weight: number;
  created_at: string;
  updated_at: string;
}

interface CreateTemplateSegmentWeightRequest {
  template_id: string;
  track_segment_id: string;
  ai_classification_id: string;
  weight: number;
}

interface BatchUpdateTemplateSegmentWeightRequest {
  template_id: string;
  track_segment_id: string;
  weights: SegmentWeightConfig[];
}

interface SegmentWeightConfig {
  ai_classification_id: string;
  weight: number;
}
```

## 使用指南

### 基本使用

1. **查看权重配置**
   - 在模板详情页面的轨道标签页中
   - 每个片段显示权重指示器
   - 悬停查看详细权重信息

2. **编辑权重配置**
   - 点击片段的权重配置编辑按钮
   - 使用滑块或数值输入调整权重
   - 点击保存应用更改

3. **批量操作**
   - 在轨道标签页点击"批量权重配置"
   - 选择操作类型：复制、重置、自定义
   - 执行批量操作

### 高级功能

1. **权重继承**
   ```typescript
   // 获取片段权重（包含默认值）
   const weights = await TemplateSegmentWeightService
     .getSegmentWeightsWithDefaults(templateId, segmentId);
   ```

2. **自定义权重**
   ```typescript
   // 设置自定义权重
   await TemplateSegmentWeightService.setSegmentWeights(
     templateId,
     segmentId,
     {
       'classification_1': 95,
       'classification_2': 30,
       'classification_3': 85,
     }
   );
   ```

3. **重置权重**
   ```typescript
   // 重置为全局权重
   await TemplateSegmentWeightService
     .resetSegmentWeightsToGlobal(templateId, segmentId);
   ```

## 最佳实践

### 权重配置策略

1. **分层配置**
   - 全局权重作为基础配置
   - 模板级权重用于特殊需求
   - 片段级权重用于精细控制

2. **权重值设置**
   - 0-20：低权重，较少使用
   - 21-50：中等权重，常规使用
   - 51-80：高权重，优先使用
   - 81-100：最高权重，强制优先

3. **批量操作**
   - 使用模板复制快速配置相似片段
   - 定期重置不需要的自定义配置
   - 使用统计信息监控配置状态

### 性能优化

1. **缓存策略**
   - 权重配置会被缓存以提高查询性能
   - 修改后自动刷新相关缓存

2. **批量操作**
   - 使用批量API减少网络请求
   - 事务处理确保数据一致性

3. **索引优化**
   - 数据库索引优化查询性能
   - 复合索引支持复杂查询

## 故障排除

### 常见问题

1. **权重配置不生效**
   - 检查权重值是否在有效范围内（0-100）
   - 确认AI分类是否激活
   - 验证模板和片段ID是否正确

2. **批量操作失败**
   - 检查目标片段是否存在
   - 验证权重配置是否有效
   - 查看错误日志获取详细信息

3. **性能问题**
   - 检查数据库索引是否正常
   - 监控权重配置数量
   - 考虑清理无用的配置

### 调试工具

1. **日志记录**
   ```rust
   // Rust 后端日志
   tracing::info!("权重配置更新: template={}, segment={}", template_id, segment_id);
   ```

2. **前端调试**
   ```typescript
   // 开发者工具中查看权重配置
   console.log('Current weights:', weights);
   ```

3. **数据库查询**
   ```sql
   -- 查看片段权重配置
   SELECT * FROM template_segment_weights 
   WHERE template_id = 'template_id' AND track_segment_id = 'segment_id';
   ```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础权重配置功能
- 权重继承机制
- 批量操作支持
- 可视化管理界面

### 未来计划

1. **权重模板**
   - 预定义权重配置模板
   - 快速应用常用配置

2. **智能推荐**
   - 基于历史数据推荐权重配置
   - 自动优化权重设置

3. **高级分析**
   - 权重配置效果分析
   - 匹配成功率统计

## 相关文档

- [AI分类管理](./ai-classification-management.md)
- [模板匹配算法](./template-matching-algorithm.md)
- [素材管理系统](./material-management-system.md)
- [开发规范](../development/coding-standards.md)
