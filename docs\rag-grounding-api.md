# RAG Grounding API 文档

## 概述

RAG Grounding 服务基于 Google Vertex AI Search 和 Gemini 模型，提供检索增强生成（Retrieval-Augmented Generation）功能。该服务参考了 `promptx/outfit-match` 项目中的 `query_llm_with_grounding` 实现，遵循 Tauri 桌面应用开发规范。

## 核心特性

- **检索增强生成**：基于数据存储的智能检索和内容生成
- **Cloudflare Gateway 集成**：通过 Cloudflare Gateway 访问 Google Vertex AI
- **容错机制**：内置重试机制和错误处理
- **类型安全**：完整的 TypeScript 类型定义
- **性能监控**：响应时间统计和性能指标

## API 接口

### 1. 查询 RAG Grounding

**命令**: `query_rag_grounding`

**请求参数**:
```typescript
interface RagGroundingRequest {
  user_input: string;           // 用户查询内容
  config?: RagGroundingConfig;  // 可选配置
  session_id?: string;          // 会话ID
}
```

**响应数据**:
```typescript
interface RagGroundingResponse {
  answer: string;                        // AI 生成的回答
  grounding_metadata?: GroundingMetadata; // Grounding 元数据
  response_time_ms: number;              // 响应时间（毫秒）
  model_used: string;                    // 使用的模型
}
```

**使用示例**:
```typescript
import { queryRagGrounding } from '../services/ragGroundingService';

const result = await queryRagGrounding("如何搭配牛仔裤？", {
  sessionId: "user-session-123",
  customConfig: {
    temperature: 0.7,
    max_output_tokens: 4096
  }
});

if (result.success) {
  console.log("回答:", result.data?.answer);
  console.log("来源:", result.data?.grounding_metadata?.sources);
} else {
  console.error("查询失败:", result.error);
}
```

### 2. 测试连接

**命令**: `test_rag_grounding_connection`

**响应数据**: `string` - 连接测试结果

**使用示例**:
```typescript
import { testRagGroundingConnection } from '../services/ragGroundingService';

const status = await testRagGroundingConnection();
console.log("服务状态:", status.available ? "可用" : "不可用");
console.log("测试结果:", status.connectionTest);
```

### 3. 获取配置信息

**命令**: `get_rag_grounding_config`

**响应数据**:
```typescript
interface RagGroundingConfigInfo {
  base_url: string;
  model_name: string;
  timeout: number;
  max_retries: number;
  retry_delay: number;
  temperature: number;
  max_tokens: number;
  cloudflare_project_id: string;
  cloudflare_gateway_id: string;
  google_project_id: string;
  regions: string[];
}
```

## 配置选项

### RagGroundingConfig

```typescript
interface RagGroundingConfig {
  project_id: string;          // Google Cloud 项目ID
  location: string;            // 数据存储位置
  data_store_id: string;       // 数据存储ID
  model_id: string;            // 模型ID
  temperature: number;         // 生成温度 (0.0-2.0)
  max_output_tokens: number;   // 最大输出令牌数
  system_prompt?: string;      // 系统提示词
}
```

### 默认配置

```typescript
const DEFAULT_CONFIG = {
  project_id: "gen-lang-client-0413414134",
  location: "global",
  data_store_id: "jeans_pattern_data_store", // 使用存在的数据存储
  model_id: "gemini-2.5-flash",
  temperature: 1.0,
  max_output_tokens: 8192,
};
```

## Grounding 元数据

### GroundingMetadata

```typescript
interface GroundingMetadata {
  sources: GroundingSource[];    // 检索来源
  search_queries: string[];      // 搜索查询
}
```

### GroundingSource

```typescript
interface GroundingSource {
  title: string;              // 来源标题
  uri?: string;               // 来源URI
  snippet: string;            // 内容片段
  relevance_score?: number;   // 相关性评分 (0.0-1.0)
}
```

## 错误处理

### 错误类型

```typescript
enum RagGroundingErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',           // 网络错误
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR', // 认证错误
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',   // 配置错误
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',           // 超时错误
  PARSING_ERROR = 'PARSING_ERROR',           // 解析错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',           // 未知错误
}
```

### 错误处理示例

```typescript
try {
  const result = await queryRagGrounding("测试查询");
  if (!result.success) {
    switch (result.error) {
      case 'NETWORK_ERROR':
        console.error("网络连接失败，请检查网络设置");
        break;
      case 'AUTHENTICATION_ERROR':
        console.error("认证失败，请检查API密钥");
        break;
      default:
        console.error("查询失败:", result.error);
    }
  }
} catch (error) {
  console.error("系统错误:", error);
}
```

## 性能监控

### 统计信息

```typescript
interface RagGroundingStats {
  totalQueries: number;         // 总查询次数
  successfulQueries: number;    // 成功查询次数
  failedQueries: number;        // 失败查询次数
  averageResponseTime: number;  // 平均响应时间
  fastestResponseTime: number;  // 最快响应时间
  slowestResponseTime: number;  // 最慢响应时间
  lastQueryTime?: Date;         // 最后查询时间
}
```

### 获取统计信息

```typescript
import { getRagGroundingStats } from '../services/ragGroundingService';

const stats = getRagGroundingStats();
console.log(`成功率: ${(stats.successfulQueries / stats.totalQueries * 100).toFixed(2)}%`);
console.log(`平均响应时间: ${stats.averageResponseTime.toFixed(0)}ms`);
```

## 最佳实践

### 1. 查询优化

- 使用清晰、具体的查询语句
- 适当设置温度参数（创意性 vs 准确性）
- 合理设置最大输出令牌数

### 2. 会话管理

- 为相关查询使用相同的 session_id
- 定期清理过期会话

### 3. 错误处理

- 实现重试机制
- 提供用户友好的错误信息
- 记录详细的错误日志

### 4. 性能优化

- 监控响应时间
- 缓存常见查询结果
- 使用适当的超时设置

## 开发规范

本 RAG Grounding 服务严格遵循 `promptx/tauri-desktop-app-expert` 中定义的开发规范：

- **安全第一**：最小权限原则，数据加密保护
- **性能优先**：异步处理，响应时间优化
- **类型安全**：完整的 TypeScript 类型定义
- **模块化设计**：清晰的架构分层
- **错误处理完善**：全面的错误处理和用户反馈

## 技术架构

```
Frontend (TypeScript)
    ↓
Tauri Commands
    ↓
Rust Backend (GeminiService)
    ↓
Cloudflare Gateway
    ↓
Google Vertex AI Search + Gemini
```

## 依赖项

- **Rust**: anyhow, serde, tokio, reqwest
- **TypeScript**: @tauri-apps/api
- **Google Cloud**: Vertex AI Search, Gemini API
- **Cloudflare**: Gateway 服务
