# 视频生成功能模块

## 功能概述

视频生成功能模块是一个基于AI的视频创作工具，允许用户通过选择不同类型的素材来生成个性化视频内容。该模块采用现代化的UI/UX设计，提供直观的操作流程和优雅的用户体验。

## 主要功能

### 1. 素材中心 (Material Center)
- **多类型素材管理**：支持6种素材类型
  - 模特 (👤)：模特照片和视频素材
  - 产品 (📦)：产品展示素材
  - 场景 (🏞️)：背景场景素材
  - 动作 (🎭)：动作和姿态素材
  - 音乐 (🎵)：背景音乐和音效
  - 提示词模板 (📝)：AI生成提示词模板

- **智能搜索与筛选**
  - 实时搜索：支持按名称、描述、标签搜索
  - 分类筛选：快速筛选特定类型素材
  - 多视图模式：网格视图和列表视图切换

- **素材管理功能**
  - 添加新素材：支持文件上传和信息编辑
  - 预览功能：快速预览素材内容
  - 编辑和删除：完整的CRUD操作

### 2. 视频生成工作台 (Video Generation)
- **专业布局设计**
  - **左侧素材区**：Tab切换（模特/产品/场景/动作/音乐/提示词模板）
  - **中央预览区**：实时视频预览、播放控制、素材概览
  - **底部参数区**：紧凑型配置面板，一行显示所有关键参数
  - **右侧任务区**：可折叠的任务状态面板，实时显示生成进度

- **高级配置选项**
  - 输出格式：MP4、MOV、AVI
  - 分辨率：720p、1080p、4K
  - 帧率：24fps、30fps、60fps
  - 质量设置：低、中、高三档
  - 音频控制：启用/禁用音频
  - 特效选项：淡入淡出、缩放、转场等
  - 高级选项：可展开的详细配置

- **实时预览与任务管理**
  - 专业视频预览窗口（播放控制、进度条、全屏支持）
  - 选中素材实时显示
  - 生成统计信息
  - 任务队列管理（进度显示、取消、重试、删除）
  - 离线任务状态监控

## 技术特性

### UI/UX 设计
- **遵循设计系统**：基于 `promptx/frontend-developer` 标准
- **响应式设计**：支持桌面端和移动端
- **优雅动画**：流畅的过渡效果和交互反馈
- **无障碍支持**：键盘导航和屏幕阅读器友好

### 性能优化
- **懒加载**：素材缩略图按需加载
- **虚拟滚动**：大量素材列表性能优化
- **内存管理**：高效的组件渲染和状态管理
- **缓存策略**：智能缓存减少重复请求

### 代码架构
- **TypeScript**：完整的类型定义和接口
- **组件化设计**：可复用的UI组件
- **状态管理**：React Hooks 和 Context
- **错误处理**：完善的错误边界和用户反馈

## 文件结构

```
apps/desktop/src/
├── pages/
│   ├── MaterialCenter.tsx          # 素材中心主页面
│   └── VideoGeneration.tsx         # 视频生成工作台（重新设计）
├── components/video-generation/
│   ├── MaterialAssetCard.tsx       # 素材卡片组件
│   ├── MaterialCategoryFilter.tsx  # 分类过滤器
│   ├── MaterialSelector.tsx        # 素材选择器
│   ├── VideoConfigPanel.tsx        # 完整视频配置面板
│   ├── CompactVideoConfigPanel.tsx # 紧凑型配置面板（新增）
│   ├── VideoPreview.tsx           # 原视频预览组件
│   ├── CentralVideoPreview.tsx    # 中央预览组件（新增）
│   ├── TaskStatusPanel.tsx        # 任务状态面板（新增）
│   └── CreateMaterialAssetModal.tsx # 创建素材模态框
└── types/
    └── videoGeneration.ts          # 扩展的类型定义
```

## 使用指南

### 访问功能
1. 在导航栏中点击 "素材中心" 或 "视频生成"
2. 或直接访问路由：
   - `/material-center` - 素材中心
   - `/video-generation` - 视频生成工作台

### 素材管理流程
1. **添加素材**
   - 点击 "添加素材" 按钮
   - 选择文件或输入文本内容
   - 填写素材信息（名称、分类、描述、标签）
   - 保存素材

2. **管理素材**
   - 使用搜索框快速查找
   - 通过分类筛选器过滤
   - 切换网格/列表视图
   - 预览、编辑或删除素材

### 视频生成流程
1. **选择素材**
   - 在各个分类中选择所需素材
   - 支持多选和预览
   - 查看已选择的素材数量

2. **配置参数**
   - 设置输出格式和分辨率
   - 调整帧率和质量
   - 启用/禁用音频
   - 添加视频特效

3. **生成预览**
   - 查看视频预览
   - 确认配置信息
   - 点击生成按钮开始处理

## 开发说明

### 环境要求
- Node.js 18+
- pnpm 包管理器
- Tauri 开发环境

### 本地开发
```bash
# 安装依赖
pnpm install

# 启动开发服务器
cd apps/desktop
pnpm dev
```

### 扩展功能
- 添加新的素材类型：修改 `MaterialCategory` 枚举和配置
- 自定义视频特效：扩展 `VideoEffect` 接口
- 集成AI服务：实现实际的视频生成API调用

## 注意事项

1. **当前状态**：这是一个演示/预览版本，主要展示UI和交互流程
2. **后端集成**：需要实际的视频生成服务来完成完整功能
3. **文件存储**：需要配置文件上传和存储服务
4. **性能考虑**：大量素材时建议实现分页和虚拟滚动

## 未来规划

- [ ] 集成真实的AI视频生成服务
- [ ] 实现云端素材存储
- [ ] 添加协作功能
- [ ] 支持更多视频格式和特效
- [ ] 移动端适配优化
- [ ] 批量操作功能
- [ ] 素材版权管理
- [ ] 使用统计和分析

---

该功能模块为视频创作提供了完整的工作流程，从素材管理到视频生成，为用户提供了专业而易用的创作工具。
