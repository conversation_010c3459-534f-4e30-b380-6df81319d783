# 批量水印处理工具 - 用户使用指南

## 功能概述

批量水印处理工具是 MixVideo Desktop 的核心功能之一，帮助您快速处理视频和图片中的水印。支持三大核心功能：

- **🔍 水印检测**: 智能识别视频/图片中的水印位置和类型
- **🗑️ 水印移除**: 使用多种算法移除不需要的水印
- **➕ 水印添加**: 为内容添加自定义水印保护版权

## 快速开始

### 1. 打开水印工具

1. 在项目详情页面选择需要处理的素材
2. 点击工具栏中的"水印处理"按钮
3. 水印处理对话框将会打开

### 2. 选择处理模式

工具提供三个标签页，对应不同的处理模式：

- **检测水印**: 分析素材中的水印
- **移除水印**: 去除现有水印
- **添加水印**: 添加新的水印

## 详细功能说明

### 🔍 水印检测

#### 功能说明
水印检测功能可以自动识别视频或图片中的水印，并提供详细的检测报告。

#### 配置选项

**相似度阈值** (0.1 - 1.0)
- 控制检测的敏感度
- 数值越高，检测越严格
- 建议值：0.8

**帧采样率** (1 - 60)
- 对于视频，每隔多少帧检测一次
- 数值越小，检测越精确，但处理时间更长
- 建议值：30

**检测方法**
- ✅ **模板匹配**: 基于已知水印模板进行匹配
- ✅ **边缘检测**: 检测重复出现的边缘特征
- ✅ **频域分析**: 分析图像频域中的周期性模式
- ✅ **透明度检测**: 识别半透明叠加层

#### 使用步骤

1. 选择"检测水印"标签页
2. 调整检测参数（使用默认值即可）
3. 点击"开始检测水印"按钮
4. 等待检测完成，查看结果报告

#### 检测结果

检测完成后，您将看到：
- 检测到的水印数量
- 每个水印的位置和置信度
- 水印类型（如果能识别）
- 处理时间统计

### 🗑️ 水印移除

#### 功能说明
水印移除功能提供多种算法来去除不需要的水印，保持视频/图片的整体质量。

#### 移除方法

**AI修复** (推荐)
- 使用人工智能算法智能填充水印区域
- 效果最佳，但处理时间较长
- 适用于复杂背景的水印

**模糊处理**
- 对水印区域进行模糊处理
- 处理速度快，适用于简单场景
- 可调节模糊半径 (1-20)

**裁剪移除**
- 通过裁剪去除边缘水印
- 会改变视频/图片尺寸
- 可设置裁剪边距 (像素)

**遮罩覆盖**
- 使用纯色或图案覆盖水印
- 适用于固定位置的水印
- 处理速度最快

**内容感知填充**
- 分析周围内容智能填充
- 效果自然，适用于纹理背景

**克隆修复**
- 使用周围相似区域修复水印
- 适用于重复纹理背景

#### 质量级别

- **低质量（快速）**: 优先处理速度
- **中等质量**: 平衡质量和速度
- **高质量**: 优先输出质量
- **无损质量**: 最高质量，处理时间最长

#### 使用步骤

1. 选择"移除水印"标签页
2. 选择移除方法和质量级别
3. 如果选择模糊处理，调整模糊半径
4. 点击"开始移除水印"按钮
5. 等待处理完成

### ➕ 水印添加

#### 功能说明
为您的内容添加自定义水印，保护版权或添加品牌标识。

#### 水印模板管理

**上传新模板**
1. 点击"上传新模板"按钮
2. 选择水印文件（支持 PNG、JPG、SVG、GIF）
3. 填写模板信息：
   - 模板名称
   - 分类（Logo、版权、签名、装饰、自定义）
   - 描述和标签
4. 确认上传

**模板分类**
- **Logo**: 公司或品牌标识
- **版权**: 版权声明文字或图标
- **签名**: 个人签名或标识
- **装饰**: 装饰性图案
- **自定义**: 其他类型水印

#### 水印配置

**位置设置**
- 9个预设位置：左上、顶部居中、右上、左侧居中、居中、右侧居中、左下、底部居中、右下
- 自定义位置：手动指定坐标
- 动态位置：智能避开人脸和文字

**外观设置**
- **透明度** (10% - 100%): 控制水印的透明程度
- **缩放比例** (0.1x - 3.0x): 调整水印大小
- **旋转角度** (-180° - 180°): 旋转水印角度

**高级设置**
- **混合模式**: 正常、叠加、柔光等
- **动画效果**: 淡入、滑入、旋转等（仅视频）
- **质量级别**: 控制输出质量

#### 使用步骤

1. 选择"添加水印"标签页
2. 从模板库中选择水印模板
3. 调整位置、透明度、缩放等参数
4. 预览效果（如果支持）
5. 点击"开始添加水印"按钮
6. 等待处理完成

## 批量处理

### 处理进度

当启动批量处理任务后，您可以看到：

- **总体进度**: 显示处理百分比
- **处理统计**: 已处理/总数量/失败数量
- **当前项目**: 正在处理的素材名称
- **预估时间**: 剩余处理时间
- **错误信息**: 如果有处理失败的项目

### 任务管理

- **暂停/继续**: 可以暂停正在进行的任务
- **取消任务**: 停止处理并清理临时文件
- **查看详情**: 查看每个素材的处理结果

## 最佳实践

### 检测水印

1. **首次使用建议使用默认参数**，根据结果调整
2. **对于低质量视频**，适当降低相似度阈值
3. **对于大文件**，可以增加帧采样率以提高速度
4. **组合使用多种检测方法**以提高准确率

### 移除水印

1. **优先尝试AI修复**，效果通常最好
2. **对于边缘水印**，考虑使用裁剪移除
3. **对于简单背景**，模糊处理效果不错且速度快
4. **处理重要内容时选择高质量级别**

### 添加水印

1. **选择合适的位置**，避免遮挡重要内容
2. **调整透明度**，既要保护版权又不影响观看
3. **对于不同类型的内容使用不同的水印**
4. **定期备份水印模板**

## 常见问题

### Q: 检测不到水印怎么办？
A: 尝试以下方法：
- 降低相似度阈值
- 增加检测方法
- 检查水印是否在检测区域内
- 对于视频，减少帧采样率

### Q: 移除水印后效果不理想？
A: 可以尝试：
- 更换移除方法
- 提高质量级别
- 手动指定水印区域
- 使用AI修复方法

### Q: 添加的水印位置不合适？
A: 建议：
- 使用动态位置避开重要内容
- 手动调整位置坐标
- 降低透明度减少干扰
- 选择合适的混合模式

### Q: 处理速度太慢？
A: 优化建议：
- 选择较低的质量级别
- 增加帧采样率（检测时）
- 减少同时处理的文件数量
- 关闭不必要的检测方法

### Q: 批量处理中断了怎么办？
A: 处理方法：
- 重新启动任务，已处理的文件会被跳过
- 检查错误日志找出问题原因
- 单独处理失败的文件
- 确保有足够的磁盘空间

## 技术限制

### 支持的文件格式

**视频格式**
- MP4, AVI, MOV, MKV, WMV, FLV

**图片格式**
- JPG, PNG, BMP, TIFF, WebP

**水印格式**
- PNG (推荐，支持透明)
- JPG, BMP, TIFF
- SVG (矢量图)
- GIF (动态水印)

### 性能要求

- **内存**: 建议 8GB 以上
- **存储**: 处理过程中需要额外存储空间
- **CPU**: 多核处理器可提高批量处理速度

### 注意事项

1. **备份原文件**: 处理前请备份重要文件
2. **版权合规**: 确保有权处理相关内容
3. **质量损失**: 某些处理可能导致轻微质量损失
4. **处理时间**: 高质量处理需要更多时间

## 获取帮助

如果您在使用过程中遇到问题：

1. 查看本用户指南的常见问题部分
2. 检查应用程序的日志文件
3. 联系技术支持团队
4. 访问在线帮助文档

---

*本指南会随着功能更新而持续完善，建议定期查看最新版本。*
