/**
 * RAG Grounding 服务使用示例
 * 
 * 本文件展示了如何在前端应用中使用 RAG Grounding 服务
 * 包括基本查询、配置管理、错误处理和性能监控等功能
 */

import {
  queryRagGrounding,
  testRagGroundingConnection,
  getRagGroundingConfig,
  getRagGroundingStats,
  ragGroundingService,
} from '../apps/desktop/src/services/ragGroundingService';

import {
  RagGroundingQueryOptions,
  RagGroundingConfig,
  DEFAULT_RAG_GROUNDING_CONFIG,
} from '../apps/desktop/src/types/ragGrounding';

/**
 * 示例 1: 基本查询
 */
async function basicQueryExample() {
  console.log('=== 基本查询示例 ===');
  
  try {
    const result = await queryRagGrounding("如何搭配牛仔裤？");
    
    if (result.success && result.data) {
      console.log('查询成功!');
      console.log('回答:', result.data.answer);
      console.log('响应时间:', result.data.response_time_ms, 'ms');
      console.log('使用模型:', result.data.model_used);
      
      // 显示 Grounding 来源
      if (result.data.grounding_metadata?.sources) {
        console.log('参考来源:');
        result.data.grounding_metadata.sources.forEach((source, index) => {
          console.log(`  ${index + 1}. ${source.title}`);
        });
      }
    } else {
      console.error('查询失败:', result.error);
    }
  } catch (error) {
    console.error('系统错误:', error);
  }
}

/**
 * 示例 2: 带配置的查询
 */
async function configuredQueryExample() {
  console.log('=== 配置查询示例 ===');
  
  const customConfig: Partial<RagGroundingConfig> = {
    temperature: 0.7,        // 降低温度以获得更准确的回答
    max_output_tokens: 4096, // 限制输出长度
    system_prompt: "你是一个专业的服装搭配顾问，请提供实用的搭配建议。",
  };

  const options: RagGroundingQueryOptions = {
    sessionId: "fashion-consultation-session",
    customConfig,
    includeMetadata: true,
  };

  try {
    const result = await queryRagGrounding(
      "我有一条深蓝色牛仔裤，应该搭配什么颜色的上衣？",
      options
    );

    if (result.success && result.data) {
      console.log('专业搭配建议:', result.data.answer);
      console.log('查询耗时:', result.totalTime, 'ms');
    }
  } catch (error) {
    console.error('配置查询失败:', error);
  }
}

/**
 * 示例 3: 会话式对话
 */
async function conversationalExample() {
  console.log('=== 会话式对话示例 ===');
  
  const sessionId = `conversation-${Date.now()}`;
  
  const questions = [
    "我想了解春季服装搭配的基本原则",
    "那么对于职场女性，有什么特别的建议吗？",
    "如果是参加正式晚宴，应该如何选择服装？"
  ];

  for (let i = 0; i < questions.length; i++) {
    console.log(`\n问题 ${i + 1}: ${questions[i]}`);
    
    try {
      const result = await queryRagGrounding(questions[i], {
        sessionId,
        customConfig: {
          temperature: 0.8,
          system_prompt: "你是一个时尚顾问，请基于之前的对话上下文回答问题。"
        }
      });

      if (result.success && result.data) {
        console.log(`回答 ${i + 1}:`, result.data.answer.substring(0, 200) + '...');
      }
    } catch (error) {
      console.error(`问题 ${i + 1} 查询失败:`, error);
    }
    
    // 模拟用户思考时间
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

/**
 * 示例 4: 服务状态检查
 */
async function serviceStatusExample() {
  console.log('=== 服务状态检查示例 ===');
  
  try {
    // 测试连接
    const status = await testRagGroundingConnection();
    console.log('服务状态:', status.available ? '✅ 可用' : '❌ 不可用');
    console.log('最后检查时间:', status.lastChecked.toLocaleString());
    
    if (status.connectionTest) {
      console.log('连接测试结果:', status.connectionTest);
    }

    // 获取配置信息
    const config = await getRagGroundingConfig();
    console.log('服务配置:');
    console.log('  基础URL:', config.base_url);
    console.log('  模型名称:', config.model_name);
    console.log('  超时时间:', config.timeout, '秒');
    console.log('  最大重试次数:', config.max_retries);
    console.log('  支持区域:', config.regions.join(', '));

  } catch (error) {
    console.error('状态检查失败:', error);
  }
}

/**
 * 示例 5: 性能监控
 */
async function performanceMonitoringExample() {
  console.log('=== 性能监控示例 ===');
  
  // 执行一些查询以生成统计数据
  const testQueries = [
    "什么是时尚？",
    "如何选择适合的颜色？",
    "职场着装有什么要求？"
  ];

  console.log('执行测试查询...');
  for (const query of testQueries) {
    try {
      await queryRagGrounding(query);
    } catch (error) {
      console.error('查询失败:', error);
    }
  }

  // 获取统计信息
  const stats = getRagGroundingStats();
  console.log('\n性能统计:');
  console.log('  总查询次数:', stats.totalQueries);
  console.log('  成功查询次数:', stats.successfulQueries);
  console.log('  失败查询次数:', stats.failedQueries);
  console.log('  成功率:', `${(stats.successfulQueries / stats.totalQueries * 100).toFixed(2)}%`);
  console.log('  平均响应时间:', `${stats.averageResponseTime.toFixed(0)}ms`);
  console.log('  最快响应时间:', `${stats.fastestResponseTime}ms`);
  console.log('  最慢响应时间:', `${stats.slowestResponseTime}ms`);
  
  if (stats.lastQueryTime) {
    console.log('  最后查询时间:', stats.lastQueryTime.toLocaleString());
  }
}

/**
 * 示例 6: 错误处理和重试
 */
async function errorHandlingExample() {
  console.log('=== 错误处理示例 ===');
  
  // 模拟可能失败的查询
  const problematicQuery = ""; // 空查询可能导致错误

  try {
    const result = await queryRagGrounding(problematicQuery);
    
    if (!result.success) {
      console.log('查询失败，错误信息:', result.error);
      
      // 根据错误类型进行不同处理
      if (result.error?.includes('网络')) {
        console.log('检测到网络错误，建议检查网络连接');
      } else if (result.error?.includes('认证')) {
        console.log('检测到认证错误，建议检查API密钥');
      } else {
        console.log('其他错误，建议稍后重试');
      }
      
      // 实现简单的重试机制
      console.log('尝试重新查询...');
      const retryResult = await queryRagGrounding("什么是时尚搭配？");
      
      if (retryResult.success) {
        console.log('重试成功!');
      } else {
        console.log('重试仍然失败:', retryResult.error);
      }
    }
  } catch (error) {
    console.error('系统级错误:', error);
  }
}

/**
 * 示例 7: 批量查询处理
 */
async function batchQueryExample() {
  console.log('=== 批量查询示例 ===');
  
  const queries = [
    "春季流行色彩有哪些？",
    "如何搭配小白鞋？",
    "商务休闲风格的特点是什么？",
    "如何选择适合的包包？"
  ];

  console.log(`开始处理 ${queries.length} 个查询...`);
  
  const results = await Promise.allSettled(
    queries.map(query => queryRagGrounding(query))
  );

  results.forEach((result, index) => {
    console.log(`\n查询 ${index + 1}: ${queries[index]}`);
    
    if (result.status === 'fulfilled' && result.value.success) {
      const data = result.value.data!;
      console.log('✅ 成功');
      console.log('回答:', data.answer.substring(0, 100) + '...');
      console.log('响应时间:', data.response_time_ms, 'ms');
    } else {
      console.log('❌ 失败');
      if (result.status === 'fulfilled') {
        console.log('错误:', result.value.error);
      } else {
        console.log('异常:', result.reason);
      }
    }
  });
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('🚀 RAG Grounding 服务使用示例\n');
  
  try {
    await basicQueryExample();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await configuredQueryExample();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await conversationalExample();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await serviceStatusExample();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await performanceMonitoringExample();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await errorHandlingExample();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await batchQueryExample();
    
    console.log('\n✅ 所有示例执行完成!');
    
  } catch (error) {
    console.error('示例执行过程中发生错误:', error);
  }
}

// 导出示例函数供外部调用
export {
  basicQueryExample,
  configuredQueryExample,
  conversationalExample,
  serviceStatusExample,
  performanceMonitoringExample,
  errorHandlingExample,
  batchQueryExample,
  runAllExamples,
};

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  runAllExamples();
}
