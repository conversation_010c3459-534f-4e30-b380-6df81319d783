{"openapi": "3.1.0", "info": {"title": "Text Video Agent Api", "description": "文本生成视频API服务", "version": "1.0.6"}, "paths": {"/api/prompt/default": {"get": {"tags": ["提示词预处理"], "summary": "获取示例提示词", "operationId": "get_sample_prompt_api_prompt_default_get", "parameters": [{"name": "task_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/prompt/health": {"get": {"tags": ["提示词预处理"], "summary": "健康检测", "operationId": "health_check_api_prompt_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/file/upload": {"post": {"tags": ["文件操作"], "summary": "上传文件到COS", "description": "上传文件到腾讯云COS\n\n- **file**: 要上传的文件\n\n返回上传后的文件URL", "operationId": "upload_file_api_file_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_api_file_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/api/file/upload/s3": {"post": {"tags": ["文件操作"], "summary": "上传文件到s3【推荐 cdn加速】", "operationId": "upload_s3_api_file_upload_s3_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_s3_api_file_upload_s3_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/file/health": {"get": {"tags": ["文件操作"], "summary": "健康检测", "operationId": "health_check_api_file_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/template/default": {"get": {"tags": ["视频模板管理"], "summary": "获取视频模板列表", "description": "获取视频模板列表，支持分页和按任务类型筛选\n\nArgs:\n    task_type: 任务类型，如果为None则返回所有模板\n    page: 页码，从1开始，默认1\n    page_size: 每页记录数，默认100，最大1000\n    \nReturns:\n    {\n        \"status\": True, \n        \"data\": [模板数据列表], \n        \"page\": 当前页码,\n        \"page_size\": 每页数量,\n        \"total\": 总记录数,\n        \"total_pages\": 总页数\n    }", "operationId": "query_video_template_v2_api_template_default_get", "parameters": [{"name": "task_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Type"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码，从1开始", "default": 1, "title": "Page"}, "description": "页码，从1开始"}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "每页数量", "default": 100, "title": "<PERSON>"}, "description": "每页数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/template/check/task_type": {"get": {"tags": ["视频模板管理"], "summary": "检查任务类型是否可用", "description": "检查指定的任务类型是否可用（未被占用）\n\nArgs:\n    task_type: 要检查的任务类型\n    \nReturns:\n    JSON: 包含任务类型可用性的响应", "operationId": "check_template_task_type__api_template_check_task_type_get", "parameters": [{"name": "task_type", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/template/create": {"post": {"tags": ["视频模板管理"], "summary": "创建新的视频模板", "description": "创建新的视频模板\n    \n    Args:\n        template_data: 模板数据，必须包含task_type字段，且该字段值必须唯一\n        样例数据:  {\n        \"prompt\": \"Cozy, earthy-style visual highlighting the natural essence of herbal detox tea for gut health. A ceramic tea cup with hand-painted floral designs contains steaming tea, its surface sprinkled with turmeric and cinnamon. The cup rests on a weathered outdoor stone surface, surrounded by wildflowers and fresh green leaves for a natural ambiance. A small clay bowl holds psyllium husk and senna leaves, showcasing the raw ingredients. The scene is enhanced with soft sunlight filtering through tree branches, creating dappled shadows. A few drops of water glisten on nearby stones, suggesting a dew-kissed morning. Shot with a Nikon DSLR, the image captures a warm, rustic tone, emphasizing the organic and earth-inspired elements of the tea ritual.\",\n        \"cover_url\": \"https://sucai-**********.cos.ap-shanghai.myqcloud.com/tk/image/upload_20250630_172120_6226.png\",\n        \"video_url\": \"https://sucai-**********.cos.ap-shanghai.myqcloud.com/tk/video/02175127558530600000000000000000000ffffac15900ce5c3cc.mp4\",\n        \"description\": \"茶艺展示\",\n        \"detailDescription\": \"茶艺仪式与草本排毒茶的自然精华展示\",\n        \"title_zh\": \"养生草本茶艺展示\",\n        \"title_en\": null,\n        \"description_zh\": \"茶艺展示\",\n        \"description_en\": null,\n        \"aspect_ratio\": \"9:16\",\n        \"engine\": \"mj\",\n        \"presetPrompts\": \"11111\n22222\",\n        \"task_type\": \"tea\"\n  }\n        \n    Returns:\n        JSON: 包含创建结果的响应", "operationId": "handler_create_video_template_api_template_create_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Template Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/template/update": {"put": {"tags": ["视频模板管理"], "summary": "更新视频模板", "description": "更新视频模板\n\nArgs:\n    template_data: 模板数据，必须包含id字段\n    \nReturns:\n    JSON: 包含更新结果的响应", "operationId": "handler_update_video_template_api_template_update_put", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Template Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/template/delete/{template_id}": {"delete": {"tags": ["视频模板管理"], "summary": "删除视频模板", "description": "删除视频模板（软删除）\n\nArgs:\n    template_id: 要删除的模板ID\n    \nReturns:\n    JSON: 包含删除结果的响应", "operationId": "handler_delete_video_template_api_template_delete__template_id__delete", "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/prompt/check": {"get": {"tags": ["Midjourney图片生成"], "summary": "🔥图片提示词预审", "operationId": "prompt_check_api_mj_prompt_check_get", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/sync/image": {"post": {"tags": ["Midjourney图片生成"], "summary": "同步生成图片接口", "description": "同步生成图片接口 - 提交任务并轮询结果\n\nArgs:\n    prompt (str): 图片生成提示词\n    img_file: 样貌参考 --oref\n    max_wait_time (int): 最大等待时间，默认120秒\n    poll_interval (int): 轮询间隔，默认2秒\n\nReturns:\n    dict: 包含status, msg, data的响应字典", "operationId": "generate_image_sync_api_mj_sync_image_post", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}, {"name": "max_wait_time", "in": "query", "required": false, "schema": {"type": "integer", "default": 120, "title": "<PERSON> Wait Time"}}, {"name": "poll_interval", "in": "query", "required": false, "schema": {"type": "integer", "default": 2, "title": "Poll Interval"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_image_sync_api_mj_sync_image_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/generate-image": {"post": {"tags": ["Midjourney图片生成"], "summary": "生成图片", "description": "生成图片接口", "operationId": "generate_image_api_api_mj_generate_image_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_image_api_api_mj_generate_image_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/sync/img/describe": {"post": {"tags": ["Midjourney图片生成"], "summary": "获取图像描述", "description": "获取图像描述接口", "operationId": "describe_image_api_api_mj_sync_img_describe_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_describe_image_api_api_mj_sync_img_describe_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/sync/file/img/describe": {"post": {"tags": ["Midjourney图片生成"], "summary": "通过文件获取生图的提示词", "operationId": "desc_img_by_file_api_mj_sync_file_img_describe_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_desc_img_by_file_api_mj_sync_file_img_describe_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/health": {"get": {"tags": ["Midjourney图片生成"], "summary": "健康检查", "description": "健康检查接口", "operationId": "health_check_api_mj_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/mj/async/generate/image": {"post": {"tags": ["Midjourney图片生成"], "summary": "异步提交生图任务", "operationId": "async_gen_image_api_mj_async_generate_image_post", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_async_gen_image_api_mj_async_generate_image_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/async/query/status": {"get": {"tags": ["Midjourney图片生成"], "summary": "异步查询任务状态", "operationId": "async_query_status_api_mj_async_query_status_get", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jm/generate-video": {"post": {"tags": ["极梦视频生成api"], "summary": "生成视频", "description": "生成视频接口", "operationId": "generate_video_api_api_jm_generate_video_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_generate_video_api_api_jm_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jm/sync/generate/video": {"post": {"tags": ["极梦视频生成api"], "summary": "同步:生成视频v2,支持通过图片文件生成视频", "operationId": "sync_generate_video_v2_api_jm_sync_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_generate_video_v2_api_jm_sync_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jm/async/generate/video": {"post": {"tags": ["极梦视频生成api"], "summary": "异步生成视频,提交任务", "operationId": "async_gen_video_api_jm_async_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_async_gen_video_api_jm_async_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jm/async/query/status": {"get": {"tags": ["极梦视频生成api"], "summary": "异步查询生成视频的任务状态", "operationId": "async_query_video_status_api_jm_async_query_status_get", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jm/health": {"get": {"tags": ["极梦视频生成api"], "summary": "健康检查", "description": "健康检查接口", "operationId": "health_check_api_jm_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/task/create/task": {"post": {"tags": ["任务管理api"], "summary": "新版异步提交任务）", "description": "异步提交任务到 Modal 进行处理（不阻塞，立即返回任务ID）\n\n- **task_type**: 任务类型 (tea/chop/lady/vlog)\n- **prompt**: 生成提示词\n- **img_url**: 可选的参考图片URL，如果提供会先进行图片描述\n- **ar**:  生成图片的长宽比 默认为9:16\n\n立即返回 Modal 任务ID，任务在后台异步执行", "operationId": "submit_task_v2_api_task_create_task_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task/create/task/v2": {"post": {"tags": ["任务管理api"], "summary": "新版本异步提交任务", "description": "异步提交任务到 Modal 进行处理（不阻塞，立即返回任务ID）\n\n   - **task_type**: 任务类型 (tea/chop/lady/vlog)\n   - **prompt**: 生成提示词\n   - **img_url**: 可选的参考图片URL，如果提供会先进行图片描述\n   - **ar**:  生成图片的长宽比 默认为9:16\n   立即返回 Modal 任务ID，任务在后台异步执行", "operationId": "submit_task_v3_api_task_create_task_v2_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task/status/{task_id}": {"get": {"tags": ["任务管理api"], "summary": "异步查询任务状态", "operationId": "get_task_status_v2_api_task_status__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/task/health": {"get": {"tags": ["任务管理api"], "summary": "健康检测", "operationId": "health_check_api_task_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/veo/submit": {"post": {"tags": ["veo视频生成"], "summary": "异步: 仅支持 text--> video", "description": "提交视频生成任务", "operationId": "submit_video_generation_api_veo_submit_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/api/veo/async/submit": {"post": {"tags": ["veo视频生成"], "summary": "异步:text-->video or text + img --> video", "operationId": "submit_iv_submit_api_veo_async_submit_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_submit_iv_submit_api_veo_async_submit_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/api/veo/sync/generate/video": {"post": {"tags": ["veo视频生成"], "summary": "同步: 生成视频【文本+图片 OR 文生成视频】", "operationId": "sync_img_2video_api_veo_sync_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_img_2video_api_veo_sync_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/api/veo/task/{task_id}": {"get": {"tags": ["veo视频生成"], "summary": "异步: 获取任务执行状态", "description": "异步获取任务运行结果", "operationId": "get_task_status_api_veo_task__task_id__get", "deprecated": true, "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/veo/health": {"get": {"tags": ["veo视频生成"], "summary": "健康检查", "description": "健康检查接口", "operationId": "health_check_api_veo_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "deprecated": true}}, "/api/mj/video/async/submit": {"post": {"tags": ["midjourney视频生成api"], "summary": "异步提交生成视频任务", "operationId": "submit_video_task_api_mj_video_async_submit_post", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}, {"name": "img_url", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_submit_video_task_api_mj_video_async_submit_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/video/async/task/status": {"post": {"tags": ["midjourney视频生成api"], "summary": "异步查询生成任务进度", "operationId": "async_query_task_status_api_mj_video_async_task_status_post", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/video/sync/gen": {"post": {"tags": ["midjourney视频生成api"], "summary": "同步生成视频视频", "operationId": "sync_submit_task_api_mj_video_sync_gen_post", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}, {"name": "img_url", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_submit_task_api_mj_video_sync_gen_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/mj/video/health": {"get": {"tags": ["midjourney视频生成api"], "summary": "健康检查", "operationId": "health_check_api_mj_video_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/tt/video/async/submit/task": {"post": {"tags": ["🔥midjourney视频生成接口"], "summary": "异步:提交任务,支持图片链接或者文件", "operationId": "async_submit_task_api_tt_video_async_submit_task_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_async_submit_task_api_tt_video_async_submit_task_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/api/tt/video/async/query/status": {"post": {"tags": ["🔥midjourney视频生成接口"], "summary": "异步查询任务状态", "operationId": "query_task_status_async_api_tt_video_async_query_status_post", "deprecated": true, "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tt/video/sync/generate/video": {"post": {"tags": ["🔥midjourney视频生成接口"], "summary": "同步生成视频【text+img-->video】", "operationId": "sync_gen_video_api_tt_video_sync_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_gen_video_api_tt_video_sync_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true}}, "/api/302/mj/async/generate/image": {"post": {"tags": ["🔥302ai Midjourney图片生成"], "summary": "异步提交生图任务", "operationId": "async_gen_image_api_302_mj_async_generate_image_post", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_async_gen_image_api_302_mj_async_generate_image_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/task/cancel": {"post": {"tags": ["🔥302ai Midjourney图片生成"], "summary": "取消任务", "operationId": "cancel_task_api_302_mj_task_cancel_post", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/async/query/status": {"get": {"tags": ["🔥302ai Midjourney图片生成"], "summary": "异步查询任务状态", "description": "Args:\n    task_id: 任务id\n    task_type: 任务类型：image【生图】, describe【反推提示词】\n\nReturns:", "operationId": "async_query_status_api_302_mj_async_query_status_get", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "task_type", "in": "query", "required": false, "schema": {"type": "string", "default": "image", "title": "Task Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/sync/img/describe": {"post": {"tags": ["🔥302ai Midjourney图片生成"], "summary": "获取图像描述", "operationId": "describe_image_api_api_302_mj_sync_img_describe_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_describe_image_api_api_302_mj_sync_img_describe_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/sync/file/img/describe": {"post": {"tags": ["🔥302ai Midjourney图片生成"], "summary": "通过文件获取生图的提示词", "operationId": "desc_img_by_file_api_302_mj_sync_file_img_describe_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_desc_img_by_file_api_302_mj_sync_file_img_describe_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/sync/image": {"post": {"tags": ["🔥302ai Midjourney图片生成"], "summary": "同步生成图片接口", "operationId": "generate_image_sync_api_302_mj_sync_image_post", "parameters": [{"name": "prompt", "in": "query", "required": true, "schema": {"type": "string", "title": "Prompt"}}, {"name": "max_wait_time", "in": "query", "required": false, "schema": {"type": "integer", "default": 500, "title": "<PERSON> Wait Time"}}, {"name": "poll_interval", "in": "query", "required": false, "schema": {"type": "integer", "default": 4, "title": "Poll Interval"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_generate_image_sync_api_302_mj_sync_image_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/jm/sync/generate/video": {"post": {"tags": ["🔥302ai 极梦视频生成api"], "summary": "【302ai】同步:生成视频v2,支持通过图片文件生成视频", "operationId": "sync_generate_video_v2_api_302_jm_sync_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_generate_video_v2_api_302_jm_sync_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/jm/async/generate/video": {"post": {"tags": ["🔥302ai 极梦视频生成api"], "summary": "异步生成视频,提交任务", "operationId": "async_gen_video_api_302_jm_async_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_async_gen_video_api_302_jm_async_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/jm/async/query/status": {"get": {"tags": ["🔥302ai 极梦视频生成api"], "summary": "异步查询生成视频的任务状态", "operationId": "async_query_video_status_api_302_jm_async_query_status_get", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/video/async/submit": {"post": {"tags": ["🔥302ai Midjourney视频生成接口"], "summary": "异步提交生成视频任务", "operationId": "submit_task_api_302_mj_video_async_submit_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_submit_task_api_302_mj_video_async_submit_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/video/async/task/status": {"post": {"tags": ["🔥302ai Midjourney视频生成接口"], "summary": "异步查询生成任务进度", "operationId": "async_query_task_status_api_302_mj_video_async_task_status_post", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/mj/video/sync/generate/video": {"post": {"tags": ["🔥302ai Midjourney视频生成接口"], "summary": "同步生成视频【text+img-->video】", "operationId": "sync_gen_video_api_302_mj_video_sync_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_gen_video_api_302_mj_video_sync_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/veo/video/async/submit": {"post": {"tags": ["🔥 302ai veo视频生成接口"], "summary": "异步:text-->video or text + img --> video", "operationId": "submit_iv_submit_api_302_veo_video_async_submit_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_submit_iv_submit_api_302_veo_video_async_submit_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/veo/video/sync/generate/video": {"post": {"tags": ["🔥 302ai veo视频生成接口"], "summary": "同步: 生成视频【文本+图片 OR 文生成视频】", "operationId": "sync_img_2video_api_302_veo_video_sync_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_img_2video_api_302_veo_video_sync_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/veo/video/task/{task_id}": {"get": {"tags": ["🔥 302ai veo视频生成接口"], "summary": "异步: 获取任务执行状态", "description": "Args:\n    task_id: 任务id\n\n    img_mode: True:图文到视频  False 文生视频\n\nReturns:", "operationId": "get_task_status_api_302_veo_video_task__task_id__get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "img_mode", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Img Mode"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/hl_router/sync/generate/speech": {"post": {"tags": ["海螺API"], "summary": "海螺同步生成音频", "operationId": "hl_tts_api_302_hl_router_sync_generate_speech_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_hl_tts_api_302_hl_router_sync_generate_speech_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/hl_router/sync/get/voices": {"get": {"tags": ["海螺API"], "summary": "查询克隆的音色ID, 【接口来自官方， 302没有对应的中转接口】", "operationId": "get_voices_hl_api_302_hl_router_sync_get_voices_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/302/hl_router/sync/file/upload": {"post": {"tags": ["海螺API"], "summary": "上传素材到302ai,用于复刻", "operationId": "upload_material_file_api_302_hl_router_sync_file_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_material_file_api_302_hl_router_sync_file_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/302/hl_router/sync/voice/clone": {"post": {"tags": ["海螺API"], "summary": "声音克隆", "operationId": "voice_clone_api_302_hl_router_sync_voice_clone_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_voice_clone_api_302_hl_router_sync_voice_clone_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/union/img/model/list": {"get": {"tags": ["🔥【聚合接口】图片生成"], "summary": "获取支持的模型列表", "operationId": "fetch_supported_model_api_union_img_model_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/union/img/sync/generate/image": {"post": {"tags": ["🔥【聚合接口】图片生成"], "summary": "生图", "description": "详细参考: https://doc.302.ai/286288228e0", "operationId": "sync_gen_image_api_union_img_sync_generate_image_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_sync_gen_image_api_union_img_sync_generate_image_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/union/video/model/list": {"get": {"tags": ["🔥【聚合接口】视频生成接口"], "summary": "获取支持的模型列表", "operationId": "fetch_supported_model_api_union_video_model_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/union/video/async/generate/video": {"post": {"tags": ["🔥【聚合接口】视频生成接口"], "summary": "异步提交任务", "operationId": "submit_task_api_union_video_async_generate_video_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_submit_task_api_union_video_async_generate_video_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/union/video/async/{task_id}/status": {"get": {"tags": ["🔥【聚合接口】视频生成接口"], "summary": "异步查询任务状态", "operationId": "query_task_status_api_union_video_async__task_id__status_get", "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/comfy/fetch/running/node": {"get": {"tags": ["comfyui工作流处理"], "summary": "根据任务数,获取运行节点", "operationId": "get_running_node_api_comfy_fetch_running_node_get", "parameters": [{"name": "task_count", "in": "query", "required": false, "schema": {"type": "integer", "description": "运行任务的数目", "default": 1, "title": "Task Count"}, "description": "运行任务的数目"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/comfy/async/submit/task": {"post": {"tags": ["comfyui工作流处理"], "summary": "异步提交任务", "operationId": "submit_task_api_comfy_async_submit_task_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_submit_task_api_comfy_async_submit_task_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/comfy/async/task/status": {"get": {"tags": ["comfyui工作流处理"], "summary": "查询任务状态", "operationId": "query_task_status_api_comfy_async_task_status_get", "parameters": [{"name": "task_id", "in": "query", "required": true, "schema": {"type": "string", "description": "任务id", "title": "Task Id"}, "description": "任务id"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/comfy/sync/execute/workflow": {"post": {"tags": ["comfyui工作流处理"], "summary": "同步执行comfy工作流", "operationId": "sync_execute_workflow_api_comfy_sync_execute_workflow_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_sync_execute_workflow_api_comfy_sync_execute_workflow_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Body_async_gen_image_api_302_mj_async_generate_image_post": {"properties": {"img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "样貌参考图片"}}, "type": "object", "title": "Body_async_gen_image_api_302_mj_async_generate_image_post"}, "Body_async_gen_image_api_mj_async_generate_image_post": {"properties": {"img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "样貌参考图片"}}, "type": "object", "title": "Body_async_gen_image_api_mj_async_generate_image_post"}, "Body_async_gen_video_api_302_jm_async_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "视频生成提示词"}, "img_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url", "description": "图片URL地址", "default": ""}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "上传的图片文件"}, "duration": {"type": "string", "title": "Duration", "description": "视频时长(秒)", "default": "5"}, "model_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Type", "description": "模型类型, 支持 lite pro", "default": "lite"}}, "type": "object", "required": ["prompt"], "title": "Body_async_gen_video_api_302_jm_async_generate_video_post"}, "Body_async_gen_video_api_jm_async_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "视频生成提示词"}, "img_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url", "description": "图片URL地址", "default": ""}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "上传的图片文件"}, "duration": {"type": "string", "title": "Duration", "description": "视频时长(秒)", "default": "5"}, "model_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Type", "description": "模型类型, 支持 lite pro", "default": "lite"}}, "type": "object", "required": ["prompt"], "title": "Body_async_gen_video_api_jm_async_generate_video_post"}, "Body_async_submit_task_api_tt_video_async_submit_task_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url", "description": "首帧参数图url"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图文件"}}, "type": "object", "required": ["prompt"], "title": "Body_async_submit_task_api_tt_video_async_submit_task_post"}, "Body_desc_img_by_file_api_302_mj_sync_file_img_describe_post": {"properties": {"img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "上传的图片"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 120}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 2}}, "type": "object", "required": ["img_file"], "title": "Body_desc_img_by_file_api_302_mj_sync_file_img_describe_post"}, "Body_desc_img_by_file_api_mj_sync_file_img_describe_post": {"properties": {"img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "上传的图片"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 120}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 2}}, "type": "object", "required": ["img_file"], "title": "Body_desc_img_by_file_api_mj_sync_file_img_describe_post"}, "Body_describe_image_api_api_302_mj_sync_img_describe_post": {"properties": {"image_url": {"type": "string", "title": "Image Url", "description": "图片URL地址"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 120}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 2}}, "type": "object", "required": ["image_url"], "title": "Body_describe_image_api_api_302_mj_sync_img_describe_post"}, "Body_describe_image_api_api_mj_sync_img_describe_post": {"properties": {"image_url": {"type": "string", "title": "Image Url", "description": "图片URL地址"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 120}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 2}}, "type": "object", "required": ["image_url"], "title": "Body_describe_image_api_api_mj_sync_img_describe_post"}, "Body_generate_image_api_api_mj_generate_image_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "图片生成提示词"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "样貌参考图片"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 120}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 2}}, "type": "object", "required": ["prompt"], "title": "Body_generate_image_api_api_mj_generate_image_post"}, "Body_generate_image_sync_api_302_mj_sync_image_post": {"properties": {"img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "样貌参考图片"}}, "type": "object", "title": "Body_generate_image_sync_api_302_mj_sync_image_post"}, "Body_generate_image_sync_api_mj_sync_image_post": {"properties": {"img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "样貌参考图片"}}, "type": "object", "title": "Body_generate_image_sync_api_mj_sync_image_post"}, "Body_generate_video_api_api_jm_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "视频生成提示词"}, "img_url": {"type": "string", "title": "Img Url", "description": "图片URL地址"}, "duration": {"type": "string", "title": "Duration", "description": "视频时长(秒)", "default": "5"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 300}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 5}, "model_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Type", "description": "模型类型, 支持 lite pro", "default": "lite"}}, "type": "object", "required": ["prompt", "img_url"], "title": "Body_generate_video_api_api_jm_generate_video_post"}, "Body_hl_tts_api_302_hl_router_sync_generate_speech_post": {"properties": {"text": {"type": "string", "title": "Text", "description": "tts文本内容"}, "voice_id": {"type": "string", "title": "Voice Id", "description": "Voice ID"}, "speed": {"type": "number", "title": "Speed", "description": "语速, [0.5, 2]", "default": 1.0}, "vol": {"type": "number", "title": "Vol", "description": "音量(0,10]默认1.0", "default": 1.0}, "emotion": {"type": "string", "title": "Emotion", "description": "情感, [\"happy\", \"sad\", \"angry\", \"fearful\", \"disgusted\", \"surprised\", \"calm\"]"}}, "type": "object", "required": ["text", "voice_id"], "title": "Body_hl_tts_api_302_hl_router_sync_generate_speech_post"}, "Body_submit_iv_submit_api_302_veo_video_async_submit_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图"}}, "type": "object", "required": ["prompt"], "title": "Body_submit_iv_submit_api_302_veo_video_async_submit_post"}, "Body_submit_iv_submit_api_veo_async_submit_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图"}}, "type": "object", "required": ["prompt", "img_file"], "title": "Body_submit_iv_submit_api_veo_async_submit_post"}, "Body_submit_task_api_302_mj_video_async_submit_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图文件"}}, "type": "object", "required": ["prompt"], "title": "Body_submit_task_api_302_mj_video_async_submit_post"}, "Body_submit_task_api_comfy_async_submit_task_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "工作流节点数据"}}, "type": "object", "required": ["prompt"], "title": "Body_submit_task_api_comfy_async_submit_task_post"}, "Body_submit_task_api_union_video_async_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生图的提示词"}, "img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "首帧图片"}, "model": {"type": "string", "title": "Model", "description": "生图的模型", "default": "seedance_i2v"}, "duration": {"type": "integer", "title": "Duration", "description": "视频时长", "default": 5}}, "type": "object", "required": ["prompt"], "title": "Body_submit_task_api_union_video_async_generate_video_post"}, "Body_submit_video_task_api_mj_video_async_submit_post": {"properties": {"img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File"}}, "type": "object", "title": "Body_submit_video_task_api_mj_video_async_submit_post"}, "Body_sync_execute_workflow_api_comfy_sync_execute_workflow_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "工作流json字符穿"}}, "type": "object", "required": ["prompt"], "title": "Body_sync_execute_workflow_api_comfy_sync_execute_workflow_post"}, "Body_sync_gen_image_api_union_img_sync_generate_image_post": {"properties": {"model": {"type": "string", "title": "Model", "description": "生图的模型默认.mj", "default": "midjourney-v7-t2i"}, "prompt": {"type": "string", "title": "Prompt", "description": "生图的提示词"}, "img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "参考图"}, "aspect_ratio": {"type": "string", "title": "Aspect Ratio", "description": "图片的尺寸", "default": "9:16"}}, "type": "object", "required": ["prompt"], "title": "Body_sync_gen_image_api_union_img_sync_generate_image_post"}, "Body_sync_gen_video_api_302_mj_video_sync_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "首帧参考图文件"}, "timeout": {"type": "integer", "title": "Timeout", "description": "超时时间", "default": 300}, "interval": {"type": "integer", "title": "Interval", "description": "轮询间隔", "default": 3}}, "type": "object", "required": ["prompt", "img_file"], "title": "Body_sync_gen_video_api_302_mj_video_sync_generate_video_post"}, "Body_sync_gen_video_api_tt_video_sync_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url", "description": "首帧参数图url"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图文件"}, "timeout": {"type": "integer", "title": "Timeout", "description": "超时时间", "default": 300}, "interval": {"type": "integer", "title": "Interval", "description": "轮询间隔", "default": 3}}, "type": "object", "required": ["prompt"], "title": "Body_sync_gen_video_api_tt_video_sync_generate_video_post"}, "Body_sync_generate_video_v2_api_302_jm_sync_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "生成视频的图片"}, "duration": {"type": "string", "title": "Duration", "description": "视频时长(秒)", "default": "5"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 300}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 5}, "model_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Type", "description": "模型类型, 支持 lite pro", "default": "lite"}}, "type": "object", "required": ["prompt", "img_file"], "title": "Body_sync_generate_video_v2_api_302_jm_sync_generate_video_post"}, "Body_sync_generate_video_v2_api_jm_sync_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"type": "string", "format": "binary", "title": "Img File", "description": "生成视频的图片"}, "duration": {"type": "string", "title": "Duration", "description": "视频时长(秒)", "default": "5"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间(秒)", "default": 300}, "poll_interval": {"type": "integer", "title": "Poll Interval", "description": "轮询间隔(秒)", "default": 5}, "model_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Type", "description": "模型类型, 支持 lite pro", "default": "lite"}}, "type": "object", "required": ["prompt", "img_file"], "title": "Body_sync_generate_video_v2_api_jm_sync_generate_video_post"}, "Body_sync_img_2video_api_302_veo_video_sync_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图,可选"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间,单位秒", "default": 500}, "interval": {"type": "integer", "title": "Interval", "description": "轮询间隔,单位秒", "default": 5}}, "type": "object", "required": ["prompt"], "title": "Body_sync_img_2video_api_302_veo_video_sync_generate_video_post"}, "Body_sync_img_2video_api_veo_sync_generate_video_post": {"properties": {"prompt": {"type": "string", "title": "Prompt", "description": "生成视频的提示词"}, "img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File", "description": "首帧参考图,可选"}, "max_wait_time": {"type": "integer", "title": "<PERSON> Wait Time", "description": "最大等待时间,单位秒", "default": 500}, "interval": {"type": "integer", "title": "Interval", "description": "轮询间隔,单位秒", "default": 5}}, "type": "object", "required": ["prompt", "img_file"], "title": "Body_sync_img_2video_api_veo_sync_generate_video_post"}, "Body_sync_submit_task_api_mj_video_sync_gen_post": {"properties": {"img_file": {"anyOf": [{"type": "string", "format": "binary"}, {"type": "null"}], "title": "Img File"}}, "type": "object", "title": "Body_sync_submit_task_api_mj_video_sync_gen_post"}, "Body_upload_file_api_file_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_api_file_upload_post"}, "Body_upload_material_file_api_302_hl_router_sync_file_upload_post": {"properties": {"audio_file": {"type": "string", "format": "binary", "title": "Audio File", "description": "音频文件"}, "purpose": {"type": "string", "title": "Purpose", "description": "意图,默认voice_clone", "default": "voice_clone"}}, "type": "object", "required": ["audio_file"], "title": "Body_upload_material_file_api_302_hl_router_sync_file_upload_post"}, "Body_upload_s3_api_file_upload_s3_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_s3_api_file_upload_s3_post"}, "Body_voice_clone_api_302_hl_router_sync_voice_clone_post": {"properties": {"text": {"type": "string", "title": "Text", "description": "复刻的文本"}, "model": {"type": "string", "title": "Model", "description": "支持的模型有:speech-02-hd,speech-02-turbo,speech-01-hd,speech-01-turbo", "default": "speech-02-hd"}, "need_noise_reduction": {"type": "boolean", "title": "Need Noise Reduction", "description": "是否开启降噪", "default": true}, "voice_id": {"type": "string", "title": "Voice Id", "description": "音色克隆voice_id"}, "prefix": {"type": "string", "title": "Prefix", "description": "音色voice_id前缀", "default": "BoWong-"}, "audio_file": {"type": "string", "format": "binary", "title": "Audio File", "description": "参考音频文件"}}, "type": "object", "required": ["text"], "title": "Body_voice_clone_api_302_hl_router_sync_voice_clone_post"}, "FileUploadResponse": {"properties": {"status": {"type": "boolean", "title": "Status", "description": "上传状态"}, "msg": {"type": "string", "title": "Msg", "description": "响应消息"}, "data": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Data", "description": "文件URL"}}, "type": "object", "required": ["status", "msg"], "title": "FileUploadResponse", "description": "文件上传响应模型"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "TaskRequest": {"properties": {"task_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Task Type", "description": "任务类型如: tea, chop, lady, vlog", "default": ""}, "prompt": {"type": "string", "title": "Prompt", "description": "生图的提示词"}, "img_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Img Url", "description": "参考图", "default": ""}, "ar": {"type": "string", "title": "Ar", "description": "生成图片,视频的分辨率", "default": "9:16"}}, "type": "object", "required": ["prompt"], "title": "TaskRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VideoRequest": {"properties": {"prompt": {"type": "string", "title": "Prompt"}}, "type": "object", "required": ["prompt"], "title": "VideoRequest"}}}}